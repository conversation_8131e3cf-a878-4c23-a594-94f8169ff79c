Eres un ingeniero full‑stack y DevOps experto. Configura un entorno completo con:

• Frontend: React 18 + TypeScript + Vite + Tailwind CSS  
• Backend: FastAPI + PostgreSQL + Redis + Vector DB (Pinecone)  
• IA: MLflow + Pinecone + Federated learning  
• Arquitectura: microservicios con Domain‑Driven Design  
• Orquestación: Kubernetes con autoscaling + sharding familiar  
• Monitoreo: Prometheus + Grafana + Jaeger

1. Prepara carpetas y archivos divididos (<300 líneas cada uno).  
2. <PERSON>rea Dockerfiles, docker-compose y manifests de Kubernetes.  
3. Conecta frontend ↔ backend (API funcional + UI mínima).  
4. Configura MLflow, Pinecone y aprendizaje federado.  
5. Implementa autoscaling y sharding en Kubernetes.  
6. Añade Prometheus, Grafana y Jaeger en el mismo entorno.  
7. Incluye backup automatizado, scripts de arranque y librerías.  
8. Genera un **tutorial de instalación completo** (README + manual).
9. Accesible at url project2
10. Download and install all dependencies 
  

----------------------------------- creo que este es bueno
Eres un ingeniero full-stack y DevOps senior.

Tienes dos proyectos:

• **Project1**: microservicios (FastAPI + PostgreSQL + Redis + Pinecone + MLflow + federated learning), frontend React18+TS+Vite+Tailwind, Kubernetes con autoscaling, Prometheus, Grafana, Jaeger.

• **Project2**: módulo independiente con su propio frontend/backend y dashboards.

Objetivo: unificar ambos proyectos en un solo entorno:

1. Elimina duplicados de configuración y **ignora variables default de env** (p. ej. `.env.example`), usando sólo ConfigMaps y Secrets bien definidos :contentReference[oaicite:1]{index=1}.  
2. Integra frontend: menú React con pestañas “Project1” y “Project2”, cada una con su UI.  
3. Integra backend: incluye API de P2 bajo `/p2/*`, conectada a la infraestructura existente (Postgres, Redis).  
4. Mantén dashboards separados: Dashboard‑P1 y Dashboard‑P2 en Grafana, organizados en carpetas o datasources distintos :contentReference[oaicite:2]{index=2}.  
5. Dockeriza todo, crea `Dockerfile`s y `docker-compose.yml`.  
6. Configura Kubernetes (manifests o Helm umbrella chart) con **namespaces `p1`, `p2`**, autoscaling y sharding.  
7. Implementa service mesh (Istio) para comunicación segura y observabilidad unificada :contentReference[oaicite:3]{index=3}.  
8. Configura Prometheus federado y Grafana central: scraping desde ambos namespaces, dashboards por carpeta :contentReference[oaicite:4]{index=4}.  
9. Configura Jaeger para trazabilidad distribuida entre P1 y P2.  
10. Modulariza el código (<300 líneas por archivo).  
11. Añade scripts de backup, CI/CD, `backup_schedule.py` y `restore_test.py`.  
12. Genera un README + tutorial de instalación (dev/prod).  
13. Empaqueta todo en zip funcional y proporciona URL de descarga.

Asegúrate que el entorno se despliegue sin errores con:

Prompt 2. ----------------------------


Eres un ingeniero full-stack y DevOps senior. Tienes dos proyectos:

• **Project1**: microservicios (FastAPI + PostgreSQL + Redis + Pinecone + MLflow + federated learning) front React18+TS+Vite+Tailwind, Kubernetes con autoscaling, Prometheus, Grafana, Jaeger.
• **Project2**: otro módulo independiente que quieres unir (frontend + backend + dashboards específicos).

Objetivo: fusionar ambos proyectos en un único entorno:

1. Conserva dashboards separados: Dashboard‑P1 y Dashboard‑P2.
2. Unifica frontend: añade en React un menú con pestañas “MIGRATED” y “Project2”, cada una cargando su UI.
3. Backend: expone endpoints P2 integrados en el FastAPI del entorno; rutas aisladas con API prefix `/p2/*`.
4. Asegura que P2 reuse la base de datos y Redis donde aplique, sin conflictos.
5. Dockeriza todo, genera Dockerfiles y `docker-compose.yml` o Helm umbrella chart.
6. Kubernetes manifests o Helm charts: dos deploys, con namespaces distintos para aislar recursos (`p1`, `p2`).
7. Implementa service mesh (Istio) para observabilidad y comunicación segura entre los microservicios.
8. Prometheus y Grafana configurados para mostrar los dos dashboards en la misma instancia, cada uno con su folder/datasource.
9. Jaeger y trazabilidad unificada cobrando spans de ambos contextos.
10. Modulariza el código (<300 líneas por archivo).
11. Añade backup, autoscaling, sharding en P1 y P2.
12. Crea scripts CI/CD, backup schedule y restore, con integración en el entorno.
13. Genera un README + tutorial de instalación completo para entornos dev/producción.
14. Empaqueta todo en un zip funcional y proporciona un URL para descarga.
15. Al final, asegúrate de que el comando “npm install && python setup && docker-compose up” (o `helm install`) despliegue ambos proyectos integrados, con dashboards separados bajo un único frontend/monitorización.


