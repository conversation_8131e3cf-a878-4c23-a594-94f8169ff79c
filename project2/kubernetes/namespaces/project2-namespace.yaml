# ============================================================================
# KUBERNETES NAMESPACE - Project2 Namespace
# ============================================================================

apiVersion: v1
kind: Namespace
metadata:
  name: project2
  labels:
    name: project2
    environment: production
    app: project2-platform
  annotations:
    description: "Project2 AI Microservices Platform"
    contact: "<EMAIL>"
    version: "1.0.0"

---
# Resource Quota for the namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: project2-quota
  namespace: project2
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"

---
# Network Policy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: project2-network-policy
  namespace: project2
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: project2
    - namespaceSelector:
        matchLabels:
          name: kube-system
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: project2
    - namespaceSelector:
        matchLabels:
          name: kube-system
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
