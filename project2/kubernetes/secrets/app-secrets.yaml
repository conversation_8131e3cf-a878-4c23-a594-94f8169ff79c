# ============================================================================
# KUBERNETES SECRETS - Application Secrets (Base64 Encoded)
# ============================================================================

apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: project2
  labels:
    app: project2-platform
    component: secrets
type: Opaque
data:
  # Database credentials (base64 encoded)
  # Original: **********************************************************/project2
  DATABASE_URL: ********************************************************************************************
  
  # Redis URL (base64 encoded)
  # Original: redis://redis:6379/0
  REDIS_URL: cmVkaXM6Ly9yZWRpczozNjM3OS8w
  
  # JWT secrets (base64 encoded)
  # Original: your-super-secret-key-here-at-least-32-characters-long-production
  SECRET_KEY: eW91ci1zdXBlci1zZWNyZXQta2V5LWhlcmUtYXQtbGVhc3QtMzItY2hhcmFjdGVycy1sb25nLXByb2R1Y3Rpb24=
  
  # Original: your-jwt-secret-key-here-at-least-32-characters-long-production
  JWT_SECRET_KEY: eW91ci1qd3Qtc2VjcmV0LWtleS1oZXJlLWF0LWxlYXN0LTMyLWNoYXJhY3RlcnMtbG9uZy1wcm9kdWN0aW9u
  
  # Celery broker (base64 encoded)
  # Original: redis://redis:6379/1
  CELERY_BROKER_URL: cmVkaXM6Ly9yZWRpczozNjM3OS8x
  
  # Original: redis://redis:6379/2
  CELERY_RESULT_BACKEND: cmVkaXM6Ly9yZWRpczozNjM3OS8y
  
  # MLflow tracking URI (base64 encoded)
  # Original: http://mlflow:5000
  MLFLOW_TRACKING_URI: aHR0cDovL21sZmxvdzo1MDAw
  
  # Email credentials (base64 encoded - replace with actual values)
  # Original: smtp.gmail.com
  SMTP_HOST: c210cC5nbWFpbC5jb20=
  
  # Original: <EMAIL>
  SMTP_USERNAME: ****************************
  
  # Original: your-app-password
  SMTP_PASSWORD: eW91ci1hcHAtcGFzc3dvcmQ=
  
  # Original: <EMAIL>
  EMAIL_FROM: ****************************

---
apiVersion: v1
kind: Secret
metadata:
  name: external-api-secrets
  namespace: project2
  labels:
    app: project2-platform
    component: external-apis
type: Opaque
data:
  # Pinecone API key (base64 encoded - replace with actual key)
  # Original: your-pinecone-api-key-here
  PINECONE_API_KEY: eW91ci1waW5lY29uZS1hcGkta2V5LWhlcmU=
  
  # Original: us-west1-gcp
  PINECONE_ENVIRONMENT: dXMtd2VzdDEtZ2Nw
  
  # OpenAI API key (base64 encoded - replace with actual key)
  # Original: your-openai-api-key-here
  OPENAI_API_KEY: eW91ci1vcGVuYWktYXBpLWtleS1oZXJl
  
  # Anthropic API key (base64 encoded - replace with actual key)
  # Original: your-anthropic-api-key-here
  ANTHROPIC_API_KEY: eW91ci1hbnRocm9waWMtYXBpLWtleS1oZXJl
  
  # HuggingFace API key (base64 encoded - replace with actual key)
  # Original: your-huggingface-api-key-here
  HUGGINGFACE_API_KEY: eW91ci1odWdnaW5nZmFjZS1hcGkta2V5LWhlcmU=

---
apiVersion: v1
kind: Secret
metadata:
  name: monitoring-secrets
  namespace: project2
  labels:
    app: project2-platform
    component: monitoring
type: Opaque
data:
  # Grafana admin credentials (base64 encoded)
  # Original: admin
  GF_SECURITY_ADMIN_USER: YWRtaW4=
  
  # Original: admin123!@#
  GF_SECURITY_ADMIN_PASSWORD: YWRtaW4xMjMhQCM=
  
  # Sentry DSN (base64 encoded - replace with actual DSN)
  # Original: https://<EMAIL>/project-id
  SENTRY_DSN: ************************************************************

---
# TLS Secret for HTTPS
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: project2
  labels:
    app: project2-platform
    component: tls
type: kubernetes.io/tls
data:
  # Replace with actual TLS certificate and key (base64 encoded)
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCi4uLgotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0t
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCi4uLgotLS0tLUVORCBQUklWQVRFIEtFWS0tLS0t
