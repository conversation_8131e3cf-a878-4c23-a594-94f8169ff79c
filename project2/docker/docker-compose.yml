# ============================================================================
# DOCKER COMPOSE - Development Environment
# ============================================================================

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: project2-postgres
    environment:
      POSTGRES_DB: project2
      POSTGRES_USER: project2_user
      POSTGRES_PASSWORD: project2_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../databases/postgresql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - project2-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U project2_user -d project2"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: project2-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ../databases/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - project2-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: project2-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: project2_user
      RABBITMQ_DEFAULT_PASS: project2_password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - project2-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MLflow Tracking Server
  mlflow:
    image: python:3.11-slim
    container_name: project2-mlflow
    working_dir: /app
    environment:
      MLFLOW_BACKEND_STORE_URI: **********************************************************/project2
      MLFLOW_DEFAULT_ARTIFACT_ROOT: /app/artifacts
    ports:
      - "5000:5000"
    volumes:
      - mlflow_artifacts:/app/artifacts
      - ./ai-ml/mlflow:/app
    command: >
      bash -c "
        pip install mlflow psycopg2-binary &&
        mlflow server 
          --backend-store-uri **********************************************************/project2
          --default-artifact-root /app/artifacts
          --host 0.0.0.0
          --port 5000
      "
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - project2-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: project2-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - project2-network

  # Grafana Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: project2-grafana
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - project2-network

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: project2-jaeger
    environment:
      COLLECTOR_ZIPKIN_HOST_PORT: :9411
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
      - "9411:9411"
    networks:
      - project2-network

  # Elasticsearch for Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: project2-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - project2-network

  # Kibana for Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: project2-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - project2-network

  # API Gateway
  api-gateway:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile.gateway
    container_name: project2-api-gateway
    environment:
      - DATABASE_URL=**********************************************************/project2
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - MLFLOW_TRACKING_URI=http://mlflow:5000
    ports:
      - "8000:8000"
    volumes:
      - ../backend:/app
      - /app/__pycache__
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - project2-network
    restart: unless-stopped

  # Auth Service
  auth-service:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile.auth
    container_name: project2-auth-service
    environment:
      - DATABASE_URL=**********************************************************/project2
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "8001:8001"
    volumes:
      - ../backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - project2-network

  # User Service
  user-service:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile.user
    container_name: project2-user-service
    environment:
      - DATABASE_URL=**********************************************************/project2
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "8002:8002"
    volumes:
      - ../backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - project2-network

  # AI Service
  ai-service:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile.ai
    container_name: project2-ai-service
    environment:
      - DATABASE_URL=**********************************************************/project2
      - REDIS_URL=redis://redis:6379/0
      - MLFLOW_TRACKING_URI=http://mlflow:5000
    ports:
      - "8003:8003"
    volumes:
      - ../backend:/app
      - ../ai-ml:/ai-ml
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      mlflow:
        condition: service_started
    networks:
      - project2-network

  # Data Service
  data-service:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile.data
    container_name: project2-data-service
    environment:
      - DATABASE_URL=**********************************************************/project2
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "8004:8004"
    volumes:
      - ../backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - project2-network

  # Notification Service
  notification-service:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile.notification
    container_name: project2-notification-service
    environment:
      - DATABASE_URL=**********************************************************/project2
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
    ports:
      - "8005:8005"
    volumes:
      - ../backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - project2-network

  # Celery Worker
  celery-worker:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile.celery
    container_name: project2-celery-worker
    environment:
      - DATABASE_URL=**********************************************************/project2
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ../backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - project2-network
    command: celery -A shared.celery worker --loglevel=info

  # Celery Beat (Scheduler)
  celery-beat:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile.celery
    container_name: project2-celery-beat
    environment:
      - DATABASE_URL=**********************************************************/project2
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ../backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - project2-network
    command: celery -A shared.celery beat --loglevel=info

  # Flower (Celery Monitoring)
  flower:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile.celery
    container_name: project2-flower
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    ports:
      - "5555:5555"
    volumes:
      - ../backend:/app
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - project2-network
    command: celery -A shared.celery flower --port=5555

# Networks
networks:
  project2-network:
    driver: bridge

# Volumes
volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  mlflow_artifacts:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
