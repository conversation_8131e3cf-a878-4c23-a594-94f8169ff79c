# 🚀 Project2 - Complete Installation Guide

## 📋 Prerequisites

### System Requirements
- **Operating System**: macOS, Linux, or Windows with WSL2
- **RAM**: Minimum 8GB, Recommended 16GB+
- **Storage**: Minimum 20GB free space
- **CPU**: 4+ cores recommended

### Required Software
- **Docker Desktop** 4.0+ with Docker Compose
- **Node.js** 18+ and npm
- **Python** 3.11+ and pip
- **Git** for version control

## 🔧 Installation Methods

### Method 1: One-Command Installation (Recommended)

```bash
# Clone the repository
git clone https://github.com/your-username/project2.git
cd project2

# Run the automated installer
make install
```

This will automatically:
- ✅ Check all prerequisites
- ✅ Setup environment variables
- ✅ Install all dependencies
- ✅ Build Docker images
- ✅ Initialize databases
- ✅ Start all services
- ✅ Run health checks

### Method 2: Manual Step-by-Step Installation

#### Step 1: Clone and Setup
```bash
git clone https://github.com/your-username/project2.git
cd project2
```

#### Step 2: Environment Configuration
```bash
# Backend environment
cp backend/.env.example backend/.env
# Edit backend/.env with your settings

# Frontend environment
cp frontend/.env.example frontend/.env
# Edit frontend/.env with your settings
```

#### Step 3: Install Dependencies
```bash
# Frontend
cd frontend
npm install
cd ..

# Backend (optional - Docker handles this)
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
cd ..
```

#### Step 4: Start Services
```bash
# Start databases first
cd docker
docker-compose up -d postgres redis

# Wait for databases to be ready
sleep 30

# Start all services
docker-compose up -d
```

## 🌐 Access URLs

After successful installation, access these URLs:

| Service | URL | Credentials |
|---------|-----|-------------|
| **Frontend** | http://localhost:5173 | - |
| **API Gateway** | http://localhost:8000 | - |
| **API Documentation** | http://localhost:8000/docs | - |
| **Grafana** | http://localhost:3000 | admin/admin123 |
| **Prometheus** | http://localhost:9090 | - |
| **Jaeger** | http://localhost:16686 | - |
| **MLflow** | http://localhost:5000 | - |
| **RabbitMQ** | http://localhost:15672 | project2_user/project2_password |

### Default Admin User
```
Email: <EMAIL>
Password: admin123
```

## 🔍 Verification

### Health Checks
```bash
# Check all services
make health

# Check individual service
curl http://localhost:8000/health
```

### Service Status
```bash
# View running containers
make status

# View logs
make logs

# View specific service logs
make logs-service SERVICE=api-gateway
```

## 🛠️ Common Commands

```bash
# Development
make dev              # Start development environment
make start            # Start all services
make stop             # Stop all services
make restart          # Restart all services

# Monitoring
make logs             # View all logs
make health           # Check service health
make monitor          # Open monitoring dashboards

# Database
make db-init          # Initialize database
make db-reset         # Reset database (⚠️ deletes data)
make backup           # Create database backup

# Testing
make test             # Run all tests
make test-integration # Run integration tests
make test-e2e         # Run end-to-end tests

# Utilities
make clean            # Clean Docker resources
make help             # Show all commands
```

## 🐛 Troubleshooting

### Common Issues

#### Docker Not Running
```bash
# Start Docker Desktop
open -a Docker  # macOS
# Or start Docker service on Linux
sudo systemctl start docker
```

#### Port Conflicts
```bash
# Check what's using a port
lsof -i :5173  # Frontend port
lsof -i :8000  # API Gateway port

# Kill process using port
kill -9 $(lsof -t -i:5173)
```

#### Database Connection Issues
```bash
# Reset database
make db-reset

# Check database logs
make logs-service SERVICE=postgres
```

#### Frontend Build Issues
```bash
# Clear node_modules and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install
```

#### Backend Import Issues
```bash
# Rebuild backend image
make build-service SERVICE=api-gateway
```

### Service-Specific Troubleshooting

#### PostgreSQL
```bash
# Connect to database
make shell-postgres

# Check database status
docker-compose exec postgres pg_isready -U project2_user
```

#### Redis
```bash
# Connect to Redis
make shell-redis

# Check Redis status
docker-compose exec redis redis-cli ping
```

#### MLflow
```bash
# Check MLflow logs
docker-compose logs mlflow

# Access MLflow UI
open http://localhost:5000
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```bash
# Database
DATABASE_URL=postgresql://project2_user:project2_password@localhost:5432/project2

# Redis
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# External APIs
PINECONE_API_KEY=your-pinecone-api-key
OPENAI_API_KEY=your-openai-api-key
```

#### Frontend (.env)
```bash
# API Configuration
VITE_API_URL=http://localhost:8000

# Feature Flags
VITE_FEATURE_AI_MODELS=true
VITE_FEATURE_ANALYTICS=true
```

### Docker Configuration

#### Memory Settings
```bash
# Increase Docker memory limit
# Docker Desktop > Settings > Resources > Memory: 8GB+
```

#### Volume Cleanup
```bash
# Clean up volumes
docker volume prune

# Remove specific volume
docker volume rm docker_postgres_data
```

## 🚀 Production Deployment

### Kubernetes Deployment
```bash
# Apply Kubernetes manifests
kubectl apply -f kubernetes/

# Check deployment status
kubectl get pods -n project2
```

### Environment-Specific Configs
```bash
# Staging
cp backend/.env.staging backend/.env
make deploy-staging

# Production
cp backend/.env.production backend/.env
make deploy-production
```

## 📞 Support

### Getting Help
- **Documentation**: [docs/](../docs/)
- **Issues**: [GitHub Issues](https://github.com/your-username/project2/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/project2/discussions)

### Logs and Debugging
```bash
# Collect all logs for support
make logs > debug.log

# System information
docker version
docker-compose version
node --version
python --version
```

---

**🎉 Congratulations! Your Project2 environment is ready!**
