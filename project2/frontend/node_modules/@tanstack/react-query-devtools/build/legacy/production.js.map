{"version": 3, "sources": ["../../src/production.ts"], "sourcesContent": ["'use client'\n\nimport * as Devtools from './ReactQueryDevtools'\nimport * as DevtoolsPanel from './ReactQueryDevtoolsPanel'\n\nexport const ReactQueryDevtools = Devtools.ReactQueryDevtools\nexport const ReactQueryDevtoolsPanel = DevtoolsPanel.ReactQueryDevtoolsPanel\n"], "mappings": ";;;AAEA,YAAY,cAAc;AAC1B,YAAY,mBAAmB;AAExB,IAAMA,sBAA8B;AACpC,IAAMC,2BAAwC;", "names": ["ReactQueryDevtools", "ReactQueryDevtoolsPanel"]}