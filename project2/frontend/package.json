{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.80.10", "@tanstack/react-query-devtools": "^5.80.10", "axios": "^1.10.0", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.518.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}