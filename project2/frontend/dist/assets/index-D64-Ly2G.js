var wT=Object.defineProperty;var cg=n=>{throw TypeError(n)};var RT=(n,a,s)=>a in n?wT(n,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[a]=s;var vo=(n,a,s)=>RT(n,typeof a!="symbol"?a+"":a,s),Sf=(n,a,s)=>a.has(n)||cg("Cannot "+s);var P=(n,a,s)=>(Sf(n,a,"read from private field"),s?s.call(n):a.get(n)),wt=(n,a,s)=>a.has(n)?cg("Cannot add the same private member more than once"):a instanceof WeakSet?a.add(n):a.set(n,s),ft=(n,a,s,r)=>(Sf(n,a,"write to private field"),r?r.call(n,s):a.set(n,s),s),me=(n,a,s)=>(Sf(n,a,"access private method"),s);var bo=(n,a,s,r)=>({set _(o){ft(n,a,o,s)},get _(){return P(n,a,r)}});(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const c of o)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&r(d)}).observe(document,{childList:!0,subtree:!0});function s(o){const c={};return o.integrity&&(c.integrity=o.integrity),o.referrerPolicy&&(c.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?c.credentials="include":o.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function r(o){if(o.ep)return;o.ep=!0;const c=s(o);fetch(o.href,c)}})();function MT(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var xf={exports:{}},xr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fg;function CT(){if(fg)return xr;fg=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");function s(r,o,c){var d=null;if(c!==void 0&&(d=""+c),o.key!==void 0&&(d=""+o.key),"key"in o){c={};for(var h in o)h!=="key"&&(c[h]=o[h])}else c=o;return o=c.ref,{$$typeof:n,type:r,key:d,ref:o!==void 0?o:null,props:c}}return xr.Fragment=a,xr.jsx=s,xr.jsxs=s,xr}var dg;function OT(){return dg||(dg=1,xf.exports=CT()),xf.exports}var it=OT(),Tf={exports:{}},ht={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hg;function DT(){if(hg)return ht;hg=1;var n=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),v=Symbol.iterator;function S(M){return M===null||typeof M!="object"?null:(M=v&&M[v]||M["@@iterator"],typeof M=="function"?M:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},x=Object.assign,T={};function w(M,Q,W){this.props=M,this.context=Q,this.refs=T,this.updater=W||E}w.prototype.isReactComponent={},w.prototype.setState=function(M,Q){if(typeof M!="object"&&typeof M!="function"&&M!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,M,Q,"setState")},w.prototype.forceUpdate=function(M){this.updater.enqueueForceUpdate(this,M,"forceUpdate")};function R(){}R.prototype=w.prototype;function V(M,Q,W){this.props=M,this.context=Q,this.refs=T,this.updater=W||E}var U=V.prototype=new R;U.constructor=V,x(U,w.prototype),U.isPureReactComponent=!0;var K=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},$=Object.prototype.hasOwnProperty;function at(M,Q,W,Z,I,vt){return W=vt.ref,{$$typeof:n,type:M,key:Q,ref:W!==void 0?W:null,props:vt}}function Y(M,Q){return at(M.type,Q,void 0,void 0,void 0,M.props)}function J(M){return typeof M=="object"&&M!==null&&M.$$typeof===n}function mt(M){var Q={"=":"=0",":":"=2"};return"$"+M.replace(/[=:]/g,function(W){return Q[W]})}var Vt=/\/+/g;function Bt(M,Q){return typeof M=="object"&&M!==null&&M.key!=null?mt(""+M.key):Q.toString(36)}function Pe(){}function Ae(M){switch(M.status){case"fulfilled":return M.value;case"rejected":throw M.reason;default:switch(typeof M.status=="string"?M.then(Pe,Pe):(M.status="pending",M.then(function(Q){M.status==="pending"&&(M.status="fulfilled",M.value=Q)},function(Q){M.status==="pending"&&(M.status="rejected",M.reason=Q)})),M.status){case"fulfilled":return M.value;case"rejected":throw M.reason}}throw M}function jt(M,Q,W,Z,I){var vt=typeof M;(vt==="undefined"||vt==="boolean")&&(M=null);var ct=!1;if(M===null)ct=!0;else switch(vt){case"bigint":case"string":case"number":ct=!0;break;case"object":switch(M.$$typeof){case n:case a:ct=!0;break;case y:return ct=M._init,jt(ct(M._payload),Q,W,Z,I)}}if(ct)return I=I(M),ct=Z===""?"."+Bt(M,0):Z,K(I)?(W="",ct!=null&&(W=ct.replace(Vt,"$&/")+"/"),jt(I,Q,W,"",function(Ne){return Ne})):I!=null&&(J(I)&&(I=Y(I,W+(I.key==null||M&&M.key===I.key?"":(""+I.key).replace(Vt,"$&/")+"/")+ct)),Q.push(I)),1;ct=0;var At=Z===""?".":Z+":";if(K(M))for(var Nt=0;Nt<M.length;Nt++)Z=M[Nt],vt=At+Bt(Z,Nt),ct+=jt(Z,Q,W,vt,I);else if(Nt=S(M),typeof Nt=="function")for(M=Nt.call(M),Nt=0;!(Z=M.next()).done;)Z=Z.value,vt=At+Bt(Z,Nt++),ct+=jt(Z,Q,W,vt,I);else if(vt==="object"){if(typeof M.then=="function")return jt(Ae(M),Q,W,Z,I);throw Q=String(M),Error("Objects are not valid as a React child (found: "+(Q==="[object Object]"?"object with keys {"+Object.keys(M).join(", ")+"}":Q)+"). If you meant to render a collection of children, use an array instead.")}return ct}function j(M,Q,W){if(M==null)return M;var Z=[],I=0;return jt(M,Z,"","",function(vt){return Q.call(W,vt,I++)}),Z}function F(M){if(M._status===-1){var Q=M._result;Q=Q(),Q.then(function(W){(M._status===0||M._status===-1)&&(M._status=1,M._result=W)},function(W){(M._status===0||M._status===-1)&&(M._status=2,M._result=W)}),M._status===-1&&(M._status=0,M._result=Q)}if(M._status===1)return M._result.default;throw M._result}var X=typeof reportError=="function"?reportError:function(M){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof M=="object"&&M!==null&&typeof M.message=="string"?String(M.message):String(M),error:M});if(!window.dispatchEvent(Q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",M);return}console.error(M)};function xt(){}return ht.Children={map:j,forEach:function(M,Q,W){j(M,function(){Q.apply(this,arguments)},W)},count:function(M){var Q=0;return j(M,function(){Q++}),Q},toArray:function(M){return j(M,function(Q){return Q})||[]},only:function(M){if(!J(M))throw Error("React.Children.only expected to receive a single React element child.");return M}},ht.Component=w,ht.Fragment=s,ht.Profiler=o,ht.PureComponent=V,ht.StrictMode=r,ht.Suspense=p,ht.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,ht.__COMPILER_RUNTIME={__proto__:null,c:function(M){return k.H.useMemoCache(M)}},ht.cache=function(M){return function(){return M.apply(null,arguments)}},ht.cloneElement=function(M,Q,W){if(M==null)throw Error("The argument must be a React element, but you passed "+M+".");var Z=x({},M.props),I=M.key,vt=void 0;if(Q!=null)for(ct in Q.ref!==void 0&&(vt=void 0),Q.key!==void 0&&(I=""+Q.key),Q)!$.call(Q,ct)||ct==="key"||ct==="__self"||ct==="__source"||ct==="ref"&&Q.ref===void 0||(Z[ct]=Q[ct]);var ct=arguments.length-2;if(ct===1)Z.children=W;else if(1<ct){for(var At=Array(ct),Nt=0;Nt<ct;Nt++)At[Nt]=arguments[Nt+2];Z.children=At}return at(M.type,I,void 0,void 0,vt,Z)},ht.createContext=function(M){return M={$$typeof:d,_currentValue:M,_currentValue2:M,_threadCount:0,Provider:null,Consumer:null},M.Provider=M,M.Consumer={$$typeof:c,_context:M},M},ht.createElement=function(M,Q,W){var Z,I={},vt=null;if(Q!=null)for(Z in Q.key!==void 0&&(vt=""+Q.key),Q)$.call(Q,Z)&&Z!=="key"&&Z!=="__self"&&Z!=="__source"&&(I[Z]=Q[Z]);var ct=arguments.length-2;if(ct===1)I.children=W;else if(1<ct){for(var At=Array(ct),Nt=0;Nt<ct;Nt++)At[Nt]=arguments[Nt+2];I.children=At}if(M&&M.defaultProps)for(Z in ct=M.defaultProps,ct)I[Z]===void 0&&(I[Z]=ct[Z]);return at(M,vt,void 0,void 0,null,I)},ht.createRef=function(){return{current:null}},ht.forwardRef=function(M){return{$$typeof:h,render:M}},ht.isValidElement=J,ht.lazy=function(M){return{$$typeof:y,_payload:{_status:-1,_result:M},_init:F}},ht.memo=function(M,Q){return{$$typeof:m,type:M,compare:Q===void 0?null:Q}},ht.startTransition=function(M){var Q=k.T,W={};k.T=W;try{var Z=M(),I=k.S;I!==null&&I(W,Z),typeof Z=="object"&&Z!==null&&typeof Z.then=="function"&&Z.then(xt,X)}catch(vt){X(vt)}finally{k.T=Q}},ht.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},ht.use=function(M){return k.H.use(M)},ht.useActionState=function(M,Q,W){return k.H.useActionState(M,Q,W)},ht.useCallback=function(M,Q){return k.H.useCallback(M,Q)},ht.useContext=function(M){return k.H.useContext(M)},ht.useDebugValue=function(){},ht.useDeferredValue=function(M,Q){return k.H.useDeferredValue(M,Q)},ht.useEffect=function(M,Q,W){var Z=k.H;if(typeof W=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Z.useEffect(M,Q)},ht.useId=function(){return k.H.useId()},ht.useImperativeHandle=function(M,Q,W){return k.H.useImperativeHandle(M,Q,W)},ht.useInsertionEffect=function(M,Q){return k.H.useInsertionEffect(M,Q)},ht.useLayoutEffect=function(M,Q){return k.H.useLayoutEffect(M,Q)},ht.useMemo=function(M,Q){return k.H.useMemo(M,Q)},ht.useOptimistic=function(M,Q){return k.H.useOptimistic(M,Q)},ht.useReducer=function(M,Q,W){return k.H.useReducer(M,Q,W)},ht.useRef=function(M){return k.H.useRef(M)},ht.useState=function(M){return k.H.useState(M)},ht.useSyncExternalStore=function(M,Q,W){return k.H.useSyncExternalStore(M,Q,W)},ht.useTransition=function(){return k.H.useTransition()},ht.version="19.1.0",ht}var mg;function Md(){return mg||(mg=1,Tf.exports=DT()),Tf.exports}var z=Md();const pg=MT(z);var Af={exports:{}},Tr={},Ef={exports:{}},wf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yg;function UT(){return yg||(yg=1,function(n){function a(j,F){var X=j.length;j.push(F);t:for(;0<X;){var xt=X-1>>>1,M=j[xt];if(0<o(M,F))j[xt]=F,j[X]=M,X=xt;else break t}}function s(j){return j.length===0?null:j[0]}function r(j){if(j.length===0)return null;var F=j[0],X=j.pop();if(X!==F){j[0]=X;t:for(var xt=0,M=j.length,Q=M>>>1;xt<Q;){var W=2*(xt+1)-1,Z=j[W],I=W+1,vt=j[I];if(0>o(Z,X))I<M&&0>o(vt,Z)?(j[xt]=vt,j[I]=X,xt=I):(j[xt]=Z,j[W]=X,xt=W);else if(I<M&&0>o(vt,X))j[xt]=vt,j[I]=X,xt=I;else break t}}return F}function o(j,F){var X=j.sortIndex-F.sortIndex;return X!==0?X:j.id-F.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;n.unstable_now=function(){return c.now()}}else{var d=Date,h=d.now();n.unstable_now=function(){return d.now()-h}}var p=[],m=[],y=1,v=null,S=3,E=!1,x=!1,T=!1,w=!1,R=typeof setTimeout=="function"?setTimeout:null,V=typeof clearTimeout=="function"?clearTimeout:null,U=typeof setImmediate<"u"?setImmediate:null;function K(j){for(var F=s(m);F!==null;){if(F.callback===null)r(m);else if(F.startTime<=j)r(m),F.sortIndex=F.expirationTime,a(p,F);else break;F=s(m)}}function k(j){if(T=!1,K(j),!x)if(s(p)!==null)x=!0,$||($=!0,Bt());else{var F=s(m);F!==null&&jt(k,F.startTime-j)}}var $=!1,at=-1,Y=5,J=-1;function mt(){return w?!0:!(n.unstable_now()-J<Y)}function Vt(){if(w=!1,$){var j=n.unstable_now();J=j;var F=!0;try{t:{x=!1,T&&(T=!1,V(at),at=-1),E=!0;var X=S;try{e:{for(K(j),v=s(p);v!==null&&!(v.expirationTime>j&&mt());){var xt=v.callback;if(typeof xt=="function"){v.callback=null,S=v.priorityLevel;var M=xt(v.expirationTime<=j);if(j=n.unstable_now(),typeof M=="function"){v.callback=M,K(j),F=!0;break e}v===s(p)&&r(p),K(j)}else r(p);v=s(p)}if(v!==null)F=!0;else{var Q=s(m);Q!==null&&jt(k,Q.startTime-j),F=!1}}break t}finally{v=null,S=X,E=!1}F=void 0}}finally{F?Bt():$=!1}}}var Bt;if(typeof U=="function")Bt=function(){U(Vt)};else if(typeof MessageChannel<"u"){var Pe=new MessageChannel,Ae=Pe.port2;Pe.port1.onmessage=Vt,Bt=function(){Ae.postMessage(null)}}else Bt=function(){R(Vt,0)};function jt(j,F){at=R(function(){j(n.unstable_now())},F)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(j){j.callback=null},n.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Y=0<j?Math.floor(1e3/j):5},n.unstable_getCurrentPriorityLevel=function(){return S},n.unstable_next=function(j){switch(S){case 1:case 2:case 3:var F=3;break;default:F=S}var X=S;S=F;try{return j()}finally{S=X}},n.unstable_requestPaint=function(){w=!0},n.unstable_runWithPriority=function(j,F){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var X=S;S=j;try{return F()}finally{S=X}},n.unstable_scheduleCallback=function(j,F,X){var xt=n.unstable_now();switch(typeof X=="object"&&X!==null?(X=X.delay,X=typeof X=="number"&&0<X?xt+X:xt):X=xt,j){case 1:var M=-1;break;case 2:M=250;break;case 5:M=1073741823;break;case 4:M=1e4;break;default:M=5e3}return M=X+M,j={id:y++,callback:F,priorityLevel:j,startTime:X,expirationTime:M,sortIndex:-1},X>xt?(j.sortIndex=X,a(m,j),s(p)===null&&j===s(m)&&(T?(V(at),at=-1):T=!0,jt(k,X-xt))):(j.sortIndex=M,a(p,j),x||E||(x=!0,$||($=!0,Bt()))),j},n.unstable_shouldYield=mt,n.unstable_wrapCallback=function(j){var F=S;return function(){var X=S;S=F;try{return j.apply(this,arguments)}finally{S=X}}}}(wf)),wf}var gg;function NT(){return gg||(gg=1,Ef.exports=UT()),Ef.exports}var Rf={exports:{}},pe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vg;function zT(){if(vg)return pe;vg=1;var n=Md();function a(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)m+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var r={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},o=Symbol.for("react.portal");function c(p,m,y){var v=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:v==null?null:""+v,children:p,containerInfo:m,implementation:y}}var d=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return pe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,pe.createPortal=function(p,m){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(a(299));return c(p,m,null,y)},pe.flushSync=function(p){var m=d.T,y=r.p;try{if(d.T=null,r.p=2,p)return p()}finally{d.T=m,r.p=y,r.d.f()}},pe.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,r.d.C(p,m))},pe.prefetchDNS=function(p){typeof p=="string"&&r.d.D(p)},pe.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var y=m.as,v=h(y,m.crossOrigin),S=typeof m.integrity=="string"?m.integrity:void 0,E=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;y==="style"?r.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:v,integrity:S,fetchPriority:E}):y==="script"&&r.d.X(p,{crossOrigin:v,integrity:S,fetchPriority:E,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},pe.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var y=h(m.as,m.crossOrigin);r.d.M(p,{crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&r.d.M(p)},pe.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var y=m.as,v=h(y,m.crossOrigin);r.d.L(p,y,{crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},pe.preloadModule=function(p,m){if(typeof p=="string")if(m){var y=h(m.as,m.crossOrigin);r.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:y,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else r.d.m(p)},pe.requestFormReset=function(p){r.d.r(p)},pe.unstable_batchedUpdates=function(p,m){return p(m)},pe.useFormState=function(p,m,y){return d.H.useFormState(p,m,y)},pe.useFormStatus=function(){return d.H.useHostTransitionStatus()},pe.version="19.1.0",pe}var bg;function _T(){if(bg)return Rf.exports;bg=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),Rf.exports=zT(),Rf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sg;function LT(){if(Sg)return Tr;Sg=1;var n=NT(),a=Md(),s=_T();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)e+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function c(t){var e=t,i=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(i=e.return),t=e.return;while(t)}return e.tag===3?i:null}function d(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function h(t){if(c(t)!==t)throw Error(r(188))}function p(t){var e=t.alternate;if(!e){if(e=c(t),e===null)throw Error(r(188));return e!==t?null:t}for(var i=t,l=e;;){var u=i.return;if(u===null)break;var f=u.alternate;if(f===null){if(l=u.return,l!==null){i=l;continue}break}if(u.child===f.child){for(f=u.child;f;){if(f===i)return h(u),t;if(f===l)return h(u),e;f=f.sibling}throw Error(r(188))}if(i.return!==l.return)i=u,l=f;else{for(var g=!1,b=u.child;b;){if(b===i){g=!0,i=u,l=f;break}if(b===l){g=!0,l=u,i=f;break}b=b.sibling}if(!g){for(b=f.child;b;){if(b===i){g=!0,i=f,l=u;break}if(b===l){g=!0,l=f,i=u;break}b=b.sibling}if(!g)throw Error(r(189))}}if(i.alternate!==l)throw Error(r(190))}if(i.tag!==3)throw Error(r(188));return i.stateNode.current===i?t:e}function m(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=m(t),e!==null)return e;t=t.sibling}return null}var y=Object.assign,v=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),E=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),T=Symbol.for("react.strict_mode"),w=Symbol.for("react.profiler"),R=Symbol.for("react.provider"),V=Symbol.for("react.consumer"),U=Symbol.for("react.context"),K=Symbol.for("react.forward_ref"),k=Symbol.for("react.suspense"),$=Symbol.for("react.suspense_list"),at=Symbol.for("react.memo"),Y=Symbol.for("react.lazy"),J=Symbol.for("react.activity"),mt=Symbol.for("react.memo_cache_sentinel"),Vt=Symbol.iterator;function Bt(t){return t===null||typeof t!="object"?null:(t=Vt&&t[Vt]||t["@@iterator"],typeof t=="function"?t:null)}var Pe=Symbol.for("react.client.reference");function Ae(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Pe?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case x:return"Fragment";case w:return"Profiler";case T:return"StrictMode";case k:return"Suspense";case $:return"SuspenseList";case J:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case E:return"Portal";case U:return(t.displayName||"Context")+".Provider";case V:return(t._context.displayName||"Context")+".Consumer";case K:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case at:return e=t.displayName||null,e!==null?e:Ae(t.type)||"Memo";case Y:e=t._payload,t=t._init;try{return Ae(t(e))}catch{}}return null}var jt=Array.isArray,j=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X={pending:!1,data:null,method:null,action:null},xt=[],M=-1;function Q(t){return{current:t}}function W(t){0>M||(t.current=xt[M],xt[M]=null,M--)}function Z(t,e){M++,xt[M]=t.current,t.current=e}var I=Q(null),vt=Q(null),ct=Q(null),At=Q(null);function Nt(t,e){switch(Z(ct,e),Z(vt,t),Z(I,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?jy(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=jy(e),t=ky(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}W(I),Z(I,t)}function Ne(){W(I),W(vt),W(ct)}function Zn(t){t.memoizedState!==null&&Z(At,t);var e=I.current,i=ky(e,t.type);e!==i&&(Z(vt,t),Z(I,i))}function $n(t){vt.current===t&&(W(I),W(vt)),At.current===t&&(W(At),yr._currentValue=X)}var Jn=Object.prototype.hasOwnProperty,ru=n.unstable_scheduleCallback,lu=n.unstable_cancelCallback,a1=n.unstable_shouldYield,i1=n.unstable_requestPaint,fn=n.unstable_now,s1=n.unstable_getCurrentPriorityLevel,vh=n.unstable_ImmediatePriority,bh=n.unstable_UserBlockingPriority,al=n.unstable_NormalPriority,r1=n.unstable_LowPriority,Sh=n.unstable_IdlePriority,l1=n.log,o1=n.unstable_setDisableYieldValue,Es=null,ze=null;function Wn(t){if(typeof l1=="function"&&o1(t),ze&&typeof ze.setStrictMode=="function")try{ze.setStrictMode(Es,t)}catch{}}var _e=Math.clz32?Math.clz32:f1,u1=Math.log,c1=Math.LN2;function f1(t){return t>>>=0,t===0?32:31-(u1(t)/c1|0)|0}var il=256,sl=4194304;function za(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function rl(t,e,i){var l=t.pendingLanes;if(l===0)return 0;var u=0,f=t.suspendedLanes,g=t.pingedLanes;t=t.warmLanes;var b=l&134217727;return b!==0?(l=b&~f,l!==0?u=za(l):(g&=b,g!==0?u=za(g):i||(i=b&~t,i!==0&&(u=za(i))))):(b=l&~f,b!==0?u=za(b):g!==0?u=za(g):i||(i=l&~t,i!==0&&(u=za(i)))),u===0?0:e!==0&&e!==u&&(e&f)===0&&(f=u&-u,i=e&-e,f>=i||f===32&&(i&4194048)!==0)?e:u}function ws(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function d1(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function xh(){var t=il;return il<<=1,(il&4194048)===0&&(il=256),t}function Th(){var t=sl;return sl<<=1,(sl&62914560)===0&&(sl=4194304),t}function ou(t){for(var e=[],i=0;31>i;i++)e.push(t);return e}function Rs(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function h1(t,e,i,l,u,f){var g=t.pendingLanes;t.pendingLanes=i,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=i,t.entangledLanes&=i,t.errorRecoveryDisabledLanes&=i,t.shellSuspendCounter=0;var b=t.entanglements,A=t.expirationTimes,N=t.hiddenUpdates;for(i=g&~i;0<i;){var H=31-_e(i),G=1<<H;b[H]=0,A[H]=-1;var _=N[H];if(_!==null)for(N[H]=null,H=0;H<_.length;H++){var L=_[H];L!==null&&(L.lane&=-536870913)}i&=~G}l!==0&&Ah(t,l,0),f!==0&&u===0&&t.tag!==0&&(t.suspendedLanes|=f&~(g&~e))}function Ah(t,e,i){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-_e(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|i&4194090}function Eh(t,e){var i=t.entangledLanes|=e;for(t=t.entanglements;i;){var l=31-_e(i),u=1<<l;u&e|t[l]&e&&(t[l]|=e),i&=~u}}function uu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function cu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function wh(){var t=F.p;return t!==0?t:(t=window.event,t===void 0?32:ig(t.type))}function m1(t,e){var i=F.p;try{return F.p=t,e()}finally{F.p=i}}var In=Math.random().toString(36).slice(2),de="__reactFiber$"+In,Ee="__reactProps$"+In,mi="__reactContainer$"+In,fu="__reactEvents$"+In,p1="__reactListeners$"+In,y1="__reactHandles$"+In,Rh="__reactResources$"+In,Ms="__reactMarker$"+In;function du(t){delete t[de],delete t[Ee],delete t[fu],delete t[p1],delete t[y1]}function pi(t){var e=t[de];if(e)return e;for(var i=t.parentNode;i;){if(e=i[mi]||i[de]){if(i=e.alternate,e.child!==null||i!==null&&i.child!==null)for(t=Gy(t);t!==null;){if(i=t[de])return i;t=Gy(t)}return e}t=i,i=t.parentNode}return null}function yi(t){if(t=t[de]||t[mi]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Cs(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function gi(t){var e=t[Rh];return e||(e=t[Rh]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ie(t){t[Ms]=!0}var Mh=new Set,Ch={};function _a(t,e){vi(t,e),vi(t+"Capture",e)}function vi(t,e){for(Ch[t]=e,t=0;t<e.length;t++)Mh.add(e[t])}var g1=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Oh={},Dh={};function v1(t){return Jn.call(Dh,t)?!0:Jn.call(Oh,t)?!1:g1.test(t)?Dh[t]=!0:(Oh[t]=!0,!1)}function ll(t,e,i){if(v1(e))if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+i)}}function ol(t,e,i){if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+i)}}function Rn(t,e,i,l){if(l===null)t.removeAttribute(i);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(i);return}t.setAttributeNS(e,i,""+l)}}var hu,Uh;function bi(t){if(hu===void 0)try{throw Error()}catch(i){var e=i.stack.trim().match(/\n( *(at )?)/);hu=e&&e[1]||"",Uh=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+hu+t+Uh}var mu=!1;function pu(t,e){if(!t||mu)return"";mu=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var G=function(){throw Error()};if(Object.defineProperty(G.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(G,[])}catch(L){var _=L}Reflect.construct(t,[],G)}else{try{G.call()}catch(L){_=L}t.call(G.prototype)}}else{try{throw Error()}catch(L){_=L}(G=t())&&typeof G.catch=="function"&&G.catch(function(){})}}catch(L){if(L&&_&&typeof L.stack=="string")return[L.stack,_.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=l.DetermineComponentFrameRoot(),g=f[0],b=f[1];if(g&&b){var A=g.split(`
`),N=b.split(`
`);for(u=l=0;l<A.length&&!A[l].includes("DetermineComponentFrameRoot");)l++;for(;u<N.length&&!N[u].includes("DetermineComponentFrameRoot");)u++;if(l===A.length||u===N.length)for(l=A.length-1,u=N.length-1;1<=l&&0<=u&&A[l]!==N[u];)u--;for(;1<=l&&0<=u;l--,u--)if(A[l]!==N[u]){if(l!==1||u!==1)do if(l--,u--,0>u||A[l]!==N[u]){var H=`
`+A[l].replace(" at new "," at ");return t.displayName&&H.includes("<anonymous>")&&(H=H.replace("<anonymous>",t.displayName)),H}while(1<=l&&0<=u);break}}}finally{mu=!1,Error.prepareStackTrace=i}return(i=t?t.displayName||t.name:"")?bi(i):""}function b1(t){switch(t.tag){case 26:case 27:case 5:return bi(t.type);case 16:return bi("Lazy");case 13:return bi("Suspense");case 19:return bi("SuspenseList");case 0:case 15:return pu(t.type,!1);case 11:return pu(t.type.render,!1);case 1:return pu(t.type,!0);case 31:return bi("Activity");default:return""}}function Nh(t){try{var e="";do e+=b1(t),t=t.return;while(t);return e}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function Ge(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function zh(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function S1(t){var e=zh(t)?"checked":"value",i=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var u=i.get,f=i.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return u.call(this)},set:function(g){l=""+g,f.call(this,g)}}),Object.defineProperty(t,e,{enumerable:i.enumerable}),{getValue:function(){return l},setValue:function(g){l=""+g},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function ul(t){t._valueTracker||(t._valueTracker=S1(t))}function _h(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var i=e.getValue(),l="";return t&&(l=zh(t)?t.checked?"true":"false":t.value),t=l,t!==i?(e.setValue(t),!0):!1}function cl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var x1=/[\n"\\]/g;function Ye(t){return t.replace(x1,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function yu(t,e,i,l,u,f,g,b){t.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?t.type=g:t.removeAttribute("type"),e!=null?g==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Ge(e)):t.value!==""+Ge(e)&&(t.value=""+Ge(e)):g!=="submit"&&g!=="reset"||t.removeAttribute("value"),e!=null?gu(t,g,Ge(e)):i!=null?gu(t,g,Ge(i)):l!=null&&t.removeAttribute("value"),u==null&&f!=null&&(t.defaultChecked=!!f),u!=null&&(t.checked=u&&typeof u!="function"&&typeof u!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?t.name=""+Ge(b):t.removeAttribute("name")}function Lh(t,e,i,l,u,f,g,b){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.type=f),e!=null||i!=null){if(!(f!=="submit"&&f!=="reset"||e!=null))return;i=i!=null?""+Ge(i):"",e=e!=null?""+Ge(e):i,b||e===t.value||(t.value=e),t.defaultValue=e}l=l??u,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=b?t.checked:!!l,t.defaultChecked=!!l,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(t.name=g)}function gu(t,e,i){e==="number"&&cl(t.ownerDocument)===t||t.defaultValue===""+i||(t.defaultValue=""+i)}function Si(t,e,i,l){if(t=t.options,e){e={};for(var u=0;u<i.length;u++)e["$"+i[u]]=!0;for(i=0;i<t.length;i++)u=e.hasOwnProperty("$"+t[i].value),t[i].selected!==u&&(t[i].selected=u),u&&l&&(t[i].defaultSelected=!0)}else{for(i=""+Ge(i),e=null,u=0;u<t.length;u++){if(t[u].value===i){t[u].selected=!0,l&&(t[u].defaultSelected=!0);return}e!==null||t[u].disabled||(e=t[u])}e!==null&&(e.selected=!0)}}function Vh(t,e,i){if(e!=null&&(e=""+Ge(e),e!==t.value&&(t.value=e),i==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=i!=null?""+Ge(i):""}function Bh(t,e,i,l){if(e==null){if(l!=null){if(i!=null)throw Error(r(92));if(jt(l)){if(1<l.length)throw Error(r(93));l=l[0]}i=l}i==null&&(i=""),e=i}i=Ge(e),t.defaultValue=i,l=t.textContent,l===i&&l!==""&&l!==null&&(t.value=l)}function xi(t,e){if(e){var i=t.firstChild;if(i&&i===t.lastChild&&i.nodeType===3){i.nodeValue=e;return}}t.textContent=e}var T1=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function jh(t,e,i){var l=e.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,i):typeof i!="number"||i===0||T1.has(e)?e==="float"?t.cssFloat=i:t[e]=(""+i).trim():t[e]=i+"px"}function kh(t,e,i){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,i!=null){for(var l in i)!i.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var u in e)l=e[u],e.hasOwnProperty(u)&&i[u]!==l&&jh(t,u,l)}else for(var f in e)e.hasOwnProperty(f)&&jh(t,f,e[f])}function vu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var A1=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),E1=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function fl(t){return E1.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var bu=null;function Su(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ti=null,Ai=null;function Hh(t){var e=yi(t);if(e&&(t=e.stateNode)){var i=t[Ee]||null;t:switch(t=e.stateNode,e.type){case"input":if(yu(t,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),e=i.name,i.type==="radio"&&e!=null){for(i=t;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+Ye(""+e)+'"][type="radio"]'),e=0;e<i.length;e++){var l=i[e];if(l!==t&&l.form===t.form){var u=l[Ee]||null;if(!u)throw Error(r(90));yu(l,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(e=0;e<i.length;e++)l=i[e],l.form===t.form&&_h(l)}break t;case"textarea":Vh(t,i.value,i.defaultValue);break t;case"select":e=i.value,e!=null&&Si(t,!!i.multiple,e,!1)}}}var xu=!1;function qh(t,e,i){if(xu)return t(e,i);xu=!0;try{var l=t(e);return l}finally{if(xu=!1,(Ti!==null||Ai!==null)&&($l(),Ti&&(e=Ti,t=Ai,Ai=Ti=null,Hh(e),t)))for(e=0;e<t.length;e++)Hh(t[e])}}function Os(t,e){var i=t.stateNode;if(i===null)return null;var l=i[Ee]||null;if(l===null)return null;i=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(i&&typeof i!="function")throw Error(r(231,e,typeof i));return i}var Mn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Tu=!1;if(Mn)try{var Ds={};Object.defineProperty(Ds,"passive",{get:function(){Tu=!0}}),window.addEventListener("test",Ds,Ds),window.removeEventListener("test",Ds,Ds)}catch{Tu=!1}var ta=null,Au=null,dl=null;function Ph(){if(dl)return dl;var t,e=Au,i=e.length,l,u="value"in ta?ta.value:ta.textContent,f=u.length;for(t=0;t<i&&e[t]===u[t];t++);var g=i-t;for(l=1;l<=g&&e[i-l]===u[f-l];l++);return dl=u.slice(t,1<l?1-l:void 0)}function hl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ml(){return!0}function Gh(){return!1}function we(t){function e(i,l,u,f,g){this._reactName=i,this._targetInst=u,this.type=l,this.nativeEvent=f,this.target=g,this.currentTarget=null;for(var b in t)t.hasOwnProperty(b)&&(i=t[b],this[b]=i?i(f):f[b]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?ml:Gh,this.isPropagationStopped=Gh,this}return y(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=ml)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=ml)},persist:function(){},isPersistent:ml}),e}var La={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},pl=we(La),Us=y({},La,{view:0,detail:0}),w1=we(Us),Eu,wu,Ns,yl=y({},Us,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Mu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ns&&(Ns&&t.type==="mousemove"?(Eu=t.screenX-Ns.screenX,wu=t.screenY-Ns.screenY):wu=Eu=0,Ns=t),Eu)},movementY:function(t){return"movementY"in t?t.movementY:wu}}),Yh=we(yl),R1=y({},yl,{dataTransfer:0}),M1=we(R1),C1=y({},Us,{relatedTarget:0}),Ru=we(C1),O1=y({},La,{animationName:0,elapsedTime:0,pseudoElement:0}),D1=we(O1),U1=y({},La,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),N1=we(U1),z1=y({},La,{data:0}),Xh=we(z1),_1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},L1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},V1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function B1(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=V1[t])?!!e[t]:!1}function Mu(){return B1}var j1=y({},Us,{key:function(t){if(t.key){var e=_1[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=hl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?L1[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Mu,charCode:function(t){return t.type==="keypress"?hl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?hl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),k1=we(j1),H1=y({},yl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Qh=we(H1),q1=y({},Us,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Mu}),P1=we(q1),G1=y({},La,{propertyName:0,elapsedTime:0,pseudoElement:0}),Y1=we(G1),X1=y({},yl,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Q1=we(X1),K1=y({},La,{newState:0,oldState:0}),F1=we(K1),Z1=[9,13,27,32],Cu=Mn&&"CompositionEvent"in window,zs=null;Mn&&"documentMode"in document&&(zs=document.documentMode);var $1=Mn&&"TextEvent"in window&&!zs,Kh=Mn&&(!Cu||zs&&8<zs&&11>=zs),Fh=" ",Zh=!1;function $h(t,e){switch(t){case"keyup":return Z1.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jh(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ei=!1;function J1(t,e){switch(t){case"compositionend":return Jh(e);case"keypress":return e.which!==32?null:(Zh=!0,Fh);case"textInput":return t=e.data,t===Fh&&Zh?null:t;default:return null}}function W1(t,e){if(Ei)return t==="compositionend"||!Cu&&$h(t,e)?(t=Ph(),dl=Au=ta=null,Ei=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Kh&&e.locale!=="ko"?null:e.data;default:return null}}var I1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wh(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!I1[t.type]:e==="textarea"}function Ih(t,e,i,l){Ti?Ai?Ai.push(l):Ai=[l]:Ti=l,e=no(e,"onChange"),0<e.length&&(i=new pl("onChange","change",null,i,l),t.push({event:i,listeners:e}))}var _s=null,Ls=null;function tx(t){zy(t,0)}function gl(t){var e=Cs(t);if(_h(e))return t}function tm(t,e){if(t==="change")return e}var em=!1;if(Mn){var Ou;if(Mn){var Du="oninput"in document;if(!Du){var nm=document.createElement("div");nm.setAttribute("oninput","return;"),Du=typeof nm.oninput=="function"}Ou=Du}else Ou=!1;em=Ou&&(!document.documentMode||9<document.documentMode)}function am(){_s&&(_s.detachEvent("onpropertychange",im),Ls=_s=null)}function im(t){if(t.propertyName==="value"&&gl(Ls)){var e=[];Ih(e,Ls,t,Su(t)),qh(tx,e)}}function ex(t,e,i){t==="focusin"?(am(),_s=e,Ls=i,_s.attachEvent("onpropertychange",im)):t==="focusout"&&am()}function nx(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return gl(Ls)}function ax(t,e){if(t==="click")return gl(e)}function ix(t,e){if(t==="input"||t==="change")return gl(e)}function sx(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Le=typeof Object.is=="function"?Object.is:sx;function Vs(t,e){if(Le(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var i=Object.keys(t),l=Object.keys(e);if(i.length!==l.length)return!1;for(l=0;l<i.length;l++){var u=i[l];if(!Jn.call(e,u)||!Le(t[u],e[u]))return!1}return!0}function sm(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function rm(t,e){var i=sm(t);t=0;for(var l;i;){if(i.nodeType===3){if(l=t+i.textContent.length,t<=e&&l>=e)return{node:i,offset:e-t};t=l}t:{for(;i;){if(i.nextSibling){i=i.nextSibling;break t}i=i.parentNode}i=void 0}i=sm(i)}}function lm(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?lm(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function om(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=cl(t.document);e instanceof t.HTMLIFrameElement;){try{var i=typeof e.contentWindow.location.href=="string"}catch{i=!1}if(i)t=e.contentWindow;else break;e=cl(t.document)}return e}function Uu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var rx=Mn&&"documentMode"in document&&11>=document.documentMode,wi=null,Nu=null,Bs=null,zu=!1;function um(t,e,i){var l=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;zu||wi==null||wi!==cl(l)||(l=wi,"selectionStart"in l&&Uu(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Bs&&Vs(Bs,l)||(Bs=l,l=no(Nu,"onSelect"),0<l.length&&(e=new pl("onSelect","select",null,e,i),t.push({event:e,listeners:l}),e.target=wi)))}function Va(t,e){var i={};return i[t.toLowerCase()]=e.toLowerCase(),i["Webkit"+t]="webkit"+e,i["Moz"+t]="moz"+e,i}var Ri={animationend:Va("Animation","AnimationEnd"),animationiteration:Va("Animation","AnimationIteration"),animationstart:Va("Animation","AnimationStart"),transitionrun:Va("Transition","TransitionRun"),transitionstart:Va("Transition","TransitionStart"),transitioncancel:Va("Transition","TransitionCancel"),transitionend:Va("Transition","TransitionEnd")},_u={},cm={};Mn&&(cm=document.createElement("div").style,"AnimationEvent"in window||(delete Ri.animationend.animation,delete Ri.animationiteration.animation,delete Ri.animationstart.animation),"TransitionEvent"in window||delete Ri.transitionend.transition);function Ba(t){if(_u[t])return _u[t];if(!Ri[t])return t;var e=Ri[t],i;for(i in e)if(e.hasOwnProperty(i)&&i in cm)return _u[t]=e[i];return t}var fm=Ba("animationend"),dm=Ba("animationiteration"),hm=Ba("animationstart"),lx=Ba("transitionrun"),ox=Ba("transitionstart"),ux=Ba("transitioncancel"),mm=Ba("transitionend"),pm=new Map,Lu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Lu.push("scrollEnd");function nn(t,e){pm.set(t,e),_a(e,[t])}var ym=new WeakMap;function Xe(t,e){if(typeof t=="object"&&t!==null){var i=ym.get(t);return i!==void 0?i:(e={value:t,source:e,stack:Nh(e)},ym.set(t,e),e)}return{value:t,source:e,stack:Nh(e)}}var Qe=[],Mi=0,Vu=0;function vl(){for(var t=Mi,e=Vu=Mi=0;e<t;){var i=Qe[e];Qe[e++]=null;var l=Qe[e];Qe[e++]=null;var u=Qe[e];Qe[e++]=null;var f=Qe[e];if(Qe[e++]=null,l!==null&&u!==null){var g=l.pending;g===null?u.next=u:(u.next=g.next,g.next=u),l.pending=u}f!==0&&gm(i,u,f)}}function bl(t,e,i,l){Qe[Mi++]=t,Qe[Mi++]=e,Qe[Mi++]=i,Qe[Mi++]=l,Vu|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Bu(t,e,i,l){return bl(t,e,i,l),Sl(t)}function Ci(t,e){return bl(t,null,null,e),Sl(t)}function gm(t,e,i){t.lanes|=i;var l=t.alternate;l!==null&&(l.lanes|=i);for(var u=!1,f=t.return;f!==null;)f.childLanes|=i,l=f.alternate,l!==null&&(l.childLanes|=i),f.tag===22&&(t=f.stateNode,t===null||t._visibility&1||(u=!0)),t=f,f=f.return;return t.tag===3?(f=t.stateNode,u&&e!==null&&(u=31-_e(i),t=f.hiddenUpdates,l=t[u],l===null?t[u]=[e]:l.push(e),e.lane=i|536870912),f):null}function Sl(t){if(50<or)throw or=0,Gc=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Oi={};function cx(t,e,i,l){this.tag=t,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ve(t,e,i,l){return new cx(t,e,i,l)}function ju(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Cn(t,e){var i=t.alternate;return i===null?(i=Ve(t.tag,e,t.key,t.mode),i.elementType=t.elementType,i.type=t.type,i.stateNode=t.stateNode,i.alternate=t,t.alternate=i):(i.pendingProps=e,i.type=t.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=t.flags&65011712,i.childLanes=t.childLanes,i.lanes=t.lanes,i.child=t.child,i.memoizedProps=t.memoizedProps,i.memoizedState=t.memoizedState,i.updateQueue=t.updateQueue,e=t.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},i.sibling=t.sibling,i.index=t.index,i.ref=t.ref,i.refCleanup=t.refCleanup,i}function vm(t,e){t.flags&=65011714;var i=t.alternate;return i===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=i.childLanes,t.lanes=i.lanes,t.child=i.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=i.memoizedProps,t.memoizedState=i.memoizedState,t.updateQueue=i.updateQueue,t.type=i.type,e=i.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function xl(t,e,i,l,u,f){var g=0;if(l=t,typeof t=="function")ju(t)&&(g=1);else if(typeof t=="string")g=dT(t,i,I.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case J:return t=Ve(31,i,e,u),t.elementType=J,t.lanes=f,t;case x:return ja(i.children,u,f,e);case T:g=8,u|=24;break;case w:return t=Ve(12,i,e,u|2),t.elementType=w,t.lanes=f,t;case k:return t=Ve(13,i,e,u),t.elementType=k,t.lanes=f,t;case $:return t=Ve(19,i,e,u),t.elementType=$,t.lanes=f,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case R:case U:g=10;break t;case V:g=9;break t;case K:g=11;break t;case at:g=14;break t;case Y:g=16,l=null;break t}g=29,i=Error(r(130,t===null?"null":typeof t,"")),l=null}return e=Ve(g,i,e,u),e.elementType=t,e.type=l,e.lanes=f,e}function ja(t,e,i,l){return t=Ve(7,t,l,e),t.lanes=i,t}function ku(t,e,i){return t=Ve(6,t,null,e),t.lanes=i,t}function Hu(t,e,i){return e=Ve(4,t.children!==null?t.children:[],t.key,e),e.lanes=i,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Di=[],Ui=0,Tl=null,Al=0,Ke=[],Fe=0,ka=null,On=1,Dn="";function Ha(t,e){Di[Ui++]=Al,Di[Ui++]=Tl,Tl=t,Al=e}function bm(t,e,i){Ke[Fe++]=On,Ke[Fe++]=Dn,Ke[Fe++]=ka,ka=t;var l=On;t=Dn;var u=32-_e(l)-1;l&=~(1<<u),i+=1;var f=32-_e(e)+u;if(30<f){var g=u-u%5;f=(l&(1<<g)-1).toString(32),l>>=g,u-=g,On=1<<32-_e(e)+u|i<<u|l,Dn=f+t}else On=1<<f|i<<u|l,Dn=t}function qu(t){t.return!==null&&(Ha(t,1),bm(t,1,0))}function Pu(t){for(;t===Tl;)Tl=Di[--Ui],Di[Ui]=null,Al=Di[--Ui],Di[Ui]=null;for(;t===ka;)ka=Ke[--Fe],Ke[Fe]=null,Dn=Ke[--Fe],Ke[Fe]=null,On=Ke[--Fe],Ke[Fe]=null}var be=null,Gt=null,Rt=!1,qa=null,dn=!1,Gu=Error(r(519));function Pa(t){var e=Error(r(418,""));throw Hs(Xe(e,t)),Gu}function Sm(t){var e=t.stateNode,i=t.type,l=t.memoizedProps;switch(e[de]=t,e[Ee]=l,i){case"dialog":St("cancel",e),St("close",e);break;case"iframe":case"object":case"embed":St("load",e);break;case"video":case"audio":for(i=0;i<cr.length;i++)St(cr[i],e);break;case"source":St("error",e);break;case"img":case"image":case"link":St("error",e),St("load",e);break;case"details":St("toggle",e);break;case"input":St("invalid",e),Lh(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ul(e);break;case"select":St("invalid",e);break;case"textarea":St("invalid",e),Bh(e,l.value,l.defaultValue,l.children),ul(e)}i=l.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||e.textContent===""+i||l.suppressHydrationWarning===!0||By(e.textContent,i)?(l.popover!=null&&(St("beforetoggle",e),St("toggle",e)),l.onScroll!=null&&St("scroll",e),l.onScrollEnd!=null&&St("scrollend",e),l.onClick!=null&&(e.onclick=ao),e=!0):e=!1,e||Pa(t)}function xm(t){for(be=t.return;be;)switch(be.tag){case 5:case 13:dn=!1;return;case 27:case 3:dn=!0;return;default:be=be.return}}function js(t){if(t!==be)return!1;if(!Rt)return xm(t),Rt=!0,!1;var e=t.tag,i;if((i=e!==3&&e!==27)&&((i=e===5)&&(i=t.type,i=!(i!=="form"&&i!=="button")||rf(t.type,t.memoizedProps)),i=!i),i&&Gt&&Pa(t),xm(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(i=t.data,i==="/$"){if(e===0){Gt=sn(t.nextSibling);break t}e--}else i!=="$"&&i!=="$!"&&i!=="$?"||e++;t=t.nextSibling}Gt=null}}else e===27?(e=Gt,ya(t.type)?(t=cf,cf=null,Gt=t):Gt=e):Gt=be?sn(t.stateNode.nextSibling):null;return!0}function ks(){Gt=be=null,Rt=!1}function Tm(){var t=qa;return t!==null&&(Ce===null?Ce=t:Ce.push.apply(Ce,t),qa=null),t}function Hs(t){qa===null?qa=[t]:qa.push(t)}var Yu=Q(null),Ga=null,Un=null;function ea(t,e,i){Z(Yu,e._currentValue),e._currentValue=i}function Nn(t){t._currentValue=Yu.current,W(Yu)}function Xu(t,e,i){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===i)break;t=t.return}}function Qu(t,e,i,l){var u=t.child;for(u!==null&&(u.return=t);u!==null;){var f=u.dependencies;if(f!==null){var g=u.child;f=f.firstContext;t:for(;f!==null;){var b=f;f=u;for(var A=0;A<e.length;A++)if(b.context===e[A]){f.lanes|=i,b=f.alternate,b!==null&&(b.lanes|=i),Xu(f.return,i,t),l||(g=null);break t}f=b.next}}else if(u.tag===18){if(g=u.return,g===null)throw Error(r(341));g.lanes|=i,f=g.alternate,f!==null&&(f.lanes|=i),Xu(g,i,t),g=null}else g=u.child;if(g!==null)g.return=u;else for(g=u;g!==null;){if(g===t){g=null;break}if(u=g.sibling,u!==null){u.return=g.return,g=u;break}g=g.return}u=g}}function qs(t,e,i,l){t=null;for(var u=e,f=!1;u!==null;){if(!f){if((u.flags&524288)!==0)f=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var g=u.alternate;if(g===null)throw Error(r(387));if(g=g.memoizedProps,g!==null){var b=u.type;Le(u.pendingProps.value,g.value)||(t!==null?t.push(b):t=[b])}}else if(u===At.current){if(g=u.alternate,g===null)throw Error(r(387));g.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(t!==null?t.push(yr):t=[yr])}u=u.return}t!==null&&Qu(e,t,i,l),e.flags|=262144}function El(t){for(t=t.firstContext;t!==null;){if(!Le(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Ya(t){Ga=t,Un=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function he(t){return Am(Ga,t)}function wl(t,e){return Ga===null&&Ya(t),Am(t,e)}function Am(t,e){var i=e._currentValue;if(e={context:e,memoizedValue:i,next:null},Un===null){if(t===null)throw Error(r(308));Un=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Un=Un.next=e;return i}var fx=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(i,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(i){return i()})}},dx=n.unstable_scheduleCallback,hx=n.unstable_NormalPriority,te={$$typeof:U,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ku(){return{controller:new fx,data:new Map,refCount:0}}function Ps(t){t.refCount--,t.refCount===0&&dx(hx,function(){t.controller.abort()})}var Gs=null,Fu=0,Ni=0,zi=null;function mx(t,e){if(Gs===null){var i=Gs=[];Fu=0,Ni=$c(),zi={status:"pending",value:void 0,then:function(l){i.push(l)}}}return Fu++,e.then(Em,Em),e}function Em(){if(--Fu===0&&Gs!==null){zi!==null&&(zi.status="fulfilled");var t=Gs;Gs=null,Ni=0,zi=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function px(t,e){var i=[],l={status:"pending",value:null,reason:null,then:function(u){i.push(u)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var u=0;u<i.length;u++)(0,i[u])(e)},function(u){for(l.status="rejected",l.reason=u,u=0;u<i.length;u++)(0,i[u])(void 0)}),l}var wm=j.S;j.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&mx(t,e),wm!==null&&wm(t,e)};var Xa=Q(null);function Zu(){var t=Xa.current;return t!==null?t:Lt.pooledCache}function Rl(t,e){e===null?Z(Xa,Xa.current):Z(Xa,e.pool)}function Rm(){var t=Zu();return t===null?null:{parent:te._currentValue,pool:t}}var Ys=Error(r(460)),Mm=Error(r(474)),Ml=Error(r(542)),$u={then:function(){}};function Cm(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Cl(){}function Om(t,e,i){switch(i=t[i],i===void 0?t.push(e):i!==e&&(e.then(Cl,Cl),e=i),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Um(t),t;default:if(typeof e.status=="string")e.then(Cl,Cl);else{if(t=Lt,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var u=e;u.status="fulfilled",u.value=l}},function(l){if(e.status==="pending"){var u=e;u.status="rejected",u.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Um(t),t}throw Xs=e,Ys}}var Xs=null;function Dm(){if(Xs===null)throw Error(r(459));var t=Xs;return Xs=null,t}function Um(t){if(t===Ys||t===Ml)throw Error(r(483))}var na=!1;function Ju(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Wu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function aa(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function ia(t,e,i){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(Mt&2)!==0){var u=l.pending;return u===null?e.next=e:(e.next=u.next,u.next=e),l.pending=e,e=Sl(t),gm(t,null,i),e}return bl(t,l,e,i),Sl(t)}function Qs(t,e,i){if(e=e.updateQueue,e!==null&&(e=e.shared,(i&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,i|=l,e.lanes=i,Eh(t,i)}}function Iu(t,e){var i=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,i===l)){var u=null,f=null;if(i=i.firstBaseUpdate,i!==null){do{var g={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};f===null?u=f=g:f=f.next=g,i=i.next}while(i!==null);f===null?u=f=e:f=f.next=e}else u=f=e;i={baseState:l.baseState,firstBaseUpdate:u,lastBaseUpdate:f,shared:l.shared,callbacks:l.callbacks},t.updateQueue=i;return}t=i.lastBaseUpdate,t===null?i.firstBaseUpdate=e:t.next=e,i.lastBaseUpdate=e}var tc=!1;function Ks(){if(tc){var t=zi;if(t!==null)throw t}}function Fs(t,e,i,l){tc=!1;var u=t.updateQueue;na=!1;var f=u.firstBaseUpdate,g=u.lastBaseUpdate,b=u.shared.pending;if(b!==null){u.shared.pending=null;var A=b,N=A.next;A.next=null,g===null?f=N:g.next=N,g=A;var H=t.alternate;H!==null&&(H=H.updateQueue,b=H.lastBaseUpdate,b!==g&&(b===null?H.firstBaseUpdate=N:b.next=N,H.lastBaseUpdate=A))}if(f!==null){var G=u.baseState;g=0,H=N=A=null,b=f;do{var _=b.lane&-536870913,L=_!==b.lane;if(L?(Tt&_)===_:(l&_)===_){_!==0&&_===Ni&&(tc=!0),H!==null&&(H=H.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});t:{var ot=t,rt=b;_=e;var Ut=i;switch(rt.tag){case 1:if(ot=rt.payload,typeof ot=="function"){G=ot.call(Ut,G,_);break t}G=ot;break t;case 3:ot.flags=ot.flags&-65537|128;case 0:if(ot=rt.payload,_=typeof ot=="function"?ot.call(Ut,G,_):ot,_==null)break t;G=y({},G,_);break t;case 2:na=!0}}_=b.callback,_!==null&&(t.flags|=64,L&&(t.flags|=8192),L=u.callbacks,L===null?u.callbacks=[_]:L.push(_))}else L={lane:_,tag:b.tag,payload:b.payload,callback:b.callback,next:null},H===null?(N=H=L,A=G):H=H.next=L,g|=_;if(b=b.next,b===null){if(b=u.shared.pending,b===null)break;L=b,b=L.next,L.next=null,u.lastBaseUpdate=L,u.shared.pending=null}}while(!0);H===null&&(A=G),u.baseState=A,u.firstBaseUpdate=N,u.lastBaseUpdate=H,f===null&&(u.shared.lanes=0),da|=g,t.lanes=g,t.memoizedState=G}}function Nm(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function zm(t,e){var i=t.callbacks;if(i!==null)for(t.callbacks=null,t=0;t<i.length;t++)Nm(i[t],e)}var _i=Q(null),Ol=Q(0);function _m(t,e){t=kn,Z(Ol,t),Z(_i,e),kn=t|e.baseLanes}function ec(){Z(Ol,kn),Z(_i,_i.current)}function nc(){kn=Ol.current,W(_i),W(Ol)}var sa=0,pt=null,Ot=null,Jt=null,Dl=!1,Li=!1,Qa=!1,Ul=0,Zs=0,Vi=null,yx=0;function Kt(){throw Error(r(321))}function ac(t,e){if(e===null)return!1;for(var i=0;i<e.length&&i<t.length;i++)if(!Le(t[i],e[i]))return!1;return!0}function ic(t,e,i,l,u,f){return sa=f,pt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,j.H=t===null||t.memoizedState===null?gp:vp,Qa=!1,f=i(l,u),Qa=!1,Li&&(f=Vm(e,i,l,u)),Lm(t),f}function Lm(t){j.H=Bl;var e=Ot!==null&&Ot.next!==null;if(sa=0,Jt=Ot=pt=null,Dl=!1,Zs=0,Vi=null,e)throw Error(r(300));t===null||se||(t=t.dependencies,t!==null&&El(t)&&(se=!0))}function Vm(t,e,i,l){pt=t;var u=0;do{if(Li&&(Vi=null),Zs=0,Li=!1,25<=u)throw Error(r(301));if(u+=1,Jt=Ot=null,t.updateQueue!=null){var f=t.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}j.H=Ax,f=e(i,l)}while(Li);return f}function gx(){var t=j.H,e=t.useState()[0];return e=typeof e.then=="function"?$s(e):e,t=t.useState()[0],(Ot!==null?Ot.memoizedState:null)!==t&&(pt.flags|=1024),e}function sc(){var t=Ul!==0;return Ul=0,t}function rc(t,e,i){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i}function lc(t){if(Dl){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Dl=!1}sa=0,Jt=Ot=pt=null,Li=!1,Zs=Ul=0,Vi=null}function Re(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Jt===null?pt.memoizedState=Jt=t:Jt=Jt.next=t,Jt}function Wt(){if(Ot===null){var t=pt.alternate;t=t!==null?t.memoizedState:null}else t=Ot.next;var e=Jt===null?pt.memoizedState:Jt.next;if(e!==null)Jt=e,Ot=t;else{if(t===null)throw pt.alternate===null?Error(r(467)):Error(r(310));Ot=t,t={memoizedState:Ot.memoizedState,baseState:Ot.baseState,baseQueue:Ot.baseQueue,queue:Ot.queue,next:null},Jt===null?pt.memoizedState=Jt=t:Jt=Jt.next=t}return Jt}function oc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function $s(t){var e=Zs;return Zs+=1,Vi===null&&(Vi=[]),t=Om(Vi,t,e),e=pt,(Jt===null?e.memoizedState:Jt.next)===null&&(e=e.alternate,j.H=e===null||e.memoizedState===null?gp:vp),t}function Nl(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return $s(t);if(t.$$typeof===U)return he(t)}throw Error(r(438,String(t)))}function uc(t){var e=null,i=pt.updateQueue;if(i!==null&&(e=i.memoCache),e==null){var l=pt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(u){return u.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),i===null&&(i=oc(),pt.updateQueue=i),i.memoCache=e,i=e.data[e.index],i===void 0)for(i=e.data[e.index]=Array(t),l=0;l<t;l++)i[l]=mt;return e.index++,i}function zn(t,e){return typeof e=="function"?e(t):e}function zl(t){var e=Wt();return cc(e,Ot,t)}function cc(t,e,i){var l=t.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=i;var u=t.baseQueue,f=l.pending;if(f!==null){if(u!==null){var g=u.next;u.next=f.next,f.next=g}e.baseQueue=u=f,l.pending=null}if(f=t.baseState,u===null)t.memoizedState=f;else{e=u.next;var b=g=null,A=null,N=e,H=!1;do{var G=N.lane&-536870913;if(G!==N.lane?(Tt&G)===G:(sa&G)===G){var _=N.revertLane;if(_===0)A!==null&&(A=A.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),G===Ni&&(H=!0);else if((sa&_)===_){N=N.next,_===Ni&&(H=!0);continue}else G={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},A===null?(b=A=G,g=f):A=A.next=G,pt.lanes|=_,da|=_;G=N.action,Qa&&i(f,G),f=N.hasEagerState?N.eagerState:i(f,G)}else _={lane:G,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},A===null?(b=A=_,g=f):A=A.next=_,pt.lanes|=G,da|=G;N=N.next}while(N!==null&&N!==e);if(A===null?g=f:A.next=b,!Le(f,t.memoizedState)&&(se=!0,H&&(i=zi,i!==null)))throw i;t.memoizedState=f,t.baseState=g,t.baseQueue=A,l.lastRenderedState=f}return u===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function fc(t){var e=Wt(),i=e.queue;if(i===null)throw Error(r(311));i.lastRenderedReducer=t;var l=i.dispatch,u=i.pending,f=e.memoizedState;if(u!==null){i.pending=null;var g=u=u.next;do f=t(f,g.action),g=g.next;while(g!==u);Le(f,e.memoizedState)||(se=!0),e.memoizedState=f,e.baseQueue===null&&(e.baseState=f),i.lastRenderedState=f}return[f,l]}function Bm(t,e,i){var l=pt,u=Wt(),f=Rt;if(f){if(i===void 0)throw Error(r(407));i=i()}else i=e();var g=!Le((Ot||u).memoizedState,i);g&&(u.memoizedState=i,se=!0),u=u.queue;var b=Hm.bind(null,l,u,t);if(Js(2048,8,b,[t]),u.getSnapshot!==e||g||Jt!==null&&Jt.memoizedState.tag&1){if(l.flags|=2048,Bi(9,_l(),km.bind(null,l,u,i,e),null),Lt===null)throw Error(r(349));f||(sa&124)!==0||jm(l,e,i)}return i}function jm(t,e,i){t.flags|=16384,t={getSnapshot:e,value:i},e=pt.updateQueue,e===null?(e=oc(),pt.updateQueue=e,e.stores=[t]):(i=e.stores,i===null?e.stores=[t]:i.push(t))}function km(t,e,i,l){e.value=i,e.getSnapshot=l,qm(e)&&Pm(t)}function Hm(t,e,i){return i(function(){qm(e)&&Pm(t)})}function qm(t){var e=t.getSnapshot;t=t.value;try{var i=e();return!Le(t,i)}catch{return!0}}function Pm(t){var e=Ci(t,2);e!==null&&qe(e,t,2)}function dc(t){var e=Re();if(typeof t=="function"){var i=t;if(t=i(),Qa){Wn(!0);try{i()}finally{Wn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:zn,lastRenderedState:t},e}function Gm(t,e,i,l){return t.baseState=i,cc(t,Ot,typeof l=="function"?l:zn)}function vx(t,e,i,l,u){if(Vl(t))throw Error(r(485));if(t=e.action,t!==null){var f={payload:u,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){f.listeners.push(g)}};j.T!==null?i(!0):f.isTransition=!1,l(f),i=e.pending,i===null?(f.next=e.pending=f,Ym(e,f)):(f.next=i.next,e.pending=i.next=f)}}function Ym(t,e){var i=e.action,l=e.payload,u=t.state;if(e.isTransition){var f=j.T,g={};j.T=g;try{var b=i(u,l),A=j.S;A!==null&&A(g,b),Xm(t,e,b)}catch(N){hc(t,e,N)}finally{j.T=f}}else try{f=i(u,l),Xm(t,e,f)}catch(N){hc(t,e,N)}}function Xm(t,e,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(l){Qm(t,e,l)},function(l){return hc(t,e,l)}):Qm(t,e,i)}function Qm(t,e,i){e.status="fulfilled",e.value=i,Km(e),t.state=i,e=t.pending,e!==null&&(i=e.next,i===e?t.pending=null:(i=i.next,e.next=i,Ym(t,i)))}function hc(t,e,i){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=i,Km(e),e=e.next;while(e!==l)}t.action=null}function Km(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Fm(t,e){return e}function Zm(t,e){if(Rt){var i=Lt.formState;if(i!==null){t:{var l=pt;if(Rt){if(Gt){e:{for(var u=Gt,f=dn;u.nodeType!==8;){if(!f){u=null;break e}if(u=sn(u.nextSibling),u===null){u=null;break e}}f=u.data,u=f==="F!"||f==="F"?u:null}if(u){Gt=sn(u.nextSibling),l=u.data==="F!";break t}}Pa(l)}l=!1}l&&(e=i[0])}}return i=Re(),i.memoizedState=i.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Fm,lastRenderedState:e},i.queue=l,i=mp.bind(null,pt,l),l.dispatch=i,l=dc(!1),f=vc.bind(null,pt,!1,l.queue),l=Re(),u={state:e,dispatch:null,action:t,pending:null},l.queue=u,i=vx.bind(null,pt,u,f,i),u.dispatch=i,l.memoizedState=t,[e,i,!1]}function $m(t){var e=Wt();return Jm(e,Ot,t)}function Jm(t,e,i){if(e=cc(t,e,Fm)[0],t=zl(zn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=$s(e)}catch(g){throw g===Ys?Ml:g}else l=e;e=Wt();var u=e.queue,f=u.dispatch;return i!==e.memoizedState&&(pt.flags|=2048,Bi(9,_l(),bx.bind(null,u,i),null)),[l,f,t]}function bx(t,e){t.action=e}function Wm(t){var e=Wt(),i=Ot;if(i!==null)return Jm(e,i,t);Wt(),e=e.memoizedState,i=Wt();var l=i.queue.dispatch;return i.memoizedState=t,[e,l,!1]}function Bi(t,e,i,l){return t={tag:t,create:i,deps:l,inst:e,next:null},e=pt.updateQueue,e===null&&(e=oc(),pt.updateQueue=e),i=e.lastEffect,i===null?e.lastEffect=t.next=t:(l=i.next,i.next=t,t.next=l,e.lastEffect=t),t}function _l(){return{destroy:void 0,resource:void 0}}function Im(){return Wt().memoizedState}function Ll(t,e,i,l){var u=Re();l=l===void 0?null:l,pt.flags|=t,u.memoizedState=Bi(1|e,_l(),i,l)}function Js(t,e,i,l){var u=Wt();l=l===void 0?null:l;var f=u.memoizedState.inst;Ot!==null&&l!==null&&ac(l,Ot.memoizedState.deps)?u.memoizedState=Bi(e,f,i,l):(pt.flags|=t,u.memoizedState=Bi(1|e,f,i,l))}function tp(t,e){Ll(8390656,8,t,e)}function ep(t,e){Js(2048,8,t,e)}function np(t,e){return Js(4,2,t,e)}function ap(t,e){return Js(4,4,t,e)}function ip(t,e){if(typeof e=="function"){t=t();var i=e(t);return function(){typeof i=="function"?i():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function sp(t,e,i){i=i!=null?i.concat([t]):null,Js(4,4,ip.bind(null,e,t),i)}function mc(){}function rp(t,e){var i=Wt();e=e===void 0?null:e;var l=i.memoizedState;return e!==null&&ac(e,l[1])?l[0]:(i.memoizedState=[t,e],t)}function lp(t,e){var i=Wt();e=e===void 0?null:e;var l=i.memoizedState;if(e!==null&&ac(e,l[1]))return l[0];if(l=t(),Qa){Wn(!0);try{t()}finally{Wn(!1)}}return i.memoizedState=[l,e],l}function pc(t,e,i){return i===void 0||(sa&1073741824)!==0?t.memoizedState=e:(t.memoizedState=i,t=cy(),pt.lanes|=t,da|=t,i)}function op(t,e,i,l){return Le(i,e)?i:_i.current!==null?(t=pc(t,i,l),Le(t,e)||(se=!0),t):(sa&42)===0?(se=!0,t.memoizedState=i):(t=cy(),pt.lanes|=t,da|=t,e)}function up(t,e,i,l,u){var f=F.p;F.p=f!==0&&8>f?f:8;var g=j.T,b={};j.T=b,vc(t,!1,e,i);try{var A=u(),N=j.S;if(N!==null&&N(b,A),A!==null&&typeof A=="object"&&typeof A.then=="function"){var H=px(A,l);Ws(t,e,H,He(t))}else Ws(t,e,l,He(t))}catch(G){Ws(t,e,{then:function(){},status:"rejected",reason:G},He())}finally{F.p=f,j.T=g}}function Sx(){}function yc(t,e,i,l){if(t.tag!==5)throw Error(r(476));var u=cp(t).queue;up(t,u,e,X,i===null?Sx:function(){return fp(t),i(l)})}function cp(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:X,baseState:X,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:zn,lastRenderedState:X},next:null};var i={};return e.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:zn,lastRenderedState:i},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function fp(t){var e=cp(t).next.queue;Ws(t,e,{},He())}function gc(){return he(yr)}function dp(){return Wt().memoizedState}function hp(){return Wt().memoizedState}function xx(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var i=He();t=aa(i);var l=ia(e,t,i);l!==null&&(qe(l,e,i),Qs(l,e,i)),e={cache:Ku()},t.payload=e;return}e=e.return}}function Tx(t,e,i){var l=He();i={lane:l,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},Vl(t)?pp(e,i):(i=Bu(t,e,i,l),i!==null&&(qe(i,t,l),yp(i,e,l)))}function mp(t,e,i){var l=He();Ws(t,e,i,l)}function Ws(t,e,i,l){var u={lane:l,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(Vl(t))pp(e,u);else{var f=t.alternate;if(t.lanes===0&&(f===null||f.lanes===0)&&(f=e.lastRenderedReducer,f!==null))try{var g=e.lastRenderedState,b=f(g,i);if(u.hasEagerState=!0,u.eagerState=b,Le(b,g))return bl(t,e,u,0),Lt===null&&vl(),!1}catch{}finally{}if(i=Bu(t,e,u,l),i!==null)return qe(i,t,l),yp(i,e,l),!0}return!1}function vc(t,e,i,l){if(l={lane:2,revertLane:$c(),action:l,hasEagerState:!1,eagerState:null,next:null},Vl(t)){if(e)throw Error(r(479))}else e=Bu(t,i,l,2),e!==null&&qe(e,t,2)}function Vl(t){var e=t.alternate;return t===pt||e!==null&&e===pt}function pp(t,e){Li=Dl=!0;var i=t.pending;i===null?e.next=e:(e.next=i.next,i.next=e),t.pending=e}function yp(t,e,i){if((i&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,i|=l,e.lanes=i,Eh(t,i)}}var Bl={readContext:he,use:Nl,useCallback:Kt,useContext:Kt,useEffect:Kt,useImperativeHandle:Kt,useLayoutEffect:Kt,useInsertionEffect:Kt,useMemo:Kt,useReducer:Kt,useRef:Kt,useState:Kt,useDebugValue:Kt,useDeferredValue:Kt,useTransition:Kt,useSyncExternalStore:Kt,useId:Kt,useHostTransitionStatus:Kt,useFormState:Kt,useActionState:Kt,useOptimistic:Kt,useMemoCache:Kt,useCacheRefresh:Kt},gp={readContext:he,use:Nl,useCallback:function(t,e){return Re().memoizedState=[t,e===void 0?null:e],t},useContext:he,useEffect:tp,useImperativeHandle:function(t,e,i){i=i!=null?i.concat([t]):null,Ll(4194308,4,ip.bind(null,e,t),i)},useLayoutEffect:function(t,e){return Ll(4194308,4,t,e)},useInsertionEffect:function(t,e){Ll(4,2,t,e)},useMemo:function(t,e){var i=Re();e=e===void 0?null:e;var l=t();if(Qa){Wn(!0);try{t()}finally{Wn(!1)}}return i.memoizedState=[l,e],l},useReducer:function(t,e,i){var l=Re();if(i!==void 0){var u=i(e);if(Qa){Wn(!0);try{i(e)}finally{Wn(!1)}}}else u=e;return l.memoizedState=l.baseState=u,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:u},l.queue=t,t=t.dispatch=Tx.bind(null,pt,t),[l.memoizedState,t]},useRef:function(t){var e=Re();return t={current:t},e.memoizedState=t},useState:function(t){t=dc(t);var e=t.queue,i=mp.bind(null,pt,e);return e.dispatch=i,[t.memoizedState,i]},useDebugValue:mc,useDeferredValue:function(t,e){var i=Re();return pc(i,t,e)},useTransition:function(){var t=dc(!1);return t=up.bind(null,pt,t.queue,!0,!1),Re().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,i){var l=pt,u=Re();if(Rt){if(i===void 0)throw Error(r(407));i=i()}else{if(i=e(),Lt===null)throw Error(r(349));(Tt&124)!==0||jm(l,e,i)}u.memoizedState=i;var f={value:i,getSnapshot:e};return u.queue=f,tp(Hm.bind(null,l,f,t),[t]),l.flags|=2048,Bi(9,_l(),km.bind(null,l,f,i,e),null),i},useId:function(){var t=Re(),e=Lt.identifierPrefix;if(Rt){var i=Dn,l=On;i=(l&~(1<<32-_e(l)-1)).toString(32)+i,e="«"+e+"R"+i,i=Ul++,0<i&&(e+="H"+i.toString(32)),e+="»"}else i=yx++,e="«"+e+"r"+i.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:gc,useFormState:Zm,useActionState:Zm,useOptimistic:function(t){var e=Re();e.memoizedState=e.baseState=t;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=i,e=vc.bind(null,pt,!0,i),i.dispatch=e,[t,e]},useMemoCache:uc,useCacheRefresh:function(){return Re().memoizedState=xx.bind(null,pt)}},vp={readContext:he,use:Nl,useCallback:rp,useContext:he,useEffect:ep,useImperativeHandle:sp,useInsertionEffect:np,useLayoutEffect:ap,useMemo:lp,useReducer:zl,useRef:Im,useState:function(){return zl(zn)},useDebugValue:mc,useDeferredValue:function(t,e){var i=Wt();return op(i,Ot.memoizedState,t,e)},useTransition:function(){var t=zl(zn)[0],e=Wt().memoizedState;return[typeof t=="boolean"?t:$s(t),e]},useSyncExternalStore:Bm,useId:dp,useHostTransitionStatus:gc,useFormState:$m,useActionState:$m,useOptimistic:function(t,e){var i=Wt();return Gm(i,Ot,t,e)},useMemoCache:uc,useCacheRefresh:hp},Ax={readContext:he,use:Nl,useCallback:rp,useContext:he,useEffect:ep,useImperativeHandle:sp,useInsertionEffect:np,useLayoutEffect:ap,useMemo:lp,useReducer:fc,useRef:Im,useState:function(){return fc(zn)},useDebugValue:mc,useDeferredValue:function(t,e){var i=Wt();return Ot===null?pc(i,t,e):op(i,Ot.memoizedState,t,e)},useTransition:function(){var t=fc(zn)[0],e=Wt().memoizedState;return[typeof t=="boolean"?t:$s(t),e]},useSyncExternalStore:Bm,useId:dp,useHostTransitionStatus:gc,useFormState:Wm,useActionState:Wm,useOptimistic:function(t,e){var i=Wt();return Ot!==null?Gm(i,Ot,t,e):(i.baseState=t,[t,i.queue.dispatch])},useMemoCache:uc,useCacheRefresh:hp},ji=null,Is=0;function jl(t){var e=Is;return Is+=1,ji===null&&(ji=[]),Om(ji,t,e)}function tr(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function kl(t,e){throw e.$$typeof===v?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function bp(t){var e=t._init;return e(t._payload)}function Sp(t){function e(O,C){if(t){var D=O.deletions;D===null?(O.deletions=[C],O.flags|=16):D.push(C)}}function i(O,C){if(!t)return null;for(;C!==null;)e(O,C),C=C.sibling;return null}function l(O){for(var C=new Map;O!==null;)O.key!==null?C.set(O.key,O):C.set(O.index,O),O=O.sibling;return C}function u(O,C){return O=Cn(O,C),O.index=0,O.sibling=null,O}function f(O,C,D){return O.index=D,t?(D=O.alternate,D!==null?(D=D.index,D<C?(O.flags|=67108866,C):D):(O.flags|=67108866,C)):(O.flags|=1048576,C)}function g(O){return t&&O.alternate===null&&(O.flags|=67108866),O}function b(O,C,D,q){return C===null||C.tag!==6?(C=ku(D,O.mode,q),C.return=O,C):(C=u(C,D),C.return=O,C)}function A(O,C,D,q){var tt=D.type;return tt===x?H(O,C,D.props.children,q,D.key):C!==null&&(C.elementType===tt||typeof tt=="object"&&tt!==null&&tt.$$typeof===Y&&bp(tt)===C.type)?(C=u(C,D.props),tr(C,D),C.return=O,C):(C=xl(D.type,D.key,D.props,null,O.mode,q),tr(C,D),C.return=O,C)}function N(O,C,D,q){return C===null||C.tag!==4||C.stateNode.containerInfo!==D.containerInfo||C.stateNode.implementation!==D.implementation?(C=Hu(D,O.mode,q),C.return=O,C):(C=u(C,D.children||[]),C.return=O,C)}function H(O,C,D,q,tt){return C===null||C.tag!==7?(C=ja(D,O.mode,q,tt),C.return=O,C):(C=u(C,D),C.return=O,C)}function G(O,C,D){if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return C=ku(""+C,O.mode,D),C.return=O,C;if(typeof C=="object"&&C!==null){switch(C.$$typeof){case S:return D=xl(C.type,C.key,C.props,null,O.mode,D),tr(D,C),D.return=O,D;case E:return C=Hu(C,O.mode,D),C.return=O,C;case Y:var q=C._init;return C=q(C._payload),G(O,C,D)}if(jt(C)||Bt(C))return C=ja(C,O.mode,D,null),C.return=O,C;if(typeof C.then=="function")return G(O,jl(C),D);if(C.$$typeof===U)return G(O,wl(O,C),D);kl(O,C)}return null}function _(O,C,D,q){var tt=C!==null?C.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return tt!==null?null:b(O,C,""+D,q);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case S:return D.key===tt?A(O,C,D,q):null;case E:return D.key===tt?N(O,C,D,q):null;case Y:return tt=D._init,D=tt(D._payload),_(O,C,D,q)}if(jt(D)||Bt(D))return tt!==null?null:H(O,C,D,q,null);if(typeof D.then=="function")return _(O,C,jl(D),q);if(D.$$typeof===U)return _(O,C,wl(O,D),q);kl(O,D)}return null}function L(O,C,D,q,tt){if(typeof q=="string"&&q!==""||typeof q=="number"||typeof q=="bigint")return O=O.get(D)||null,b(C,O,""+q,tt);if(typeof q=="object"&&q!==null){switch(q.$$typeof){case S:return O=O.get(q.key===null?D:q.key)||null,A(C,O,q,tt);case E:return O=O.get(q.key===null?D:q.key)||null,N(C,O,q,tt);case Y:var gt=q._init;return q=gt(q._payload),L(O,C,D,q,tt)}if(jt(q)||Bt(q))return O=O.get(D)||null,H(C,O,q,tt,null);if(typeof q.then=="function")return L(O,C,D,jl(q),tt);if(q.$$typeof===U)return L(O,C,D,wl(C,q),tt);kl(C,q)}return null}function ot(O,C,D,q){for(var tt=null,gt=null,st=C,lt=C=0,le=null;st!==null&&lt<D.length;lt++){st.index>lt?(le=st,st=null):le=st.sibling;var Et=_(O,st,D[lt],q);if(Et===null){st===null&&(st=le);break}t&&st&&Et.alternate===null&&e(O,st),C=f(Et,C,lt),gt===null?tt=Et:gt.sibling=Et,gt=Et,st=le}if(lt===D.length)return i(O,st),Rt&&Ha(O,lt),tt;if(st===null){for(;lt<D.length;lt++)st=G(O,D[lt],q),st!==null&&(C=f(st,C,lt),gt===null?tt=st:gt.sibling=st,gt=st);return Rt&&Ha(O,lt),tt}for(st=l(st);lt<D.length;lt++)le=L(st,O,lt,D[lt],q),le!==null&&(t&&le.alternate!==null&&st.delete(le.key===null?lt:le.key),C=f(le,C,lt),gt===null?tt=le:gt.sibling=le,gt=le);return t&&st.forEach(function(xa){return e(O,xa)}),Rt&&Ha(O,lt),tt}function rt(O,C,D,q){if(D==null)throw Error(r(151));for(var tt=null,gt=null,st=C,lt=C=0,le=null,Et=D.next();st!==null&&!Et.done;lt++,Et=D.next()){st.index>lt?(le=st,st=null):le=st.sibling;var xa=_(O,st,Et.value,q);if(xa===null){st===null&&(st=le);break}t&&st&&xa.alternate===null&&e(O,st),C=f(xa,C,lt),gt===null?tt=xa:gt.sibling=xa,gt=xa,st=le}if(Et.done)return i(O,st),Rt&&Ha(O,lt),tt;if(st===null){for(;!Et.done;lt++,Et=D.next())Et=G(O,Et.value,q),Et!==null&&(C=f(Et,C,lt),gt===null?tt=Et:gt.sibling=Et,gt=Et);return Rt&&Ha(O,lt),tt}for(st=l(st);!Et.done;lt++,Et=D.next())Et=L(st,O,lt,Et.value,q),Et!==null&&(t&&Et.alternate!==null&&st.delete(Et.key===null?lt:Et.key),C=f(Et,C,lt),gt===null?tt=Et:gt.sibling=Et,gt=Et);return t&&st.forEach(function(ET){return e(O,ET)}),Rt&&Ha(O,lt),tt}function Ut(O,C,D,q){if(typeof D=="object"&&D!==null&&D.type===x&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case S:t:{for(var tt=D.key;C!==null;){if(C.key===tt){if(tt=D.type,tt===x){if(C.tag===7){i(O,C.sibling),q=u(C,D.props.children),q.return=O,O=q;break t}}else if(C.elementType===tt||typeof tt=="object"&&tt!==null&&tt.$$typeof===Y&&bp(tt)===C.type){i(O,C.sibling),q=u(C,D.props),tr(q,D),q.return=O,O=q;break t}i(O,C);break}else e(O,C);C=C.sibling}D.type===x?(q=ja(D.props.children,O.mode,q,D.key),q.return=O,O=q):(q=xl(D.type,D.key,D.props,null,O.mode,q),tr(q,D),q.return=O,O=q)}return g(O);case E:t:{for(tt=D.key;C!==null;){if(C.key===tt)if(C.tag===4&&C.stateNode.containerInfo===D.containerInfo&&C.stateNode.implementation===D.implementation){i(O,C.sibling),q=u(C,D.children||[]),q.return=O,O=q;break t}else{i(O,C);break}else e(O,C);C=C.sibling}q=Hu(D,O.mode,q),q.return=O,O=q}return g(O);case Y:return tt=D._init,D=tt(D._payload),Ut(O,C,D,q)}if(jt(D))return ot(O,C,D,q);if(Bt(D)){if(tt=Bt(D),typeof tt!="function")throw Error(r(150));return D=tt.call(D),rt(O,C,D,q)}if(typeof D.then=="function")return Ut(O,C,jl(D),q);if(D.$$typeof===U)return Ut(O,C,wl(O,D),q);kl(O,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,C!==null&&C.tag===6?(i(O,C.sibling),q=u(C,D),q.return=O,O=q):(i(O,C),q=ku(D,O.mode,q),q.return=O,O=q),g(O)):i(O,C)}return function(O,C,D,q){try{Is=0;var tt=Ut(O,C,D,q);return ji=null,tt}catch(st){if(st===Ys||st===Ml)throw st;var gt=Ve(29,st,null,O.mode);return gt.lanes=q,gt.return=O,gt}finally{}}}var ki=Sp(!0),xp=Sp(!1),Ze=Q(null),hn=null;function ra(t){var e=t.alternate;Z(ee,ee.current&1),Z(Ze,t),hn===null&&(e===null||_i.current!==null||e.memoizedState!==null)&&(hn=t)}function Tp(t){if(t.tag===22){if(Z(ee,ee.current),Z(Ze,t),hn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(hn=t)}}else la()}function la(){Z(ee,ee.current),Z(Ze,Ze.current)}function _n(t){W(Ze),hn===t&&(hn=null),W(ee)}var ee=Q(0);function Hl(t){for(var e=t;e!==null;){if(e.tag===13){var i=e.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||uf(i)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function bc(t,e,i,l){e=t.memoizedState,i=i(l,e),i=i==null?e:y({},e,i),t.memoizedState=i,t.lanes===0&&(t.updateQueue.baseState=i)}var Sc={enqueueSetState:function(t,e,i){t=t._reactInternals;var l=He(),u=aa(l);u.payload=e,i!=null&&(u.callback=i),e=ia(t,u,l),e!==null&&(qe(e,t,l),Qs(e,t,l))},enqueueReplaceState:function(t,e,i){t=t._reactInternals;var l=He(),u=aa(l);u.tag=1,u.payload=e,i!=null&&(u.callback=i),e=ia(t,u,l),e!==null&&(qe(e,t,l),Qs(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var i=He(),l=aa(i);l.tag=2,e!=null&&(l.callback=e),e=ia(t,l,i),e!==null&&(qe(e,t,i),Qs(e,t,i))}};function Ap(t,e,i,l,u,f,g){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,f,g):e.prototype&&e.prototype.isPureReactComponent?!Vs(i,l)||!Vs(u,f):!0}function Ep(t,e,i,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(i,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(i,l),e.state!==t&&Sc.enqueueReplaceState(e,e.state,null)}function Ka(t,e){var i=e;if("ref"in e){i={};for(var l in e)l!=="ref"&&(i[l]=e[l])}if(t=t.defaultProps){i===e&&(i=y({},i));for(var u in t)i[u]===void 0&&(i[u]=t[u])}return i}var ql=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function wp(t){ql(t)}function Rp(t){console.error(t)}function Mp(t){ql(t)}function Pl(t,e){try{var i=t.onUncaughtError;i(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Cp(t,e,i){try{var l=t.onCaughtError;l(i.value,{componentStack:i.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function xc(t,e,i){return i=aa(i),i.tag=3,i.payload={element:null},i.callback=function(){Pl(t,e)},i}function Op(t){return t=aa(t),t.tag=3,t}function Dp(t,e,i,l){var u=i.type.getDerivedStateFromError;if(typeof u=="function"){var f=l.value;t.payload=function(){return u(f)},t.callback=function(){Cp(e,i,l)}}var g=i.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(t.callback=function(){Cp(e,i,l),typeof u!="function"&&(ha===null?ha=new Set([this]):ha.add(this));var b=l.stack;this.componentDidCatch(l.value,{componentStack:b!==null?b:""})})}function Ex(t,e,i,l,u){if(i.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=i.alternate,e!==null&&qs(e,i,u,!0),i=Ze.current,i!==null){switch(i.tag){case 13:return hn===null?Xc():i.alternate===null&&Yt===0&&(Yt=3),i.flags&=-257,i.flags|=65536,i.lanes=u,l===$u?i.flags|=16384:(e=i.updateQueue,e===null?i.updateQueue=new Set([l]):e.add(l),Kc(t,l,u)),!1;case 22:return i.flags|=65536,l===$u?i.flags|=16384:(e=i.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},i.updateQueue=e):(i=e.retryQueue,i===null?e.retryQueue=new Set([l]):i.add(l)),Kc(t,l,u)),!1}throw Error(r(435,i.tag))}return Kc(t,l,u),Xc(),!1}if(Rt)return e=Ze.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=u,l!==Gu&&(t=Error(r(422),{cause:l}),Hs(Xe(t,i)))):(l!==Gu&&(e=Error(r(423),{cause:l}),Hs(Xe(e,i))),t=t.current.alternate,t.flags|=65536,u&=-u,t.lanes|=u,l=Xe(l,i),u=xc(t.stateNode,l,u),Iu(t,u),Yt!==4&&(Yt=2)),!1;var f=Error(r(520),{cause:l});if(f=Xe(f,i),lr===null?lr=[f]:lr.push(f),Yt!==4&&(Yt=2),e===null)return!0;l=Xe(l,i),i=e;do{switch(i.tag){case 3:return i.flags|=65536,t=u&-u,i.lanes|=t,t=xc(i.stateNode,l,t),Iu(i,t),!1;case 1:if(e=i.type,f=i.stateNode,(i.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(ha===null||!ha.has(f))))return i.flags|=65536,u&=-u,i.lanes|=u,u=Op(u),Dp(u,t,i,l),Iu(i,u),!1}i=i.return}while(i!==null);return!1}var Up=Error(r(461)),se=!1;function oe(t,e,i,l){e.child=t===null?xp(e,null,i,l):ki(e,t.child,i,l)}function Np(t,e,i,l,u){i=i.render;var f=e.ref;if("ref"in l){var g={};for(var b in l)b!=="ref"&&(g[b]=l[b])}else g=l;return Ya(e),l=ic(t,e,i,g,f,u),b=sc(),t!==null&&!se?(rc(t,e,u),Ln(t,e,u)):(Rt&&b&&qu(e),e.flags|=1,oe(t,e,l,u),e.child)}function zp(t,e,i,l,u){if(t===null){var f=i.type;return typeof f=="function"&&!ju(f)&&f.defaultProps===void 0&&i.compare===null?(e.tag=15,e.type=f,_p(t,e,f,l,u)):(t=xl(i.type,null,l,e,e.mode,u),t.ref=e.ref,t.return=e,e.child=t)}if(f=t.child,!Oc(t,u)){var g=f.memoizedProps;if(i=i.compare,i=i!==null?i:Vs,i(g,l)&&t.ref===e.ref)return Ln(t,e,u)}return e.flags|=1,t=Cn(f,l),t.ref=e.ref,t.return=e,e.child=t}function _p(t,e,i,l,u){if(t!==null){var f=t.memoizedProps;if(Vs(f,l)&&t.ref===e.ref)if(se=!1,e.pendingProps=l=f,Oc(t,u))(t.flags&131072)!==0&&(se=!0);else return e.lanes=t.lanes,Ln(t,e,u)}return Tc(t,e,i,l,u)}function Lp(t,e,i){var l=e.pendingProps,u=l.children,f=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=f!==null?f.baseLanes|i:i,t!==null){for(u=e.child=t.child,f=0;u!==null;)f=f|u.lanes|u.childLanes,u=u.sibling;e.childLanes=f&~l}else e.childLanes=0,e.child=null;return Vp(t,e,l,i)}if((i&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Rl(e,f!==null?f.cachePool:null),f!==null?_m(e,f):ec(),Tp(e);else return e.lanes=e.childLanes=536870912,Vp(t,e,f!==null?f.baseLanes|i:i,i)}else f!==null?(Rl(e,f.cachePool),_m(e,f),la(),e.memoizedState=null):(t!==null&&Rl(e,null),ec(),la());return oe(t,e,u,i),e.child}function Vp(t,e,i,l){var u=Zu();return u=u===null?null:{parent:te._currentValue,pool:u},e.memoizedState={baseLanes:i,cachePool:u},t!==null&&Rl(e,null),ec(),Tp(e),t!==null&&qs(t,e,l,!0),null}function Gl(t,e){var i=e.ref;if(i===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof i!="function"&&typeof i!="object")throw Error(r(284));(t===null||t.ref!==i)&&(e.flags|=4194816)}}function Tc(t,e,i,l,u){return Ya(e),i=ic(t,e,i,l,void 0,u),l=sc(),t!==null&&!se?(rc(t,e,u),Ln(t,e,u)):(Rt&&l&&qu(e),e.flags|=1,oe(t,e,i,u),e.child)}function Bp(t,e,i,l,u,f){return Ya(e),e.updateQueue=null,i=Vm(e,l,i,u),Lm(t),l=sc(),t!==null&&!se?(rc(t,e,f),Ln(t,e,f)):(Rt&&l&&qu(e),e.flags|=1,oe(t,e,i,f),e.child)}function jp(t,e,i,l,u){if(Ya(e),e.stateNode===null){var f=Oi,g=i.contextType;typeof g=="object"&&g!==null&&(f=he(g)),f=new i(l,f),e.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=Sc,e.stateNode=f,f._reactInternals=e,f=e.stateNode,f.props=l,f.state=e.memoizedState,f.refs={},Ju(e),g=i.contextType,f.context=typeof g=="object"&&g!==null?he(g):Oi,f.state=e.memoizedState,g=i.getDerivedStateFromProps,typeof g=="function"&&(bc(e,i,g,l),f.state=e.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(g=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),g!==f.state&&Sc.enqueueReplaceState(f,f.state,null),Fs(e,l,f,u),Ks(),f.state=e.memoizedState),typeof f.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){f=e.stateNode;var b=e.memoizedProps,A=Ka(i,b);f.props=A;var N=f.context,H=i.contextType;g=Oi,typeof H=="object"&&H!==null&&(g=he(H));var G=i.getDerivedStateFromProps;H=typeof G=="function"||typeof f.getSnapshotBeforeUpdate=="function",b=e.pendingProps!==b,H||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(b||N!==g)&&Ep(e,f,l,g),na=!1;var _=e.memoizedState;f.state=_,Fs(e,l,f,u),Ks(),N=e.memoizedState,b||_!==N||na?(typeof G=="function"&&(bc(e,i,G,l),N=e.memoizedState),(A=na||Ap(e,i,A,l,_,N,g))?(H||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(e.flags|=4194308)):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=N),f.props=l,f.state=N,f.context=g,l=A):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{f=e.stateNode,Wu(t,e),g=e.memoizedProps,H=Ka(i,g),f.props=H,G=e.pendingProps,_=f.context,N=i.contextType,A=Oi,typeof N=="object"&&N!==null&&(A=he(N)),b=i.getDerivedStateFromProps,(N=typeof b=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==G||_!==A)&&Ep(e,f,l,A),na=!1,_=e.memoizedState,f.state=_,Fs(e,l,f,u),Ks();var L=e.memoizedState;g!==G||_!==L||na||t!==null&&t.dependencies!==null&&El(t.dependencies)?(typeof b=="function"&&(bc(e,i,b,l),L=e.memoizedState),(H=na||Ap(e,i,H,l,_,L,A)||t!==null&&t.dependencies!==null&&El(t.dependencies))?(N||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(l,L,A),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(l,L,A)),typeof f.componentDidUpdate=="function"&&(e.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof f.componentDidUpdate!="function"||g===t.memoizedProps&&_===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&_===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=L),f.props=l,f.state=L,f.context=A,l=H):(typeof f.componentDidUpdate!="function"||g===t.memoizedProps&&_===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&_===t.memoizedState||(e.flags|=1024),l=!1)}return f=l,Gl(t,e),l=(e.flags&128)!==0,f||l?(f=e.stateNode,i=l&&typeof i.getDerivedStateFromError!="function"?null:f.render(),e.flags|=1,t!==null&&l?(e.child=ki(e,t.child,null,u),e.child=ki(e,null,i,u)):oe(t,e,i,u),e.memoizedState=f.state,t=e.child):t=Ln(t,e,u),t}function kp(t,e,i,l){return ks(),e.flags|=256,oe(t,e,i,l),e.child}var Ac={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ec(t){return{baseLanes:t,cachePool:Rm()}}function wc(t,e,i){return t=t!==null?t.childLanes&~i:0,e&&(t|=$e),t}function Hp(t,e,i){var l=e.pendingProps,u=!1,f=(e.flags&128)!==0,g;if((g=f)||(g=t!==null&&t.memoizedState===null?!1:(ee.current&2)!==0),g&&(u=!0,e.flags&=-129),g=(e.flags&32)!==0,e.flags&=-33,t===null){if(Rt){if(u?ra(e):la(),Rt){var b=Gt,A;if(A=b){t:{for(A=b,b=dn;A.nodeType!==8;){if(!b){b=null;break t}if(A=sn(A.nextSibling),A===null){b=null;break t}}b=A}b!==null?(e.memoizedState={dehydrated:b,treeContext:ka!==null?{id:On,overflow:Dn}:null,retryLane:536870912,hydrationErrors:null},A=Ve(18,null,null,0),A.stateNode=b,A.return=e,e.child=A,be=e,Gt=null,A=!0):A=!1}A||Pa(e)}if(b=e.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return uf(b)?e.lanes=32:e.lanes=536870912,null;_n(e)}return b=l.children,l=l.fallback,u?(la(),u=e.mode,b=Yl({mode:"hidden",children:b},u),l=ja(l,u,i,null),b.return=e,l.return=e,b.sibling=l,e.child=b,u=e.child,u.memoizedState=Ec(i),u.childLanes=wc(t,g,i),e.memoizedState=Ac,l):(ra(e),Rc(e,b))}if(A=t.memoizedState,A!==null&&(b=A.dehydrated,b!==null)){if(f)e.flags&256?(ra(e),e.flags&=-257,e=Mc(t,e,i)):e.memoizedState!==null?(la(),e.child=t.child,e.flags|=128,e=null):(la(),u=l.fallback,b=e.mode,l=Yl({mode:"visible",children:l.children},b),u=ja(u,b,i,null),u.flags|=2,l.return=e,u.return=e,l.sibling=u,e.child=l,ki(e,t.child,null,i),l=e.child,l.memoizedState=Ec(i),l.childLanes=wc(t,g,i),e.memoizedState=Ac,e=u);else if(ra(e),uf(b)){if(g=b.nextSibling&&b.nextSibling.dataset,g)var N=g.dgst;g=N,l=Error(r(419)),l.stack="",l.digest=g,Hs({value:l,source:null,stack:null}),e=Mc(t,e,i)}else if(se||qs(t,e,i,!1),g=(i&t.childLanes)!==0,se||g){if(g=Lt,g!==null&&(l=i&-i,l=(l&42)!==0?1:uu(l),l=(l&(g.suspendedLanes|i))!==0?0:l,l!==0&&l!==A.retryLane))throw A.retryLane=l,Ci(t,l),qe(g,t,l),Up;b.data==="$?"||Xc(),e=Mc(t,e,i)}else b.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=A.treeContext,Gt=sn(b.nextSibling),be=e,Rt=!0,qa=null,dn=!1,t!==null&&(Ke[Fe++]=On,Ke[Fe++]=Dn,Ke[Fe++]=ka,On=t.id,Dn=t.overflow,ka=e),e=Rc(e,l.children),e.flags|=4096);return e}return u?(la(),u=l.fallback,b=e.mode,A=t.child,N=A.sibling,l=Cn(A,{mode:"hidden",children:l.children}),l.subtreeFlags=A.subtreeFlags&65011712,N!==null?u=Cn(N,u):(u=ja(u,b,i,null),u.flags|=2),u.return=e,l.return=e,l.sibling=u,e.child=l,l=u,u=e.child,b=t.child.memoizedState,b===null?b=Ec(i):(A=b.cachePool,A!==null?(N=te._currentValue,A=A.parent!==N?{parent:N,pool:N}:A):A=Rm(),b={baseLanes:b.baseLanes|i,cachePool:A}),u.memoizedState=b,u.childLanes=wc(t,g,i),e.memoizedState=Ac,l):(ra(e),i=t.child,t=i.sibling,i=Cn(i,{mode:"visible",children:l.children}),i.return=e,i.sibling=null,t!==null&&(g=e.deletions,g===null?(e.deletions=[t],e.flags|=16):g.push(t)),e.child=i,e.memoizedState=null,i)}function Rc(t,e){return e=Yl({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Yl(t,e){return t=Ve(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Mc(t,e,i){return ki(e,t.child,null,i),t=Rc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function qp(t,e,i){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Xu(t.return,e,i)}function Cc(t,e,i,l,u){var f=t.memoizedState;f===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:i,tailMode:u}:(f.isBackwards=e,f.rendering=null,f.renderingStartTime=0,f.last=l,f.tail=i,f.tailMode=u)}function Pp(t,e,i){var l=e.pendingProps,u=l.revealOrder,f=l.tail;if(oe(t,e,l.children,i),l=ee.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&qp(t,i,e);else if(t.tag===19)qp(t,i,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(Z(ee,l),u){case"forwards":for(i=e.child,u=null;i!==null;)t=i.alternate,t!==null&&Hl(t)===null&&(u=i),i=i.sibling;i=u,i===null?(u=e.child,e.child=null):(u=i.sibling,i.sibling=null),Cc(e,!1,u,i,f);break;case"backwards":for(i=null,u=e.child,e.child=null;u!==null;){if(t=u.alternate,t!==null&&Hl(t)===null){e.child=u;break}t=u.sibling,u.sibling=i,i=u,u=t}Cc(e,!0,i,null,f);break;case"together":Cc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Ln(t,e,i){if(t!==null&&(e.dependencies=t.dependencies),da|=e.lanes,(i&e.childLanes)===0)if(t!==null){if(qs(t,e,i,!1),(i&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,i=Cn(t,t.pendingProps),e.child=i,i.return=e;t.sibling!==null;)t=t.sibling,i=i.sibling=Cn(t,t.pendingProps),i.return=e;i.sibling=null}return e.child}function Oc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&El(t)))}function wx(t,e,i){switch(e.tag){case 3:Nt(e,e.stateNode.containerInfo),ea(e,te,t.memoizedState.cache),ks();break;case 27:case 5:Zn(e);break;case 4:Nt(e,e.stateNode.containerInfo);break;case 10:ea(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(ra(e),e.flags|=128,null):(i&e.child.childLanes)!==0?Hp(t,e,i):(ra(e),t=Ln(t,e,i),t!==null?t.sibling:null);ra(e);break;case 19:var u=(t.flags&128)!==0;if(l=(i&e.childLanes)!==0,l||(qs(t,e,i,!1),l=(i&e.childLanes)!==0),u){if(l)return Pp(t,e,i);e.flags|=128}if(u=e.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),Z(ee,ee.current),l)break;return null;case 22:case 23:return e.lanes=0,Lp(t,e,i);case 24:ea(e,te,t.memoizedState.cache)}return Ln(t,e,i)}function Gp(t,e,i){if(t!==null)if(t.memoizedProps!==e.pendingProps)se=!0;else{if(!Oc(t,i)&&(e.flags&128)===0)return se=!1,wx(t,e,i);se=(t.flags&131072)!==0}else se=!1,Rt&&(e.flags&1048576)!==0&&bm(e,Al,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,u=l._init;if(l=u(l._payload),e.type=l,typeof l=="function")ju(l)?(t=Ka(l,t),e.tag=1,e=jp(null,e,l,t,i)):(e.tag=0,e=Tc(null,e,l,t,i));else{if(l!=null){if(u=l.$$typeof,u===K){e.tag=11,e=Np(null,e,l,t,i);break t}else if(u===at){e.tag=14,e=zp(null,e,l,t,i);break t}}throw e=Ae(l)||l,Error(r(306,e,""))}}return e;case 0:return Tc(t,e,e.type,e.pendingProps,i);case 1:return l=e.type,u=Ka(l,e.pendingProps),jp(t,e,l,u,i);case 3:t:{if(Nt(e,e.stateNode.containerInfo),t===null)throw Error(r(387));l=e.pendingProps;var f=e.memoizedState;u=f.element,Wu(t,e),Fs(e,l,null,i);var g=e.memoizedState;if(l=g.cache,ea(e,te,l),l!==f.cache&&Qu(e,[te],i,!0),Ks(),l=g.element,f.isDehydrated)if(f={element:l,isDehydrated:!1,cache:g.cache},e.updateQueue.baseState=f,e.memoizedState=f,e.flags&256){e=kp(t,e,l,i);break t}else if(l!==u){u=Xe(Error(r(424)),e),Hs(u),e=kp(t,e,l,i);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Gt=sn(t.firstChild),be=e,Rt=!0,qa=null,dn=!0,i=xp(e,null,l,i),e.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling}else{if(ks(),l===u){e=Ln(t,e,i);break t}oe(t,e,l,i)}e=e.child}return e;case 26:return Gl(t,e),t===null?(i=Ky(e.type,null,e.pendingProps,null))?e.memoizedState=i:Rt||(i=e.type,t=e.pendingProps,l=io(ct.current).createElement(i),l[de]=e,l[Ee]=t,ce(l,i,t),ie(l),e.stateNode=l):e.memoizedState=Ky(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Zn(e),t===null&&Rt&&(l=e.stateNode=Yy(e.type,e.pendingProps,ct.current),be=e,dn=!0,u=Gt,ya(e.type)?(cf=u,Gt=sn(l.firstChild)):Gt=u),oe(t,e,e.pendingProps.children,i),Gl(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Rt&&((u=l=Gt)&&(l=Ix(l,e.type,e.pendingProps,dn),l!==null?(e.stateNode=l,be=e,Gt=sn(l.firstChild),dn=!1,u=!0):u=!1),u||Pa(e)),Zn(e),u=e.type,f=e.pendingProps,g=t!==null?t.memoizedProps:null,l=f.children,rf(u,f)?l=null:g!==null&&rf(u,g)&&(e.flags|=32),e.memoizedState!==null&&(u=ic(t,e,gx,null,null,i),yr._currentValue=u),Gl(t,e),oe(t,e,l,i),e.child;case 6:return t===null&&Rt&&((t=i=Gt)&&(i=tT(i,e.pendingProps,dn),i!==null?(e.stateNode=i,be=e,Gt=null,t=!0):t=!1),t||Pa(e)),null;case 13:return Hp(t,e,i);case 4:return Nt(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=ki(e,null,l,i):oe(t,e,l,i),e.child;case 11:return Np(t,e,e.type,e.pendingProps,i);case 7:return oe(t,e,e.pendingProps,i),e.child;case 8:return oe(t,e,e.pendingProps.children,i),e.child;case 12:return oe(t,e,e.pendingProps.children,i),e.child;case 10:return l=e.pendingProps,ea(e,e.type,l.value),oe(t,e,l.children,i),e.child;case 9:return u=e.type._context,l=e.pendingProps.children,Ya(e),u=he(u),l=l(u),e.flags|=1,oe(t,e,l,i),e.child;case 14:return zp(t,e,e.type,e.pendingProps,i);case 15:return _p(t,e,e.type,e.pendingProps,i);case 19:return Pp(t,e,i);case 31:return l=e.pendingProps,i=e.mode,l={mode:l.mode,children:l.children},t===null?(i=Yl(l,i),i.ref=e.ref,e.child=i,i.return=e,e=i):(i=Cn(t.child,l),i.ref=e.ref,e.child=i,i.return=e,e=i),e;case 22:return Lp(t,e,i);case 24:return Ya(e),l=he(te),t===null?(u=Zu(),u===null&&(u=Lt,f=Ku(),u.pooledCache=f,f.refCount++,f!==null&&(u.pooledCacheLanes|=i),u=f),e.memoizedState={parent:l,cache:u},Ju(e),ea(e,te,u)):((t.lanes&i)!==0&&(Wu(t,e),Fs(e,null,null,i),Ks()),u=t.memoizedState,f=e.memoizedState,u.parent!==l?(u={parent:l,cache:l},e.memoizedState=u,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=u),ea(e,te,l)):(l=f.cache,ea(e,te,l),l!==u.cache&&Qu(e,[te],i,!0))),oe(t,e,e.pendingProps.children,i),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function Vn(t){t.flags|=4}function Yp(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Wy(e)){if(e=Ze.current,e!==null&&((Tt&4194048)===Tt?hn!==null:(Tt&62914560)!==Tt&&(Tt&536870912)===0||e!==hn))throw Xs=$u,Mm;t.flags|=8192}}function Xl(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Th():536870912,t.lanes|=e,Gi|=e)}function er(t,e){if(!Rt)switch(t.tailMode){case"hidden":e=t.tail;for(var i=null;e!==null;)e.alternate!==null&&(i=e),e=e.sibling;i===null?t.tail=null:i.sibling=null;break;case"collapsed":i=t.tail;for(var l=null;i!==null;)i.alternate!==null&&(l=i),i=i.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Ht(t){var e=t.alternate!==null&&t.alternate.child===t.child,i=0,l=0;if(e)for(var u=t.child;u!==null;)i|=u.lanes|u.childLanes,l|=u.subtreeFlags&65011712,l|=u.flags&65011712,u.return=t,u=u.sibling;else for(u=t.child;u!==null;)i|=u.lanes|u.childLanes,l|=u.subtreeFlags,l|=u.flags,u.return=t,u=u.sibling;return t.subtreeFlags|=l,t.childLanes=i,e}function Rx(t,e,i){var l=e.pendingProps;switch(Pu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ht(e),null;case 1:return Ht(e),null;case 3:return i=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Nn(te),Ne(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(t===null||t.child===null)&&(js(e)?Vn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Tm())),Ht(e),null;case 26:return i=e.memoizedState,t===null?(Vn(e),i!==null?(Ht(e),Yp(e,i)):(Ht(e),e.flags&=-16777217)):i?i!==t.memoizedState?(Vn(e),Ht(e),Yp(e,i)):(Ht(e),e.flags&=-16777217):(t.memoizedProps!==l&&Vn(e),Ht(e),e.flags&=-16777217),null;case 27:$n(e),i=ct.current;var u=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&Vn(e);else{if(!l){if(e.stateNode===null)throw Error(r(166));return Ht(e),null}t=I.current,js(e)?Sm(e):(t=Yy(u,l,i),e.stateNode=t,Vn(e))}return Ht(e),null;case 5:if($n(e),i=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&Vn(e);else{if(!l){if(e.stateNode===null)throw Error(r(166));return Ht(e),null}if(t=I.current,js(e))Sm(e);else{switch(u=io(ct.current),t){case 1:t=u.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:t=u.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":t=u.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":t=u.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":t=u.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?u.createElement("select",{is:l.is}):u.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?u.createElement(i,{is:l.is}):u.createElement(i)}}t[de]=e,t[Ee]=l;t:for(u=e.child;u!==null;){if(u.tag===5||u.tag===6)t.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===e)break t;for(;u.sibling===null;){if(u.return===null||u.return===e)break t;u=u.return}u.sibling.return=u.return,u=u.sibling}e.stateNode=t;t:switch(ce(t,i,l),i){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Vn(e)}}return Ht(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&Vn(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(r(166));if(t=ct.current,js(e)){if(t=e.stateNode,i=e.memoizedProps,l=null,u=be,u!==null)switch(u.tag){case 27:case 5:l=u.memoizedProps}t[de]=e,t=!!(t.nodeValue===i||l!==null&&l.suppressHydrationWarning===!0||By(t.nodeValue,i)),t||Pa(e)}else t=io(t).createTextNode(l),t[de]=e,e.stateNode=t}return Ht(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(u=js(e),l!==null&&l.dehydrated!==null){if(t===null){if(!u)throw Error(r(318));if(u=e.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(r(317));u[de]=e}else ks(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ht(e),u=!1}else u=Tm(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=u),u=!0;if(!u)return e.flags&256?(_n(e),e):(_n(e),null)}if(_n(e),(e.flags&128)!==0)return e.lanes=i,e;if(i=l!==null,t=t!==null&&t.memoizedState!==null,i){l=e.child,u=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(u=l.alternate.memoizedState.cachePool.pool);var f=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(f=l.memoizedState.cachePool.pool),f!==u&&(l.flags|=2048)}return i!==t&&i&&(e.child.flags|=8192),Xl(e,e.updateQueue),Ht(e),null;case 4:return Ne(),t===null&&tf(e.stateNode.containerInfo),Ht(e),null;case 10:return Nn(e.type),Ht(e),null;case 19:if(W(ee),u=e.memoizedState,u===null)return Ht(e),null;if(l=(e.flags&128)!==0,f=u.rendering,f===null)if(l)er(u,!1);else{if(Yt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(f=Hl(t),f!==null){for(e.flags|=128,er(u,!1),t=f.updateQueue,e.updateQueue=t,Xl(e,t),e.subtreeFlags=0,t=i,i=e.child;i!==null;)vm(i,t),i=i.sibling;return Z(ee,ee.current&1|2),e.child}t=t.sibling}u.tail!==null&&fn()>Fl&&(e.flags|=128,l=!0,er(u,!1),e.lanes=4194304)}else{if(!l)if(t=Hl(f),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Xl(e,t),er(u,!0),u.tail===null&&u.tailMode==="hidden"&&!f.alternate&&!Rt)return Ht(e),null}else 2*fn()-u.renderingStartTime>Fl&&i!==536870912&&(e.flags|=128,l=!0,er(u,!1),e.lanes=4194304);u.isBackwards?(f.sibling=e.child,e.child=f):(t=u.last,t!==null?t.sibling=f:e.child=f,u.last=f)}return u.tail!==null?(e=u.tail,u.rendering=e,u.tail=e.sibling,u.renderingStartTime=fn(),e.sibling=null,t=ee.current,Z(ee,l?t&1|2:t&1),e):(Ht(e),null);case 22:case 23:return _n(e),nc(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(i&536870912)!==0&&(e.flags&128)===0&&(Ht(e),e.subtreeFlags&6&&(e.flags|=8192)):Ht(e),i=e.updateQueue,i!==null&&Xl(e,i.retryQueue),i=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==i&&(e.flags|=2048),t!==null&&W(Xa),null;case 24:return i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),Nn(te),Ht(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function Mx(t,e){switch(Pu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Nn(te),Ne(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return $n(e),null;case 13:if(_n(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));ks()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return W(ee),null;case 4:return Ne(),null;case 10:return Nn(e.type),null;case 22:case 23:return _n(e),nc(),t!==null&&W(Xa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Nn(te),null;case 25:return null;default:return null}}function Xp(t,e){switch(Pu(e),e.tag){case 3:Nn(te),Ne();break;case 26:case 27:case 5:$n(e);break;case 4:Ne();break;case 13:_n(e);break;case 19:W(ee);break;case 10:Nn(e.type);break;case 22:case 23:_n(e),nc(),t!==null&&W(Xa);break;case 24:Nn(te)}}function nr(t,e){try{var i=e.updateQueue,l=i!==null?i.lastEffect:null;if(l!==null){var u=l.next;i=u;do{if((i.tag&t)===t){l=void 0;var f=i.create,g=i.inst;l=f(),g.destroy=l}i=i.next}while(i!==u)}}catch(b){zt(e,e.return,b)}}function oa(t,e,i){try{var l=e.updateQueue,u=l!==null?l.lastEffect:null;if(u!==null){var f=u.next;l=f;do{if((l.tag&t)===t){var g=l.inst,b=g.destroy;if(b!==void 0){g.destroy=void 0,u=e;var A=i,N=b;try{N()}catch(H){zt(u,A,H)}}}l=l.next}while(l!==f)}}catch(H){zt(e,e.return,H)}}function Qp(t){var e=t.updateQueue;if(e!==null){var i=t.stateNode;try{zm(e,i)}catch(l){zt(t,t.return,l)}}}function Kp(t,e,i){i.props=Ka(t.type,t.memoizedProps),i.state=t.memoizedState;try{i.componentWillUnmount()}catch(l){zt(t,e,l)}}function ar(t,e){try{var i=t.ref;if(i!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof i=="function"?t.refCleanup=i(l):i.current=l}}catch(u){zt(t,e,u)}}function mn(t,e){var i=t.ref,l=t.refCleanup;if(i!==null)if(typeof l=="function")try{l()}catch(u){zt(t,e,u)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(u){zt(t,e,u)}else i.current=null}function Fp(t){var e=t.type,i=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":i.autoFocus&&l.focus();break t;case"img":i.src?l.src=i.src:i.srcSet&&(l.srcset=i.srcSet)}}catch(u){zt(t,t.return,u)}}function Dc(t,e,i){try{var l=t.stateNode;Fx(l,t.type,i,e),l[Ee]=e}catch(u){zt(t,t.return,u)}}function Zp(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&ya(t.type)||t.tag===4}function Uc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Zp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&ya(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Nc(t,e,i){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i).insertBefore(t,e):(e=i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i,e.appendChild(t),i=i._reactRootContainer,i!=null||e.onclick!==null||(e.onclick=ao));else if(l!==4&&(l===27&&ya(t.type)&&(i=t.stateNode,e=null),t=t.child,t!==null))for(Nc(t,e,i),t=t.sibling;t!==null;)Nc(t,e,i),t=t.sibling}function Ql(t,e,i){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?i.insertBefore(t,e):i.appendChild(t);else if(l!==4&&(l===27&&ya(t.type)&&(i=t.stateNode),t=t.child,t!==null))for(Ql(t,e,i),t=t.sibling;t!==null;)Ql(t,e,i),t=t.sibling}function $p(t){var e=t.stateNode,i=t.memoizedProps;try{for(var l=t.type,u=e.attributes;u.length;)e.removeAttributeNode(u[0]);ce(e,l,i),e[de]=t,e[Ee]=i}catch(f){zt(t,t.return,f)}}var Bn=!1,Ft=!1,zc=!1,Jp=typeof WeakSet=="function"?WeakSet:Set,re=null;function Cx(t,e){if(t=t.containerInfo,af=co,t=om(t),Uu(t)){if("selectionStart"in t)var i={start:t.selectionStart,end:t.selectionEnd};else t:{i=(i=t.ownerDocument)&&i.defaultView||window;var l=i.getSelection&&i.getSelection();if(l&&l.rangeCount!==0){i=l.anchorNode;var u=l.anchorOffset,f=l.focusNode;l=l.focusOffset;try{i.nodeType,f.nodeType}catch{i=null;break t}var g=0,b=-1,A=-1,N=0,H=0,G=t,_=null;e:for(;;){for(var L;G!==i||u!==0&&G.nodeType!==3||(b=g+u),G!==f||l!==0&&G.nodeType!==3||(A=g+l),G.nodeType===3&&(g+=G.nodeValue.length),(L=G.firstChild)!==null;)_=G,G=L;for(;;){if(G===t)break e;if(_===i&&++N===u&&(b=g),_===f&&++H===l&&(A=g),(L=G.nextSibling)!==null)break;G=_,_=G.parentNode}G=L}i=b===-1||A===-1?null:{start:b,end:A}}else i=null}i=i||{start:0,end:0}}else i=null;for(sf={focusedElem:t,selectionRange:i},co=!1,re=e;re!==null;)if(e=re,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,re=t;else for(;re!==null;){switch(e=re,f=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&f!==null){t=void 0,i=e,u=f.memoizedProps,f=f.memoizedState,l=i.stateNode;try{var ot=Ka(i.type,u,i.elementType===i.type);t=l.getSnapshotBeforeUpdate(ot,f),l.__reactInternalSnapshotBeforeUpdate=t}catch(rt){zt(i,i.return,rt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,i=t.nodeType,i===9)of(t);else if(i===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":of(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,re=t;break}re=e.return}}function Wp(t,e,i){var l=i.flags;switch(i.tag){case 0:case 11:case 15:ua(t,i),l&4&&nr(5,i);break;case 1:if(ua(t,i),l&4)if(t=i.stateNode,e===null)try{t.componentDidMount()}catch(g){zt(i,i.return,g)}else{var u=Ka(i.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(u,e,t.__reactInternalSnapshotBeforeUpdate)}catch(g){zt(i,i.return,g)}}l&64&&Qp(i),l&512&&ar(i,i.return);break;case 3:if(ua(t,i),l&64&&(t=i.updateQueue,t!==null)){if(e=null,i.child!==null)switch(i.child.tag){case 27:case 5:e=i.child.stateNode;break;case 1:e=i.child.stateNode}try{zm(t,e)}catch(g){zt(i,i.return,g)}}break;case 27:e===null&&l&4&&$p(i);case 26:case 5:ua(t,i),e===null&&l&4&&Fp(i),l&512&&ar(i,i.return);break;case 12:ua(t,i);break;case 13:ua(t,i),l&4&&ey(t,i),l&64&&(t=i.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(i=Bx.bind(null,i),eT(t,i))));break;case 22:if(l=i.memoizedState!==null||Bn,!l){e=e!==null&&e.memoizedState!==null||Ft,u=Bn;var f=Ft;Bn=l,(Ft=e)&&!f?ca(t,i,(i.subtreeFlags&8772)!==0):ua(t,i),Bn=u,Ft=f}break;case 30:break;default:ua(t,i)}}function Ip(t){var e=t.alternate;e!==null&&(t.alternate=null,Ip(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&du(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var kt=null,Me=!1;function jn(t,e,i){for(i=i.child;i!==null;)ty(t,e,i),i=i.sibling}function ty(t,e,i){if(ze&&typeof ze.onCommitFiberUnmount=="function")try{ze.onCommitFiberUnmount(Es,i)}catch{}switch(i.tag){case 26:Ft||mn(i,e),jn(t,e,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:Ft||mn(i,e);var l=kt,u=Me;ya(i.type)&&(kt=i.stateNode,Me=!1),jn(t,e,i),dr(i.stateNode),kt=l,Me=u;break;case 5:Ft||mn(i,e);case 6:if(l=kt,u=Me,kt=null,jn(t,e,i),kt=l,Me=u,kt!==null)if(Me)try{(kt.nodeType===9?kt.body:kt.nodeName==="HTML"?kt.ownerDocument.body:kt).removeChild(i.stateNode)}catch(f){zt(i,e,f)}else try{kt.removeChild(i.stateNode)}catch(f){zt(i,e,f)}break;case 18:kt!==null&&(Me?(t=kt,Py(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,i.stateNode),Sr(t)):Py(kt,i.stateNode));break;case 4:l=kt,u=Me,kt=i.stateNode.containerInfo,Me=!0,jn(t,e,i),kt=l,Me=u;break;case 0:case 11:case 14:case 15:Ft||oa(2,i,e),Ft||oa(4,i,e),jn(t,e,i);break;case 1:Ft||(mn(i,e),l=i.stateNode,typeof l.componentWillUnmount=="function"&&Kp(i,e,l)),jn(t,e,i);break;case 21:jn(t,e,i);break;case 22:Ft=(l=Ft)||i.memoizedState!==null,jn(t,e,i),Ft=l;break;default:jn(t,e,i)}}function ey(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Sr(t)}catch(i){zt(e,e.return,i)}}function Ox(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Jp),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Jp),e;default:throw Error(r(435,t.tag))}}function _c(t,e){var i=Ox(t);e.forEach(function(l){var u=jx.bind(null,t,l);i.has(l)||(i.add(l),l.then(u,u))})}function Be(t,e){var i=e.deletions;if(i!==null)for(var l=0;l<i.length;l++){var u=i[l],f=t,g=e,b=g;t:for(;b!==null;){switch(b.tag){case 27:if(ya(b.type)){kt=b.stateNode,Me=!1;break t}break;case 5:kt=b.stateNode,Me=!1;break t;case 3:case 4:kt=b.stateNode.containerInfo,Me=!0;break t}b=b.return}if(kt===null)throw Error(r(160));ty(f,g,u),kt=null,Me=!1,f=u.alternate,f!==null&&(f.return=null),u.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)ny(e,t),e=e.sibling}var an=null;function ny(t,e){var i=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Be(e,t),je(t),l&4&&(oa(3,t,t.return),nr(3,t),oa(5,t,t.return));break;case 1:Be(e,t),je(t),l&512&&(Ft||i===null||mn(i,i.return)),l&64&&Bn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(i=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=i===null?l:i.concat(l))));break;case 26:var u=an;if(Be(e,t),je(t),l&512&&(Ft||i===null||mn(i,i.return)),l&4){var f=i!==null?i.memoizedState:null;if(l=t.memoizedState,i===null)if(l===null)if(t.stateNode===null){t:{l=t.type,i=t.memoizedProps,u=u.ownerDocument||u;e:switch(l){case"title":f=u.getElementsByTagName("title")[0],(!f||f[Ms]||f[de]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=u.createElement(l),u.head.insertBefore(f,u.querySelector("head > title"))),ce(f,l,i),f[de]=t,ie(f),l=f;break t;case"link":var g=$y("link","href",u).get(l+(i.href||""));if(g){for(var b=0;b<g.length;b++)if(f=g[b],f.getAttribute("href")===(i.href==null||i.href===""?null:i.href)&&f.getAttribute("rel")===(i.rel==null?null:i.rel)&&f.getAttribute("title")===(i.title==null?null:i.title)&&f.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){g.splice(b,1);break e}}f=u.createElement(l),ce(f,l,i),u.head.appendChild(f);break;case"meta":if(g=$y("meta","content",u).get(l+(i.content||""))){for(b=0;b<g.length;b++)if(f=g[b],f.getAttribute("content")===(i.content==null?null:""+i.content)&&f.getAttribute("name")===(i.name==null?null:i.name)&&f.getAttribute("property")===(i.property==null?null:i.property)&&f.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&f.getAttribute("charset")===(i.charSet==null?null:i.charSet)){g.splice(b,1);break e}}f=u.createElement(l),ce(f,l,i),u.head.appendChild(f);break;default:throw Error(r(468,l))}f[de]=t,ie(f),l=f}t.stateNode=l}else Jy(u,t.type,t.stateNode);else t.stateNode=Zy(u,l,t.memoizedProps);else f!==l?(f===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):f.count--,l===null?Jy(u,t.type,t.stateNode):Zy(u,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Dc(t,t.memoizedProps,i.memoizedProps)}break;case 27:Be(e,t),je(t),l&512&&(Ft||i===null||mn(i,i.return)),i!==null&&l&4&&Dc(t,t.memoizedProps,i.memoizedProps);break;case 5:if(Be(e,t),je(t),l&512&&(Ft||i===null||mn(i,i.return)),t.flags&32){u=t.stateNode;try{xi(u,"")}catch(L){zt(t,t.return,L)}}l&4&&t.stateNode!=null&&(u=t.memoizedProps,Dc(t,u,i!==null?i.memoizedProps:u)),l&1024&&(zc=!0);break;case 6:if(Be(e,t),je(t),l&4){if(t.stateNode===null)throw Error(r(162));l=t.memoizedProps,i=t.stateNode;try{i.nodeValue=l}catch(L){zt(t,t.return,L)}}break;case 3:if(lo=null,u=an,an=so(e.containerInfo),Be(e,t),an=u,je(t),l&4&&i!==null&&i.memoizedState.isDehydrated)try{Sr(e.containerInfo)}catch(L){zt(t,t.return,L)}zc&&(zc=!1,ay(t));break;case 4:l=an,an=so(t.stateNode.containerInfo),Be(e,t),je(t),an=l;break;case 12:Be(e,t),je(t);break;case 13:Be(e,t),je(t),t.child.flags&8192&&t.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(Hc=fn()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,_c(t,l)));break;case 22:u=t.memoizedState!==null;var A=i!==null&&i.memoizedState!==null,N=Bn,H=Ft;if(Bn=N||u,Ft=H||A,Be(e,t),Ft=H,Bn=N,je(t),l&8192)t:for(e=t.stateNode,e._visibility=u?e._visibility&-2:e._visibility|1,u&&(i===null||A||Bn||Ft||Fa(t)),i=null,e=t;;){if(e.tag===5||e.tag===26){if(i===null){A=i=e;try{if(f=A.stateNode,u)g=f.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{b=A.stateNode;var G=A.memoizedProps.style,_=G!=null&&G.hasOwnProperty("display")?G.display:null;b.style.display=_==null||typeof _=="boolean"?"":(""+_).trim()}}catch(L){zt(A,A.return,L)}}}else if(e.tag===6){if(i===null){A=e;try{A.stateNode.nodeValue=u?"":A.memoizedProps}catch(L){zt(A,A.return,L)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;i===e&&(i=null),e=e.return}i===e&&(i=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(i=l.retryQueue,i!==null&&(l.retryQueue=null,_c(t,i))));break;case 19:Be(e,t),je(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,_c(t,l)));break;case 30:break;case 21:break;default:Be(e,t),je(t)}}function je(t){var e=t.flags;if(e&2){try{for(var i,l=t.return;l!==null;){if(Zp(l)){i=l;break}l=l.return}if(i==null)throw Error(r(160));switch(i.tag){case 27:var u=i.stateNode,f=Uc(t);Ql(t,f,u);break;case 5:var g=i.stateNode;i.flags&32&&(xi(g,""),i.flags&=-33);var b=Uc(t);Ql(t,b,g);break;case 3:case 4:var A=i.stateNode.containerInfo,N=Uc(t);Nc(t,N,A);break;default:throw Error(r(161))}}catch(H){zt(t,t.return,H)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function ay(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;ay(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ua(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Wp(t,e.alternate,e),e=e.sibling}function Fa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:oa(4,e,e.return),Fa(e);break;case 1:mn(e,e.return);var i=e.stateNode;typeof i.componentWillUnmount=="function"&&Kp(e,e.return,i),Fa(e);break;case 27:dr(e.stateNode);case 26:case 5:mn(e,e.return),Fa(e);break;case 22:e.memoizedState===null&&Fa(e);break;case 30:Fa(e);break;default:Fa(e)}t=t.sibling}}function ca(t,e,i){for(i=i&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,u=t,f=e,g=f.flags;switch(f.tag){case 0:case 11:case 15:ca(u,f,i),nr(4,f);break;case 1:if(ca(u,f,i),l=f,u=l.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(N){zt(l,l.return,N)}if(l=f,u=l.updateQueue,u!==null){var b=l.stateNode;try{var A=u.shared.hiddenCallbacks;if(A!==null)for(u.shared.hiddenCallbacks=null,u=0;u<A.length;u++)Nm(A[u],b)}catch(N){zt(l,l.return,N)}}i&&g&64&&Qp(f),ar(f,f.return);break;case 27:$p(f);case 26:case 5:ca(u,f,i),i&&l===null&&g&4&&Fp(f),ar(f,f.return);break;case 12:ca(u,f,i);break;case 13:ca(u,f,i),i&&g&4&&ey(u,f);break;case 22:f.memoizedState===null&&ca(u,f,i),ar(f,f.return);break;case 30:break;default:ca(u,f,i)}e=e.sibling}}function Lc(t,e){var i=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==i&&(t!=null&&t.refCount++,i!=null&&Ps(i))}function Vc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ps(t))}function pn(t,e,i,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)iy(t,e,i,l),e=e.sibling}function iy(t,e,i,l){var u=e.flags;switch(e.tag){case 0:case 11:case 15:pn(t,e,i,l),u&2048&&nr(9,e);break;case 1:pn(t,e,i,l);break;case 3:pn(t,e,i,l),u&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ps(t)));break;case 12:if(u&2048){pn(t,e,i,l),t=e.stateNode;try{var f=e.memoizedProps,g=f.id,b=f.onPostCommit;typeof b=="function"&&b(g,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(A){zt(e,e.return,A)}}else pn(t,e,i,l);break;case 13:pn(t,e,i,l);break;case 23:break;case 22:f=e.stateNode,g=e.alternate,e.memoizedState!==null?f._visibility&2?pn(t,e,i,l):ir(t,e):f._visibility&2?pn(t,e,i,l):(f._visibility|=2,Hi(t,e,i,l,(e.subtreeFlags&10256)!==0)),u&2048&&Lc(g,e);break;case 24:pn(t,e,i,l),u&2048&&Vc(e.alternate,e);break;default:pn(t,e,i,l)}}function Hi(t,e,i,l,u){for(u=u&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var f=t,g=e,b=i,A=l,N=g.flags;switch(g.tag){case 0:case 11:case 15:Hi(f,g,b,A,u),nr(8,g);break;case 23:break;case 22:var H=g.stateNode;g.memoizedState!==null?H._visibility&2?Hi(f,g,b,A,u):ir(f,g):(H._visibility|=2,Hi(f,g,b,A,u)),u&&N&2048&&Lc(g.alternate,g);break;case 24:Hi(f,g,b,A,u),u&&N&2048&&Vc(g.alternate,g);break;default:Hi(f,g,b,A,u)}e=e.sibling}}function ir(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var i=t,l=e,u=l.flags;switch(l.tag){case 22:ir(i,l),u&2048&&Lc(l.alternate,l);break;case 24:ir(i,l),u&2048&&Vc(l.alternate,l);break;default:ir(i,l)}e=e.sibling}}var sr=8192;function qi(t){if(t.subtreeFlags&sr)for(t=t.child;t!==null;)sy(t),t=t.sibling}function sy(t){switch(t.tag){case 26:qi(t),t.flags&sr&&t.memoizedState!==null&&mT(an,t.memoizedState,t.memoizedProps);break;case 5:qi(t);break;case 3:case 4:var e=an;an=so(t.stateNode.containerInfo),qi(t),an=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=sr,sr=16777216,qi(t),sr=e):qi(t));break;default:qi(t)}}function ry(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function rr(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var l=e[i];re=l,oy(l,t)}ry(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ly(t),t=t.sibling}function ly(t){switch(t.tag){case 0:case 11:case 15:rr(t),t.flags&2048&&oa(9,t,t.return);break;case 3:rr(t);break;case 12:rr(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Kl(t)):rr(t);break;default:rr(t)}}function Kl(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var l=e[i];re=l,oy(l,t)}ry(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:oa(8,e,e.return),Kl(e);break;case 22:i=e.stateNode,i._visibility&2&&(i._visibility&=-3,Kl(e));break;default:Kl(e)}t=t.sibling}}function oy(t,e){for(;re!==null;){var i=re;switch(i.tag){case 0:case 11:case 15:oa(8,i,e);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var l=i.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Ps(i.memoizedState.cache)}if(l=i.child,l!==null)l.return=i,re=l;else t:for(i=t;re!==null;){l=re;var u=l.sibling,f=l.return;if(Ip(l),l===i){re=null;break t}if(u!==null){u.return=f,re=u;break t}re=f}}}var Dx={getCacheForType:function(t){var e=he(te),i=e.data.get(t);return i===void 0&&(i=t(),e.data.set(t,i)),i}},Ux=typeof WeakMap=="function"?WeakMap:Map,Mt=0,Lt=null,bt=null,Tt=0,Ct=0,ke=null,fa=!1,Pi=!1,Bc=!1,kn=0,Yt=0,da=0,Za=0,jc=0,$e=0,Gi=0,lr=null,Ce=null,kc=!1,Hc=0,Fl=1/0,Zl=null,ha=null,ue=0,ma=null,Yi=null,Xi=0,qc=0,Pc=null,uy=null,or=0,Gc=null;function He(){if((Mt&2)!==0&&Tt!==0)return Tt&-Tt;if(j.T!==null){var t=Ni;return t!==0?t:$c()}return wh()}function cy(){$e===0&&($e=(Tt&536870912)===0||Rt?xh():536870912);var t=Ze.current;return t!==null&&(t.flags|=32),$e}function qe(t,e,i){(t===Lt&&(Ct===2||Ct===9)||t.cancelPendingCommit!==null)&&(Qi(t,0),pa(t,Tt,$e,!1)),Rs(t,i),((Mt&2)===0||t!==Lt)&&(t===Lt&&((Mt&2)===0&&(Za|=i),Yt===4&&pa(t,Tt,$e,!1)),yn(t))}function fy(t,e,i){if((Mt&6)!==0)throw Error(r(327));var l=!i&&(e&124)===0&&(e&t.expiredLanes)===0||ws(t,e),u=l?_x(t,e):Qc(t,e,!0),f=l;do{if(u===0){Pi&&!l&&pa(t,e,0,!1);break}else{if(i=t.current.alternate,f&&!Nx(i)){u=Qc(t,e,!1),f=!1;continue}if(u===2){if(f=e,t.errorRecoveryDisabledLanes&f)var g=0;else g=t.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){e=g;t:{var b=t;u=lr;var A=b.current.memoizedState.isDehydrated;if(A&&(Qi(b,g).flags|=256),g=Qc(b,g,!1),g!==2){if(Bc&&!A){b.errorRecoveryDisabledLanes|=f,Za|=f,u=4;break t}f=Ce,Ce=u,f!==null&&(Ce===null?Ce=f:Ce.push.apply(Ce,f))}u=g}if(f=!1,u!==2)continue}}if(u===1){Qi(t,0),pa(t,e,0,!0);break}t:{switch(l=t,f=u,f){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:pa(l,e,$e,!fa);break t;case 2:Ce=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(u=Hc+300-fn(),10<u)){if(pa(l,e,$e,!fa),rl(l,0,!0)!==0)break t;l.timeoutHandle=Hy(dy.bind(null,l,i,Ce,Zl,kc,e,$e,Za,Gi,fa,f,2,-0,0),u);break t}dy(l,i,Ce,Zl,kc,e,$e,Za,Gi,fa,f,0,-0,0)}}break}while(!0);yn(t)}function dy(t,e,i,l,u,f,g,b,A,N,H,G,_,L){if(t.timeoutHandle=-1,G=e.subtreeFlags,(G&8192||(G&16785408)===16785408)&&(pr={stylesheets:null,count:0,unsuspend:hT},sy(e),G=pT(),G!==null)){t.cancelPendingCommit=G(by.bind(null,t,e,f,i,l,u,g,b,A,H,1,_,L)),pa(t,f,g,!N);return}by(t,e,f,i,l,u,g,b,A)}function Nx(t){for(var e=t;;){var i=e.tag;if((i===0||i===11||i===15)&&e.flags&16384&&(i=e.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var l=0;l<i.length;l++){var u=i[l],f=u.getSnapshot;u=u.value;try{if(!Le(f(),u))return!1}catch{return!1}}if(i=e.child,e.subtreeFlags&16384&&i!==null)i.return=e,e=i;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function pa(t,e,i,l){e&=~jc,e&=~Za,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var u=e;0<u;){var f=31-_e(u),g=1<<f;l[f]=-1,u&=~g}i!==0&&Ah(t,i,e)}function $l(){return(Mt&6)===0?(ur(0),!1):!0}function Yc(){if(bt!==null){if(Ct===0)var t=bt.return;else t=bt,Un=Ga=null,lc(t),ji=null,Is=0,t=bt;for(;t!==null;)Xp(t.alternate,t),t=t.return;bt=null}}function Qi(t,e){var i=t.timeoutHandle;i!==-1&&(t.timeoutHandle=-1,$x(i)),i=t.cancelPendingCommit,i!==null&&(t.cancelPendingCommit=null,i()),Yc(),Lt=t,bt=i=Cn(t.current,null),Tt=e,Ct=0,ke=null,fa=!1,Pi=ws(t,e),Bc=!1,Gi=$e=jc=Za=da=Yt=0,Ce=lr=null,kc=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var u=31-_e(l),f=1<<u;e|=t[u],l&=~f}return kn=e,vl(),i}function hy(t,e){pt=null,j.H=Bl,e===Ys||e===Ml?(e=Dm(),Ct=3):e===Mm?(e=Dm(),Ct=4):Ct=e===Up?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ke=e,bt===null&&(Yt=1,Pl(t,Xe(e,t.current)))}function my(){var t=j.H;return j.H=Bl,t===null?Bl:t}function py(){var t=j.A;return j.A=Dx,t}function Xc(){Yt=4,fa||(Tt&4194048)!==Tt&&Ze.current!==null||(Pi=!0),(da&134217727)===0&&(Za&134217727)===0||Lt===null||pa(Lt,Tt,$e,!1)}function Qc(t,e,i){var l=Mt;Mt|=2;var u=my(),f=py();(Lt!==t||Tt!==e)&&(Zl=null,Qi(t,e)),e=!1;var g=Yt;t:do try{if(Ct!==0&&bt!==null){var b=bt,A=ke;switch(Ct){case 8:Yc(),g=6;break t;case 3:case 2:case 9:case 6:Ze.current===null&&(e=!0);var N=Ct;if(Ct=0,ke=null,Ki(t,b,A,N),i&&Pi){g=0;break t}break;default:N=Ct,Ct=0,ke=null,Ki(t,b,A,N)}}zx(),g=Yt;break}catch(H){hy(t,H)}while(!0);return e&&t.shellSuspendCounter++,Un=Ga=null,Mt=l,j.H=u,j.A=f,bt===null&&(Lt=null,Tt=0,vl()),g}function zx(){for(;bt!==null;)yy(bt)}function _x(t,e){var i=Mt;Mt|=2;var l=my(),u=py();Lt!==t||Tt!==e?(Zl=null,Fl=fn()+500,Qi(t,e)):Pi=ws(t,e);t:do try{if(Ct!==0&&bt!==null){e=bt;var f=ke;e:switch(Ct){case 1:Ct=0,ke=null,Ki(t,e,f,1);break;case 2:case 9:if(Cm(f)){Ct=0,ke=null,gy(e);break}e=function(){Ct!==2&&Ct!==9||Lt!==t||(Ct=7),yn(t)},f.then(e,e);break t;case 3:Ct=7;break t;case 4:Ct=5;break t;case 7:Cm(f)?(Ct=0,ke=null,gy(e)):(Ct=0,ke=null,Ki(t,e,f,7));break;case 5:var g=null;switch(bt.tag){case 26:g=bt.memoizedState;case 5:case 27:var b=bt;if(!g||Wy(g)){Ct=0,ke=null;var A=b.sibling;if(A!==null)bt=A;else{var N=b.return;N!==null?(bt=N,Jl(N)):bt=null}break e}}Ct=0,ke=null,Ki(t,e,f,5);break;case 6:Ct=0,ke=null,Ki(t,e,f,6);break;case 8:Yc(),Yt=6;break t;default:throw Error(r(462))}}Lx();break}catch(H){hy(t,H)}while(!0);return Un=Ga=null,j.H=l,j.A=u,Mt=i,bt!==null?0:(Lt=null,Tt=0,vl(),Yt)}function Lx(){for(;bt!==null&&!a1();)yy(bt)}function yy(t){var e=Gp(t.alternate,t,kn);t.memoizedProps=t.pendingProps,e===null?Jl(t):bt=e}function gy(t){var e=t,i=e.alternate;switch(e.tag){case 15:case 0:e=Bp(i,e,e.pendingProps,e.type,void 0,Tt);break;case 11:e=Bp(i,e,e.pendingProps,e.type.render,e.ref,Tt);break;case 5:lc(e);default:Xp(i,e),e=bt=vm(e,kn),e=Gp(i,e,kn)}t.memoizedProps=t.pendingProps,e===null?Jl(t):bt=e}function Ki(t,e,i,l){Un=Ga=null,lc(e),ji=null,Is=0;var u=e.return;try{if(Ex(t,u,e,i,Tt)){Yt=1,Pl(t,Xe(i,t.current)),bt=null;return}}catch(f){if(u!==null)throw bt=u,f;Yt=1,Pl(t,Xe(i,t.current)),bt=null;return}e.flags&32768?(Rt||l===1?t=!0:Pi||(Tt&536870912)!==0?t=!1:(fa=t=!0,(l===2||l===9||l===3||l===6)&&(l=Ze.current,l!==null&&l.tag===13&&(l.flags|=16384))),vy(e,t)):Jl(e)}function Jl(t){var e=t;do{if((e.flags&32768)!==0){vy(e,fa);return}t=e.return;var i=Rx(e.alternate,e,kn);if(i!==null){bt=i;return}if(e=e.sibling,e!==null){bt=e;return}bt=e=t}while(e!==null);Yt===0&&(Yt=5)}function vy(t,e){do{var i=Mx(t.alternate,t);if(i!==null){i.flags&=32767,bt=i;return}if(i=t.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!e&&(t=t.sibling,t!==null)){bt=t;return}bt=t=i}while(t!==null);Yt=6,bt=null}function by(t,e,i,l,u,f,g,b,A){t.cancelPendingCommit=null;do Wl();while(ue!==0);if((Mt&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(f=e.lanes|e.childLanes,f|=Vu,h1(t,i,f,g,b,A),t===Lt&&(bt=Lt=null,Tt=0),Yi=e,ma=t,Xi=i,qc=f,Pc=u,uy=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,kx(al,function(){return Ey(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=j.T,j.T=null,u=F.p,F.p=2,g=Mt,Mt|=4;try{Cx(t,e,i)}finally{Mt=g,F.p=u,j.T=l}}ue=1,Sy(),xy(),Ty()}}function Sy(){if(ue===1){ue=0;var t=ma,e=Yi,i=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||i){i=j.T,j.T=null;var l=F.p;F.p=2;var u=Mt;Mt|=4;try{ny(e,t);var f=sf,g=om(t.containerInfo),b=f.focusedElem,A=f.selectionRange;if(g!==b&&b&&b.ownerDocument&&lm(b.ownerDocument.documentElement,b)){if(A!==null&&Uu(b)){var N=A.start,H=A.end;if(H===void 0&&(H=N),"selectionStart"in b)b.selectionStart=N,b.selectionEnd=Math.min(H,b.value.length);else{var G=b.ownerDocument||document,_=G&&G.defaultView||window;if(_.getSelection){var L=_.getSelection(),ot=b.textContent.length,rt=Math.min(A.start,ot),Ut=A.end===void 0?rt:Math.min(A.end,ot);!L.extend&&rt>Ut&&(g=Ut,Ut=rt,rt=g);var O=rm(b,rt),C=rm(b,Ut);if(O&&C&&(L.rangeCount!==1||L.anchorNode!==O.node||L.anchorOffset!==O.offset||L.focusNode!==C.node||L.focusOffset!==C.offset)){var D=G.createRange();D.setStart(O.node,O.offset),L.removeAllRanges(),rt>Ut?(L.addRange(D),L.extend(C.node,C.offset)):(D.setEnd(C.node,C.offset),L.addRange(D))}}}}for(G=[],L=b;L=L.parentNode;)L.nodeType===1&&G.push({element:L,left:L.scrollLeft,top:L.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<G.length;b++){var q=G[b];q.element.scrollLeft=q.left,q.element.scrollTop=q.top}}co=!!af,sf=af=null}finally{Mt=u,F.p=l,j.T=i}}t.current=e,ue=2}}function xy(){if(ue===2){ue=0;var t=ma,e=Yi,i=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||i){i=j.T,j.T=null;var l=F.p;F.p=2;var u=Mt;Mt|=4;try{Wp(t,e.alternate,e)}finally{Mt=u,F.p=l,j.T=i}}ue=3}}function Ty(){if(ue===4||ue===3){ue=0,i1();var t=ma,e=Yi,i=Xi,l=uy;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ue=5:(ue=0,Yi=ma=null,Ay(t,t.pendingLanes));var u=t.pendingLanes;if(u===0&&(ha=null),cu(i),e=e.stateNode,ze&&typeof ze.onCommitFiberRoot=="function")try{ze.onCommitFiberRoot(Es,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=j.T,u=F.p,F.p=2,j.T=null;try{for(var f=t.onRecoverableError,g=0;g<l.length;g++){var b=l[g];f(b.value,{componentStack:b.stack})}}finally{j.T=e,F.p=u}}(Xi&3)!==0&&Wl(),yn(t),u=t.pendingLanes,(i&4194090)!==0&&(u&42)!==0?t===Gc?or++:(or=0,Gc=t):or=0,ur(0)}}function Ay(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Ps(e)))}function Wl(t){return Sy(),xy(),Ty(),Ey()}function Ey(){if(ue!==5)return!1;var t=ma,e=qc;qc=0;var i=cu(Xi),l=j.T,u=F.p;try{F.p=32>i?32:i,j.T=null,i=Pc,Pc=null;var f=ma,g=Xi;if(ue=0,Yi=ma=null,Xi=0,(Mt&6)!==0)throw Error(r(331));var b=Mt;if(Mt|=4,ly(f.current),iy(f,f.current,g,i),Mt=b,ur(0,!1),ze&&typeof ze.onPostCommitFiberRoot=="function")try{ze.onPostCommitFiberRoot(Es,f)}catch{}return!0}finally{F.p=u,j.T=l,Ay(t,e)}}function wy(t,e,i){e=Xe(i,e),e=xc(t.stateNode,e,2),t=ia(t,e,2),t!==null&&(Rs(t,2),yn(t))}function zt(t,e,i){if(t.tag===3)wy(t,t,i);else for(;e!==null;){if(e.tag===3){wy(e,t,i);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(ha===null||!ha.has(l))){t=Xe(i,t),i=Op(2),l=ia(e,i,2),l!==null&&(Dp(i,l,e,t),Rs(l,2),yn(l));break}}e=e.return}}function Kc(t,e,i){var l=t.pingCache;if(l===null){l=t.pingCache=new Ux;var u=new Set;l.set(e,u)}else u=l.get(e),u===void 0&&(u=new Set,l.set(e,u));u.has(i)||(Bc=!0,u.add(i),t=Vx.bind(null,t,e,i),e.then(t,t))}function Vx(t,e,i){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&i,t.warmLanes&=~i,Lt===t&&(Tt&i)===i&&(Yt===4||Yt===3&&(Tt&62914560)===Tt&&300>fn()-Hc?(Mt&2)===0&&Qi(t,0):jc|=i,Gi===Tt&&(Gi=0)),yn(t)}function Ry(t,e){e===0&&(e=Th()),t=Ci(t,e),t!==null&&(Rs(t,e),yn(t))}function Bx(t){var e=t.memoizedState,i=0;e!==null&&(i=e.retryLane),Ry(t,i)}function jx(t,e){var i=0;switch(t.tag){case 13:var l=t.stateNode,u=t.memoizedState;u!==null&&(i=u.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(r(314))}l!==null&&l.delete(e),Ry(t,i)}function kx(t,e){return ru(t,e)}var Il=null,Fi=null,Fc=!1,to=!1,Zc=!1,$a=0;function yn(t){t!==Fi&&t.next===null&&(Fi===null?Il=Fi=t:Fi=Fi.next=t),to=!0,Fc||(Fc=!0,qx())}function ur(t,e){if(!Zc&&to){Zc=!0;do for(var i=!1,l=Il;l!==null;){if(t!==0){var u=l.pendingLanes;if(u===0)var f=0;else{var g=l.suspendedLanes,b=l.pingedLanes;f=(1<<31-_e(42|t)+1)-1,f&=u&~(g&~b),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(i=!0,Dy(l,f))}else f=Tt,f=rl(l,l===Lt?f:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(f&3)===0||ws(l,f)||(i=!0,Dy(l,f));l=l.next}while(i);Zc=!1}}function Hx(){My()}function My(){to=Fc=!1;var t=0;$a!==0&&(Zx()&&(t=$a),$a=0);for(var e=fn(),i=null,l=Il;l!==null;){var u=l.next,f=Cy(l,e);f===0?(l.next=null,i===null?Il=u:i.next=u,u===null&&(Fi=i)):(i=l,(t!==0||(f&3)!==0)&&(to=!0)),l=u}ur(t)}function Cy(t,e){for(var i=t.suspendedLanes,l=t.pingedLanes,u=t.expirationTimes,f=t.pendingLanes&-62914561;0<f;){var g=31-_e(f),b=1<<g,A=u[g];A===-1?((b&i)===0||(b&l)!==0)&&(u[g]=d1(b,e)):A<=e&&(t.expiredLanes|=b),f&=~b}if(e=Lt,i=Tt,i=rl(t,t===e?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,i===0||t===e&&(Ct===2||Ct===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&lu(l),t.callbackNode=null,t.callbackPriority=0;if((i&3)===0||ws(t,i)){if(e=i&-i,e===t.callbackPriority)return e;switch(l!==null&&lu(l),cu(i)){case 2:case 8:i=bh;break;case 32:i=al;break;case 268435456:i=Sh;break;default:i=al}return l=Oy.bind(null,t),i=ru(i,l),t.callbackPriority=e,t.callbackNode=i,e}return l!==null&&l!==null&&lu(l),t.callbackPriority=2,t.callbackNode=null,2}function Oy(t,e){if(ue!==0&&ue!==5)return t.callbackNode=null,t.callbackPriority=0,null;var i=t.callbackNode;if(Wl()&&t.callbackNode!==i)return null;var l=Tt;return l=rl(t,t===Lt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(fy(t,l,e),Cy(t,fn()),t.callbackNode!=null&&t.callbackNode===i?Oy.bind(null,t):null)}function Dy(t,e){if(Wl())return null;fy(t,e,!0)}function qx(){Jx(function(){(Mt&6)!==0?ru(vh,Hx):My()})}function $c(){return $a===0&&($a=xh()),$a}function Uy(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:fl(""+t)}function Ny(t,e){var i=e.ownerDocument.createElement("input");return i.name=e.name,i.value=e.value,t.id&&i.setAttribute("form",t.id),e.parentNode.insertBefore(i,e),t=new FormData(t),i.parentNode.removeChild(i),t}function Px(t,e,i,l,u){if(e==="submit"&&i&&i.stateNode===u){var f=Uy((u[Ee]||null).action),g=l.submitter;g&&(e=(e=g[Ee]||null)?Uy(e.formAction):g.getAttribute("formAction"),e!==null&&(f=e,g=null));var b=new pl("action","action",null,l,u);t.push({event:b,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if($a!==0){var A=g?Ny(u,g):new FormData(u);yc(i,{pending:!0,data:A,method:u.method,action:f},null,A)}}else typeof f=="function"&&(b.preventDefault(),A=g?Ny(u,g):new FormData(u),yc(i,{pending:!0,data:A,method:u.method,action:f},f,A))},currentTarget:u}]})}}for(var Jc=0;Jc<Lu.length;Jc++){var Wc=Lu[Jc],Gx=Wc.toLowerCase(),Yx=Wc[0].toUpperCase()+Wc.slice(1);nn(Gx,"on"+Yx)}nn(fm,"onAnimationEnd"),nn(dm,"onAnimationIteration"),nn(hm,"onAnimationStart"),nn("dblclick","onDoubleClick"),nn("focusin","onFocus"),nn("focusout","onBlur"),nn(lx,"onTransitionRun"),nn(ox,"onTransitionStart"),nn(ux,"onTransitionCancel"),nn(mm,"onTransitionEnd"),vi("onMouseEnter",["mouseout","mouseover"]),vi("onMouseLeave",["mouseout","mouseover"]),vi("onPointerEnter",["pointerout","pointerover"]),vi("onPointerLeave",["pointerout","pointerover"]),_a("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),_a("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),_a("onBeforeInput",["compositionend","keypress","textInput","paste"]),_a("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),_a("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),_a("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var cr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Xx=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(cr));function zy(t,e){e=(e&4)!==0;for(var i=0;i<t.length;i++){var l=t[i],u=l.event;l=l.listeners;t:{var f=void 0;if(e)for(var g=l.length-1;0<=g;g--){var b=l[g],A=b.instance,N=b.currentTarget;if(b=b.listener,A!==f&&u.isPropagationStopped())break t;f=b,u.currentTarget=N;try{f(u)}catch(H){ql(H)}u.currentTarget=null,f=A}else for(g=0;g<l.length;g++){if(b=l[g],A=b.instance,N=b.currentTarget,b=b.listener,A!==f&&u.isPropagationStopped())break t;f=b,u.currentTarget=N;try{f(u)}catch(H){ql(H)}u.currentTarget=null,f=A}}}}function St(t,e){var i=e[fu];i===void 0&&(i=e[fu]=new Set);var l=t+"__bubble";i.has(l)||(_y(e,t,2,!1),i.add(l))}function Ic(t,e,i){var l=0;e&&(l|=4),_y(i,t,l,e)}var eo="_reactListening"+Math.random().toString(36).slice(2);function tf(t){if(!t[eo]){t[eo]=!0,Mh.forEach(function(i){i!=="selectionchange"&&(Xx.has(i)||Ic(i,!1,t),Ic(i,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[eo]||(e[eo]=!0,Ic("selectionchange",!1,e))}}function _y(t,e,i,l){switch(ig(e)){case 2:var u=vT;break;case 8:u=bT;break;default:u=pf}i=u.bind(null,e,i,t),u=void 0,!Tu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(u=!0),l?u!==void 0?t.addEventListener(e,i,{capture:!0,passive:u}):t.addEventListener(e,i,!0):u!==void 0?t.addEventListener(e,i,{passive:u}):t.addEventListener(e,i,!1)}function ef(t,e,i,l,u){var f=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var g=l.tag;if(g===3||g===4){var b=l.stateNode.containerInfo;if(b===u)break;if(g===4)for(g=l.return;g!==null;){var A=g.tag;if((A===3||A===4)&&g.stateNode.containerInfo===u)return;g=g.return}for(;b!==null;){if(g=pi(b),g===null)return;if(A=g.tag,A===5||A===6||A===26||A===27){l=f=g;continue t}b=b.parentNode}}l=l.return}qh(function(){var N=f,H=Su(i),G=[];t:{var _=pm.get(t);if(_!==void 0){var L=pl,ot=t;switch(t){case"keypress":if(hl(i)===0)break t;case"keydown":case"keyup":L=k1;break;case"focusin":ot="focus",L=Ru;break;case"focusout":ot="blur",L=Ru;break;case"beforeblur":case"afterblur":L=Ru;break;case"click":if(i.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":L=Yh;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":L=M1;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":L=P1;break;case fm:case dm:case hm:L=D1;break;case mm:L=Y1;break;case"scroll":case"scrollend":L=w1;break;case"wheel":L=Q1;break;case"copy":case"cut":case"paste":L=N1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":L=Qh;break;case"toggle":case"beforetoggle":L=F1}var rt=(e&4)!==0,Ut=!rt&&(t==="scroll"||t==="scrollend"),O=rt?_!==null?_+"Capture":null:_;rt=[];for(var C=N,D;C!==null;){var q=C;if(D=q.stateNode,q=q.tag,q!==5&&q!==26&&q!==27||D===null||O===null||(q=Os(C,O),q!=null&&rt.push(fr(C,q,D))),Ut)break;C=C.return}0<rt.length&&(_=new L(_,ot,null,i,H),G.push({event:_,listeners:rt}))}}if((e&7)===0){t:{if(_=t==="mouseover"||t==="pointerover",L=t==="mouseout"||t==="pointerout",_&&i!==bu&&(ot=i.relatedTarget||i.fromElement)&&(pi(ot)||ot[mi]))break t;if((L||_)&&(_=H.window===H?H:(_=H.ownerDocument)?_.defaultView||_.parentWindow:window,L?(ot=i.relatedTarget||i.toElement,L=N,ot=ot?pi(ot):null,ot!==null&&(Ut=c(ot),rt=ot.tag,ot!==Ut||rt!==5&&rt!==27&&rt!==6)&&(ot=null)):(L=null,ot=N),L!==ot)){if(rt=Yh,q="onMouseLeave",O="onMouseEnter",C="mouse",(t==="pointerout"||t==="pointerover")&&(rt=Qh,q="onPointerLeave",O="onPointerEnter",C="pointer"),Ut=L==null?_:Cs(L),D=ot==null?_:Cs(ot),_=new rt(q,C+"leave",L,i,H),_.target=Ut,_.relatedTarget=D,q=null,pi(H)===N&&(rt=new rt(O,C+"enter",ot,i,H),rt.target=D,rt.relatedTarget=Ut,q=rt),Ut=q,L&&ot)e:{for(rt=L,O=ot,C=0,D=rt;D;D=Zi(D))C++;for(D=0,q=O;q;q=Zi(q))D++;for(;0<C-D;)rt=Zi(rt),C--;for(;0<D-C;)O=Zi(O),D--;for(;C--;){if(rt===O||O!==null&&rt===O.alternate)break e;rt=Zi(rt),O=Zi(O)}rt=null}else rt=null;L!==null&&Ly(G,_,L,rt,!1),ot!==null&&Ut!==null&&Ly(G,Ut,ot,rt,!0)}}t:{if(_=N?Cs(N):window,L=_.nodeName&&_.nodeName.toLowerCase(),L==="select"||L==="input"&&_.type==="file")var tt=tm;else if(Wh(_))if(em)tt=ix;else{tt=nx;var gt=ex}else L=_.nodeName,!L||L.toLowerCase()!=="input"||_.type!=="checkbox"&&_.type!=="radio"?N&&vu(N.elementType)&&(tt=tm):tt=ax;if(tt&&(tt=tt(t,N))){Ih(G,tt,i,H);break t}gt&&gt(t,_,N),t==="focusout"&&N&&_.type==="number"&&N.memoizedProps.value!=null&&gu(_,"number",_.value)}switch(gt=N?Cs(N):window,t){case"focusin":(Wh(gt)||gt.contentEditable==="true")&&(wi=gt,Nu=N,Bs=null);break;case"focusout":Bs=Nu=wi=null;break;case"mousedown":zu=!0;break;case"contextmenu":case"mouseup":case"dragend":zu=!1,um(G,i,H);break;case"selectionchange":if(rx)break;case"keydown":case"keyup":um(G,i,H)}var st;if(Cu)t:{switch(t){case"compositionstart":var lt="onCompositionStart";break t;case"compositionend":lt="onCompositionEnd";break t;case"compositionupdate":lt="onCompositionUpdate";break t}lt=void 0}else Ei?$h(t,i)&&(lt="onCompositionEnd"):t==="keydown"&&i.keyCode===229&&(lt="onCompositionStart");lt&&(Kh&&i.locale!=="ko"&&(Ei||lt!=="onCompositionStart"?lt==="onCompositionEnd"&&Ei&&(st=Ph()):(ta=H,Au="value"in ta?ta.value:ta.textContent,Ei=!0)),gt=no(N,lt),0<gt.length&&(lt=new Xh(lt,t,null,i,H),G.push({event:lt,listeners:gt}),st?lt.data=st:(st=Jh(i),st!==null&&(lt.data=st)))),(st=$1?J1(t,i):W1(t,i))&&(lt=no(N,"onBeforeInput"),0<lt.length&&(gt=new Xh("onBeforeInput","beforeinput",null,i,H),G.push({event:gt,listeners:lt}),gt.data=st)),Px(G,t,N,i,H)}zy(G,e)})}function fr(t,e,i){return{instance:t,listener:e,currentTarget:i}}function no(t,e){for(var i=e+"Capture",l=[];t!==null;){var u=t,f=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||f===null||(u=Os(t,i),u!=null&&l.unshift(fr(t,u,f)),u=Os(t,e),u!=null&&l.push(fr(t,u,f))),t.tag===3)return l;t=t.return}return[]}function Zi(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Ly(t,e,i,l,u){for(var f=e._reactName,g=[];i!==null&&i!==l;){var b=i,A=b.alternate,N=b.stateNode;if(b=b.tag,A!==null&&A===l)break;b!==5&&b!==26&&b!==27||N===null||(A=N,u?(N=Os(i,f),N!=null&&g.unshift(fr(i,N,A))):u||(N=Os(i,f),N!=null&&g.push(fr(i,N,A)))),i=i.return}g.length!==0&&t.push({event:e,listeners:g})}var Qx=/\r\n?/g,Kx=/\u0000|\uFFFD/g;function Vy(t){return(typeof t=="string"?t:""+t).replace(Qx,`
`).replace(Kx,"")}function By(t,e){return e=Vy(e),Vy(t)===e}function ao(){}function Dt(t,e,i,l,u,f){switch(i){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||xi(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&xi(t,""+l);break;case"className":ol(t,"class",l);break;case"tabIndex":ol(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":ol(t,i,l);break;case"style":kh(t,l,f);break;case"data":if(e!=="object"){ol(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||i!=="href")){t.removeAttribute(i);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(i);break}l=fl(""+l),t.setAttribute(i,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(i==="formAction"?(e!=="input"&&Dt(t,e,"name",u.name,u,null),Dt(t,e,"formEncType",u.formEncType,u,null),Dt(t,e,"formMethod",u.formMethod,u,null),Dt(t,e,"formTarget",u.formTarget,u,null)):(Dt(t,e,"encType",u.encType,u,null),Dt(t,e,"method",u.method,u,null),Dt(t,e,"target",u.target,u,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(i);break}l=fl(""+l),t.setAttribute(i,l);break;case"onClick":l!=null&&(t.onclick=ao);break;case"onScroll":l!=null&&St("scroll",t);break;case"onScrollEnd":l!=null&&St("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(i=l.__html,i!=null){if(u.children!=null)throw Error(r(60));t.innerHTML=i}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}i=fl(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(i,""+l):t.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(i,""):t.removeAttribute(i);break;case"capture":case"download":l===!0?t.setAttribute(i,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(i,l):t.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(i,l):t.removeAttribute(i);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(i):t.setAttribute(i,l);break;case"popover":St("beforetoggle",t),St("toggle",t),ll(t,"popover",l);break;case"xlinkActuate":Rn(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Rn(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Rn(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Rn(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Rn(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Rn(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Rn(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Rn(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Rn(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":ll(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=A1.get(i)||i,ll(t,i,l))}}function nf(t,e,i,l,u,f){switch(i){case"style":kh(t,l,f);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(i=l.__html,i!=null){if(u.children!=null)throw Error(r(60));t.innerHTML=i}}break;case"children":typeof l=="string"?xi(t,l):(typeof l=="number"||typeof l=="bigint")&&xi(t,""+l);break;case"onScroll":l!=null&&St("scroll",t);break;case"onScrollEnd":l!=null&&St("scrollend",t);break;case"onClick":l!=null&&(t.onclick=ao);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ch.hasOwnProperty(i))t:{if(i[0]==="o"&&i[1]==="n"&&(u=i.endsWith("Capture"),e=i.slice(2,u?i.length-7:void 0),f=t[Ee]||null,f=f!=null?f[i]:null,typeof f=="function"&&t.removeEventListener(e,f,u),typeof l=="function")){typeof f!="function"&&f!==null&&(i in t?t[i]=null:t.hasAttribute(i)&&t.removeAttribute(i)),t.addEventListener(e,l,u);break t}i in t?t[i]=l:l===!0?t.setAttribute(i,""):ll(t,i,l)}}}function ce(t,e,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":St("error",t),St("load",t);var l=!1,u=!1,f;for(f in i)if(i.hasOwnProperty(f)){var g=i[f];if(g!=null)switch(f){case"src":l=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Dt(t,e,f,g,i,null)}}u&&Dt(t,e,"srcSet",i.srcSet,i,null),l&&Dt(t,e,"src",i.src,i,null);return;case"input":St("invalid",t);var b=f=g=u=null,A=null,N=null;for(l in i)if(i.hasOwnProperty(l)){var H=i[l];if(H!=null)switch(l){case"name":u=H;break;case"type":g=H;break;case"checked":A=H;break;case"defaultChecked":N=H;break;case"value":f=H;break;case"defaultValue":b=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(r(137,e));break;default:Dt(t,e,l,H,i,null)}}Lh(t,f,b,A,N,g,u,!1),ul(t);return;case"select":St("invalid",t),l=g=f=null;for(u in i)if(i.hasOwnProperty(u)&&(b=i[u],b!=null))switch(u){case"value":f=b;break;case"defaultValue":g=b;break;case"multiple":l=b;default:Dt(t,e,u,b,i,null)}e=f,i=g,t.multiple=!!l,e!=null?Si(t,!!l,e,!1):i!=null&&Si(t,!!l,i,!0);return;case"textarea":St("invalid",t),f=u=l=null;for(g in i)if(i.hasOwnProperty(g)&&(b=i[g],b!=null))switch(g){case"value":l=b;break;case"defaultValue":u=b;break;case"children":f=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(r(91));break;default:Dt(t,e,g,b,i,null)}Bh(t,l,u,f),ul(t);return;case"option":for(A in i)if(i.hasOwnProperty(A)&&(l=i[A],l!=null))switch(A){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Dt(t,e,A,l,i,null)}return;case"dialog":St("beforetoggle",t),St("toggle",t),St("cancel",t),St("close",t);break;case"iframe":case"object":St("load",t);break;case"video":case"audio":for(l=0;l<cr.length;l++)St(cr[l],t);break;case"image":St("error",t),St("load",t);break;case"details":St("toggle",t);break;case"embed":case"source":case"link":St("error",t),St("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in i)if(i.hasOwnProperty(N)&&(l=i[N],l!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Dt(t,e,N,l,i,null)}return;default:if(vu(e)){for(H in i)i.hasOwnProperty(H)&&(l=i[H],l!==void 0&&nf(t,e,H,l,i,void 0));return}}for(b in i)i.hasOwnProperty(b)&&(l=i[b],l!=null&&Dt(t,e,b,l,i,null))}function Fx(t,e,i,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,f=null,g=null,b=null,A=null,N=null,H=null;for(L in i){var G=i[L];if(i.hasOwnProperty(L)&&G!=null)switch(L){case"checked":break;case"value":break;case"defaultValue":A=G;default:l.hasOwnProperty(L)||Dt(t,e,L,null,l,G)}}for(var _ in l){var L=l[_];if(G=i[_],l.hasOwnProperty(_)&&(L!=null||G!=null))switch(_){case"type":f=L;break;case"name":u=L;break;case"checked":N=L;break;case"defaultChecked":H=L;break;case"value":g=L;break;case"defaultValue":b=L;break;case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(r(137,e));break;default:L!==G&&Dt(t,e,_,L,l,G)}}yu(t,g,b,A,N,H,f,u);return;case"select":L=g=b=_=null;for(f in i)if(A=i[f],i.hasOwnProperty(f)&&A!=null)switch(f){case"value":break;case"multiple":L=A;default:l.hasOwnProperty(f)||Dt(t,e,f,null,l,A)}for(u in l)if(f=l[u],A=i[u],l.hasOwnProperty(u)&&(f!=null||A!=null))switch(u){case"value":_=f;break;case"defaultValue":b=f;break;case"multiple":g=f;default:f!==A&&Dt(t,e,u,f,l,A)}e=b,i=g,l=L,_!=null?Si(t,!!i,_,!1):!!l!=!!i&&(e!=null?Si(t,!!i,e,!0):Si(t,!!i,i?[]:"",!1));return;case"textarea":L=_=null;for(b in i)if(u=i[b],i.hasOwnProperty(b)&&u!=null&&!l.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:Dt(t,e,b,null,l,u)}for(g in l)if(u=l[g],f=i[g],l.hasOwnProperty(g)&&(u!=null||f!=null))switch(g){case"value":_=u;break;case"defaultValue":L=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(r(91));break;default:u!==f&&Dt(t,e,g,u,l,f)}Vh(t,_,L);return;case"option":for(var ot in i)if(_=i[ot],i.hasOwnProperty(ot)&&_!=null&&!l.hasOwnProperty(ot))switch(ot){case"selected":t.selected=!1;break;default:Dt(t,e,ot,null,l,_)}for(A in l)if(_=l[A],L=i[A],l.hasOwnProperty(A)&&_!==L&&(_!=null||L!=null))switch(A){case"selected":t.selected=_&&typeof _!="function"&&typeof _!="symbol";break;default:Dt(t,e,A,_,l,L)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var rt in i)_=i[rt],i.hasOwnProperty(rt)&&_!=null&&!l.hasOwnProperty(rt)&&Dt(t,e,rt,null,l,_);for(N in l)if(_=l[N],L=i[N],l.hasOwnProperty(N)&&_!==L&&(_!=null||L!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(r(137,e));break;default:Dt(t,e,N,_,l,L)}return;default:if(vu(e)){for(var Ut in i)_=i[Ut],i.hasOwnProperty(Ut)&&_!==void 0&&!l.hasOwnProperty(Ut)&&nf(t,e,Ut,void 0,l,_);for(H in l)_=l[H],L=i[H],!l.hasOwnProperty(H)||_===L||_===void 0&&L===void 0||nf(t,e,H,_,l,L);return}}for(var O in i)_=i[O],i.hasOwnProperty(O)&&_!=null&&!l.hasOwnProperty(O)&&Dt(t,e,O,null,l,_);for(G in l)_=l[G],L=i[G],!l.hasOwnProperty(G)||_===L||_==null&&L==null||Dt(t,e,G,_,l,L)}var af=null,sf=null;function io(t){return t.nodeType===9?t:t.ownerDocument}function jy(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ky(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function rf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var lf=null;function Zx(){var t=window.event;return t&&t.type==="popstate"?t===lf?!1:(lf=t,!0):(lf=null,!1)}var Hy=typeof setTimeout=="function"?setTimeout:void 0,$x=typeof clearTimeout=="function"?clearTimeout:void 0,qy=typeof Promise=="function"?Promise:void 0,Jx=typeof queueMicrotask=="function"?queueMicrotask:typeof qy<"u"?function(t){return qy.resolve(null).then(t).catch(Wx)}:Hy;function Wx(t){setTimeout(function(){throw t})}function ya(t){return t==="head"}function Py(t,e){var i=e,l=0,u=0;do{var f=i.nextSibling;if(t.removeChild(i),f&&f.nodeType===8)if(i=f.data,i==="/$"){if(0<l&&8>l){i=l;var g=t.ownerDocument;if(i&1&&dr(g.documentElement),i&2&&dr(g.body),i&4)for(i=g.head,dr(i),g=i.firstChild;g;){var b=g.nextSibling,A=g.nodeName;g[Ms]||A==="SCRIPT"||A==="STYLE"||A==="LINK"&&g.rel.toLowerCase()==="stylesheet"||i.removeChild(g),g=b}}if(u===0){t.removeChild(f),Sr(e);return}u--}else i==="$"||i==="$?"||i==="$!"?u++:l=i.charCodeAt(0)-48;else l=0;i=f}while(i);Sr(e)}function of(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var i=e;switch(e=e.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":of(i),du(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}t.removeChild(i)}}function Ix(t,e,i,l){for(;t.nodeType===1;){var u=i;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Ms])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(f=t.getAttribute("rel"),f==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(f!==u.rel||t.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||t.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||t.getAttribute("title")!==(u.title==null?null:u.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(f=t.getAttribute("src"),(f!==(u.src==null?null:u.src)||t.getAttribute("type")!==(u.type==null?null:u.type)||t.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&f&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var f=u.name==null?null:""+u.name;if(u.type==="hidden"&&t.getAttribute("name")===f)return t}else return t;if(t=sn(t.nextSibling),t===null)break}return null}function tT(t,e,i){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!i||(t=sn(t.nextSibling),t===null))return null;return t}function uf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function eT(t,e){var i=t.ownerDocument;if(t.data!=="$?"||i.readyState==="complete")e();else{var l=function(){e(),i.removeEventListener("DOMContentLoaded",l)};i.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function sn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var cf=null;function Gy(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var i=t.data;if(i==="$"||i==="$!"||i==="$?"){if(e===0)return t;e--}else i==="/$"&&e++}t=t.previousSibling}return null}function Yy(t,e,i){switch(e=io(i),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function dr(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);du(t)}var Je=new Map,Xy=new Set;function so(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Hn=F.d;F.d={f:nT,r:aT,D:iT,C:sT,L:rT,m:lT,X:uT,S:oT,M:cT};function nT(){var t=Hn.f(),e=$l();return t||e}function aT(t){var e=yi(t);e!==null&&e.tag===5&&e.type==="form"?fp(e):Hn.r(t)}var $i=typeof document>"u"?null:document;function Qy(t,e,i){var l=$i;if(l&&typeof e=="string"&&e){var u=Ye(e);u='link[rel="'+t+'"][href="'+u+'"]',typeof i=="string"&&(u+='[crossorigin="'+i+'"]'),Xy.has(u)||(Xy.add(u),t={rel:t,crossOrigin:i,href:e},l.querySelector(u)===null&&(e=l.createElement("link"),ce(e,"link",t),ie(e),l.head.appendChild(e)))}}function iT(t){Hn.D(t),Qy("dns-prefetch",t,null)}function sT(t,e){Hn.C(t,e),Qy("preconnect",t,e)}function rT(t,e,i){Hn.L(t,e,i);var l=$i;if(l&&t&&e){var u='link[rel="preload"][as="'+Ye(e)+'"]';e==="image"&&i&&i.imageSrcSet?(u+='[imagesrcset="'+Ye(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(u+='[imagesizes="'+Ye(i.imageSizes)+'"]')):u+='[href="'+Ye(t)+'"]';var f=u;switch(e){case"style":f=Ji(t);break;case"script":f=Wi(t)}Je.has(f)||(t=y({rel:"preload",href:e==="image"&&i&&i.imageSrcSet?void 0:t,as:e},i),Je.set(f,t),l.querySelector(u)!==null||e==="style"&&l.querySelector(hr(f))||e==="script"&&l.querySelector(mr(f))||(e=l.createElement("link"),ce(e,"link",t),ie(e),l.head.appendChild(e)))}}function lT(t,e){Hn.m(t,e);var i=$i;if(i&&t){var l=e&&typeof e.as=="string"?e.as:"script",u='link[rel="modulepreload"][as="'+Ye(l)+'"][href="'+Ye(t)+'"]',f=u;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=Wi(t)}if(!Je.has(f)&&(t=y({rel:"modulepreload",href:t},e),Je.set(f,t),i.querySelector(u)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(mr(f)))return}l=i.createElement("link"),ce(l,"link",t),ie(l),i.head.appendChild(l)}}}function oT(t,e,i){Hn.S(t,e,i);var l=$i;if(l&&t){var u=gi(l).hoistableStyles,f=Ji(t);e=e||"default";var g=u.get(f);if(!g){var b={loading:0,preload:null};if(g=l.querySelector(hr(f)))b.loading=5;else{t=y({rel:"stylesheet",href:t,"data-precedence":e},i),(i=Je.get(f))&&ff(t,i);var A=g=l.createElement("link");ie(A),ce(A,"link",t),A._p=new Promise(function(N,H){A.onload=N,A.onerror=H}),A.addEventListener("load",function(){b.loading|=1}),A.addEventListener("error",function(){b.loading|=2}),b.loading|=4,ro(g,e,l)}g={type:"stylesheet",instance:g,count:1,state:b},u.set(f,g)}}}function uT(t,e){Hn.X(t,e);var i=$i;if(i&&t){var l=gi(i).hoistableScripts,u=Wi(t),f=l.get(u);f||(f=i.querySelector(mr(u)),f||(t=y({src:t,async:!0},e),(e=Je.get(u))&&df(t,e),f=i.createElement("script"),ie(f),ce(f,"link",t),i.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},l.set(u,f))}}function cT(t,e){Hn.M(t,e);var i=$i;if(i&&t){var l=gi(i).hoistableScripts,u=Wi(t),f=l.get(u);f||(f=i.querySelector(mr(u)),f||(t=y({src:t,async:!0,type:"module"},e),(e=Je.get(u))&&df(t,e),f=i.createElement("script"),ie(f),ce(f,"link",t),i.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},l.set(u,f))}}function Ky(t,e,i,l){var u=(u=ct.current)?so(u):null;if(!u)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(e=Ji(i.href),i=gi(u).hoistableStyles,l=i.get(e),l||(l={type:"style",instance:null,count:0,state:null},i.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){t=Ji(i.href);var f=gi(u).hoistableStyles,g=f.get(t);if(g||(u=u.ownerDocument||u,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(t,g),(f=u.querySelector(hr(t)))&&!f._p&&(g.instance=f,g.state.loading=5),Je.has(t)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},Je.set(t,i),f||fT(u,t,i,g.state))),e&&l===null)throw Error(r(528,""));return g}if(e&&l!==null)throw Error(r(529,""));return null;case"script":return e=i.async,i=i.src,typeof i=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Wi(i),i=gi(u).hoistableScripts,l=i.get(e),l||(l={type:"script",instance:null,count:0,state:null},i.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function Ji(t){return'href="'+Ye(t)+'"'}function hr(t){return'link[rel="stylesheet"]['+t+"]"}function Fy(t){return y({},t,{"data-precedence":t.precedence,precedence:null})}function fT(t,e,i,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),ce(e,"link",i),ie(e),t.head.appendChild(e))}function Wi(t){return'[src="'+Ye(t)+'"]'}function mr(t){return"script[async]"+t}function Zy(t,e,i){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Ye(i.href)+'"]');if(l)return e.instance=l,ie(l),l;var u=y({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),ie(l),ce(l,"style",u),ro(l,i.precedence,t),e.instance=l;case"stylesheet":u=Ji(i.href);var f=t.querySelector(hr(u));if(f)return e.state.loading|=4,e.instance=f,ie(f),f;l=Fy(i),(u=Je.get(u))&&ff(l,u),f=(t.ownerDocument||t).createElement("link"),ie(f);var g=f;return g._p=new Promise(function(b,A){g.onload=b,g.onerror=A}),ce(f,"link",l),e.state.loading|=4,ro(f,i.precedence,t),e.instance=f;case"script":return f=Wi(i.src),(u=t.querySelector(mr(f)))?(e.instance=u,ie(u),u):(l=i,(u=Je.get(f))&&(l=y({},i),df(l,u)),t=t.ownerDocument||t,u=t.createElement("script"),ie(u),ce(u,"link",l),t.head.appendChild(u),e.instance=u);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,ro(l,i.precedence,t));return e.instance}function ro(t,e,i){for(var l=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=l.length?l[l.length-1]:null,f=u,g=0;g<l.length;g++){var b=l[g];if(b.dataset.precedence===e)f=b;else if(f!==u)break}f?f.parentNode.insertBefore(t,f.nextSibling):(e=i.nodeType===9?i.head:i,e.insertBefore(t,e.firstChild))}function ff(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function df(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var lo=null;function $y(t,e,i){if(lo===null){var l=new Map,u=lo=new Map;u.set(i,l)}else u=lo,l=u.get(i),l||(l=new Map,u.set(i,l));if(l.has(t))return l;for(l.set(t,null),i=i.getElementsByTagName(t),u=0;u<i.length;u++){var f=i[u];if(!(f[Ms]||f[de]||t==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var g=f.getAttribute(e)||"";g=t+g;var b=l.get(g);b?b.push(f):l.set(g,[f])}}return l}function Jy(t,e,i){t=t.ownerDocument||t,t.head.insertBefore(i,e==="title"?t.querySelector("head > title"):null)}function dT(t,e,i){if(i===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Wy(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var pr=null;function hT(){}function mT(t,e,i){if(pr===null)throw Error(r(475));var l=pr;if(e.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var u=Ji(i.href),f=t.querySelector(hr(u));if(f){t=f._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=oo.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=f,ie(f);return}f=t.ownerDocument||t,i=Fy(i),(u=Je.get(u))&&ff(i,u),f=f.createElement("link"),ie(f);var g=f;g._p=new Promise(function(b,A){g.onload=b,g.onerror=A}),ce(f,"link",i),e.instance=f}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=oo.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function pT(){if(pr===null)throw Error(r(475));var t=pr;return t.stylesheets&&t.count===0&&hf(t,t.stylesheets),0<t.count?function(e){var i=setTimeout(function(){if(t.stylesheets&&hf(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(i)}}:null}function oo(){if(this.count--,this.count===0){if(this.stylesheets)hf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var uo=null;function hf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,uo=new Map,e.forEach(yT,t),uo=null,oo.call(t))}function yT(t,e){if(!(e.state.loading&4)){var i=uo.get(t);if(i)var l=i.get(null);else{i=new Map,uo.set(t,i);for(var u=t.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<u.length;f++){var g=u[f];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(i.set(g.dataset.precedence,g),l=g)}l&&i.set(null,l)}u=e.instance,g=u.getAttribute("data-precedence"),f=i.get(g)||l,f===l&&i.set(null,u),i.set(g,u),this.count++,l=oo.bind(this),u.addEventListener("load",l),u.addEventListener("error",l),f?f.parentNode.insertBefore(u,f.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(u,t.firstChild)),e.state.loading|=4}}var yr={$$typeof:U,Provider:null,Consumer:null,_currentValue:X,_currentValue2:X,_threadCount:0};function gT(t,e,i,l,u,f,g,b){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ou(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ou(0),this.hiddenUpdates=ou(null),this.identifierPrefix=l,this.onUncaughtError=u,this.onCaughtError=f,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function Iy(t,e,i,l,u,f,g,b,A,N,H,G){return t=new gT(t,e,i,g,b,A,N,G),e=1,f===!0&&(e|=24),f=Ve(3,null,null,e),t.current=f,f.stateNode=t,e=Ku(),e.refCount++,t.pooledCache=e,e.refCount++,f.memoizedState={element:l,isDehydrated:i,cache:e},Ju(f),t}function tg(t){return t?(t=Oi,t):Oi}function eg(t,e,i,l,u,f){u=tg(u),l.context===null?l.context=u:l.pendingContext=u,l=aa(e),l.payload={element:i},f=f===void 0?null:f,f!==null&&(l.callback=f),i=ia(t,l,e),i!==null&&(qe(i,t,e),Qs(i,t,e))}function ng(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var i=t.retryLane;t.retryLane=i!==0&&i<e?i:e}}function mf(t,e){ng(t,e),(t=t.alternate)&&ng(t,e)}function ag(t){if(t.tag===13){var e=Ci(t,67108864);e!==null&&qe(e,t,67108864),mf(t,67108864)}}var co=!0;function vT(t,e,i,l){var u=j.T;j.T=null;var f=F.p;try{F.p=2,pf(t,e,i,l)}finally{F.p=f,j.T=u}}function bT(t,e,i,l){var u=j.T;j.T=null;var f=F.p;try{F.p=8,pf(t,e,i,l)}finally{F.p=f,j.T=u}}function pf(t,e,i,l){if(co){var u=yf(l);if(u===null)ef(t,e,l,fo,i),sg(t,l);else if(xT(u,t,e,i,l))l.stopPropagation();else if(sg(t,l),e&4&&-1<ST.indexOf(t)){for(;u!==null;){var f=yi(u);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var g=za(f.pendingLanes);if(g!==0){var b=f;for(b.pendingLanes|=2,b.entangledLanes|=2;g;){var A=1<<31-_e(g);b.entanglements[1]|=A,g&=~A}yn(f),(Mt&6)===0&&(Fl=fn()+500,ur(0))}}break;case 13:b=Ci(f,2),b!==null&&qe(b,f,2),$l(),mf(f,2)}if(f=yf(l),f===null&&ef(t,e,l,fo,i),f===u)break;u=f}u!==null&&l.stopPropagation()}else ef(t,e,l,null,i)}}function yf(t){return t=Su(t),gf(t)}var fo=null;function gf(t){if(fo=null,t=pi(t),t!==null){var e=c(t);if(e===null)t=null;else{var i=e.tag;if(i===13){if(t=d(e),t!==null)return t;t=null}else if(i===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return fo=t,null}function ig(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(s1()){case vh:return 2;case bh:return 8;case al:case r1:return 32;case Sh:return 268435456;default:return 32}default:return 32}}var vf=!1,ga=null,va=null,ba=null,gr=new Map,vr=new Map,Sa=[],ST="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function sg(t,e){switch(t){case"focusin":case"focusout":ga=null;break;case"dragenter":case"dragleave":va=null;break;case"mouseover":case"mouseout":ba=null;break;case"pointerover":case"pointerout":gr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":vr.delete(e.pointerId)}}function br(t,e,i,l,u,f){return t===null||t.nativeEvent!==f?(t={blockedOn:e,domEventName:i,eventSystemFlags:l,nativeEvent:f,targetContainers:[u]},e!==null&&(e=yi(e),e!==null&&ag(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,u!==null&&e.indexOf(u)===-1&&e.push(u),t)}function xT(t,e,i,l,u){switch(e){case"focusin":return ga=br(ga,t,e,i,l,u),!0;case"dragenter":return va=br(va,t,e,i,l,u),!0;case"mouseover":return ba=br(ba,t,e,i,l,u),!0;case"pointerover":var f=u.pointerId;return gr.set(f,br(gr.get(f)||null,t,e,i,l,u)),!0;case"gotpointercapture":return f=u.pointerId,vr.set(f,br(vr.get(f)||null,t,e,i,l,u)),!0}return!1}function rg(t){var e=pi(t.target);if(e!==null){var i=c(e);if(i!==null){if(e=i.tag,e===13){if(e=d(i),e!==null){t.blockedOn=e,m1(t.priority,function(){if(i.tag===13){var l=He();l=uu(l);var u=Ci(i,l);u!==null&&qe(u,i,l),mf(i,l)}});return}}else if(e===3&&i.stateNode.current.memoizedState.isDehydrated){t.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}t.blockedOn=null}function ho(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var i=yf(t.nativeEvent);if(i===null){i=t.nativeEvent;var l=new i.constructor(i.type,i);bu=l,i.target.dispatchEvent(l),bu=null}else return e=yi(i),e!==null&&ag(e),t.blockedOn=i,!1;e.shift()}return!0}function lg(t,e,i){ho(t)&&i.delete(e)}function TT(){vf=!1,ga!==null&&ho(ga)&&(ga=null),va!==null&&ho(va)&&(va=null),ba!==null&&ho(ba)&&(ba=null),gr.forEach(lg),vr.forEach(lg)}function mo(t,e){t.blockedOn===e&&(t.blockedOn=null,vf||(vf=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,TT)))}var po=null;function og(t){po!==t&&(po=t,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){po===t&&(po=null);for(var e=0;e<t.length;e+=3){var i=t[e],l=t[e+1],u=t[e+2];if(typeof l!="function"){if(gf(l||i)===null)continue;break}var f=yi(i);f!==null&&(t.splice(e,3),e-=3,yc(f,{pending:!0,data:u,method:i.method,action:l},l,u))}}))}function Sr(t){function e(A){return mo(A,t)}ga!==null&&mo(ga,t),va!==null&&mo(va,t),ba!==null&&mo(ba,t),gr.forEach(e),vr.forEach(e);for(var i=0;i<Sa.length;i++){var l=Sa[i];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Sa.length&&(i=Sa[0],i.blockedOn===null);)rg(i),i.blockedOn===null&&Sa.shift();if(i=(t.ownerDocument||t).$$reactFormReplay,i!=null)for(l=0;l<i.length;l+=3){var u=i[l],f=i[l+1],g=u[Ee]||null;if(typeof f=="function")g||og(i);else if(g){var b=null;if(f&&f.hasAttribute("formAction")){if(u=f,g=f[Ee]||null)b=g.formAction;else if(gf(u)!==null)continue}else b=g.action;typeof b=="function"?i[l+1]=b:(i.splice(l,3),l-=3),og(i)}}}function bf(t){this._internalRoot=t}yo.prototype.render=bf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var i=e.current,l=He();eg(i,l,t,e,null,null)},yo.prototype.unmount=bf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;eg(t.current,2,null,t,null,null),$l(),e[mi]=null}};function yo(t){this._internalRoot=t}yo.prototype.unstable_scheduleHydration=function(t){if(t){var e=wh();t={blockedOn:null,target:t,priority:e};for(var i=0;i<Sa.length&&e!==0&&e<Sa[i].priority;i++);Sa.splice(i,0,t),i===0&&rg(t)}};var ug=a.version;if(ug!=="19.1.0")throw Error(r(527,ug,"19.1.0"));F.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=p(e),t=t!==null?m(t):null,t=t===null?null:t.stateNode,t};var AT={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:j,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var go=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!go.isDisabled&&go.supportsFiber)try{Es=go.inject(AT),ze=go}catch{}}return Tr.createRoot=function(t,e){if(!o(t))throw Error(r(299));var i=!1,l="",u=wp,f=Rp,g=Mp,b=null;return e!=null&&(e.unstable_strictMode===!0&&(i=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(u=e.onUncaughtError),e.onCaughtError!==void 0&&(f=e.onCaughtError),e.onRecoverableError!==void 0&&(g=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(b=e.unstable_transitionCallbacks)),e=Iy(t,1,!1,null,null,i,l,u,f,g,b,null),t[mi]=e.current,tf(t),new bf(e)},Tr.hydrateRoot=function(t,e,i){if(!o(t))throw Error(r(299));var l=!1,u="",f=wp,g=Rp,b=Mp,A=null,N=null;return i!=null&&(i.unstable_strictMode===!0&&(l=!0),i.identifierPrefix!==void 0&&(u=i.identifierPrefix),i.onUncaughtError!==void 0&&(f=i.onUncaughtError),i.onCaughtError!==void 0&&(g=i.onCaughtError),i.onRecoverableError!==void 0&&(b=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(A=i.unstable_transitionCallbacks),i.formState!==void 0&&(N=i.formState)),e=Iy(t,1,!0,e,i??null,l,u,f,g,b,A,N),e.context=tg(null),i=e.current,l=He(),l=uu(l),u=aa(l),u.callback=null,ia(i,u,l),i=l,e.current.lanes=i,Rs(e,i),yn(e),t[mi]=e.current,tf(t),new yo(e)},Tr.version="19.1.0",Tr}var xg;function VT(){if(xg)return Af.exports;xg=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(a){console.error(a)}}return n(),Af.exports=LT(),Af.exports}var BT=VT(),Xo=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(n){return this.listeners.add(n),this.onSubscribe(),()=>{this.listeners.delete(n),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Qo=typeof window>"u"||"Deno"in globalThis;function ln(){}function jT(n,a){return typeof n=="function"?n(a):n}function kT(n){return typeof n=="number"&&n>=0&&n!==1/0}function HT(n,a){return Math.max(n+(a||0)-Date.now(),0)}function Ff(n,a){return typeof n=="function"?n(a):n}function qT(n,a){return typeof n=="function"?n(a):n}function Tg(n,a){const{type:s="all",exact:r,fetchStatus:o,predicate:c,queryKey:d,stale:h}=n;if(d){if(r){if(a.queryHash!==Cd(d,a.options))return!1}else if(!Vr(a.queryKey,d))return!1}if(s!=="all"){const p=a.isActive();if(s==="active"&&!p||s==="inactive"&&p)return!1}return!(typeof h=="boolean"&&a.isStale()!==h||o&&o!==a.state.fetchStatus||c&&!c(a))}function Ag(n,a){const{exact:s,status:r,predicate:o,mutationKey:c}=n;if(c){if(!a.options.mutationKey)return!1;if(s){if(Lr(a.options.mutationKey)!==Lr(c))return!1}else if(!Vr(a.options.mutationKey,c))return!1}return!(r&&a.state.status!==r||o&&!o(a))}function Cd(n,a){return((a==null?void 0:a.queryKeyHashFn)||Lr)(n)}function Lr(n){return JSON.stringify(n,(a,s)=>Zf(s)?Object.keys(s).sort().reduce((r,o)=>(r[o]=s[o],r),{}):s)}function Vr(n,a){return n===a?!0:typeof n!=typeof a?!1:n&&a&&typeof n=="object"&&typeof a=="object"?Object.keys(a).every(s=>Vr(n[s],a[s])):!1}function v0(n,a){if(n===a)return n;const s=Eg(n)&&Eg(a);if(s||Zf(n)&&Zf(a)){const r=s?n:Object.keys(n),o=r.length,c=s?a:Object.keys(a),d=c.length,h=s?[]:{},p=new Set(r);let m=0;for(let y=0;y<d;y++){const v=s?y:c[y];(!s&&p.has(v)||s)&&n[v]===void 0&&a[v]===void 0?(h[v]=void 0,m++):(h[v]=v0(n[v],a[v]),h[v]===n[v]&&n[v]!==void 0&&m++)}return o===d&&m===o?n:h}return a}function Eg(n){return Array.isArray(n)&&n.length===Object.keys(n).length}function Zf(n){if(!wg(n))return!1;const a=n.constructor;if(a===void 0)return!0;const s=a.prototype;return!(!wg(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(n)!==Object.prototype)}function wg(n){return Object.prototype.toString.call(n)==="[object Object]"}function PT(n){return new Promise(a=>{setTimeout(a,n)})}function GT(n,a,s){return typeof s.structuralSharing=="function"?s.structuralSharing(n,a):s.structuralSharing!==!1?v0(n,a):a}function YT(n,a,s=0){const r=[...n,a];return s&&r.length>s?r.slice(1):r}function XT(n,a,s=0){const r=[a,...n];return s&&r.length>s?r.slice(0,-1):r}var Od=Symbol();function b0(n,a){return!n.queryFn&&(a!=null&&a.initialPromise)?()=>a.initialPromise:!n.queryFn||n.queryFn===Od?()=>Promise.reject(new Error(`Missing queryFn: '${n.queryHash}'`)):n.queryFn}var ai,wa,rs,c0,QT=(c0=class extends Xo{constructor(){super();wt(this,ai);wt(this,wa);wt(this,rs);ft(this,rs,a=>{if(!Qo&&window.addEventListener){const s=()=>a();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){P(this,wa)||this.setEventListener(P(this,rs))}onUnsubscribe(){var a;this.hasListeners()||((a=P(this,wa))==null||a.call(this),ft(this,wa,void 0))}setEventListener(a){var s;ft(this,rs,a),(s=P(this,wa))==null||s.call(this),ft(this,wa,a(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(a){P(this,ai)!==a&&(ft(this,ai,a),this.onFocus())}onFocus(){const a=this.isFocused();this.listeners.forEach(s=>{s(a)})}isFocused(){var a;return typeof P(this,ai)=="boolean"?P(this,ai):((a=globalThis.document)==null?void 0:a.visibilityState)!=="hidden"}},ai=new WeakMap,wa=new WeakMap,rs=new WeakMap,c0),S0=new QT,ls,Ra,os,f0,KT=(f0=class extends Xo{constructor(){super();wt(this,ls,!0);wt(this,Ra);wt(this,os);ft(this,os,a=>{if(!Qo&&window.addEventListener){const s=()=>a(!0),r=()=>a(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",r)}}})}onSubscribe(){P(this,Ra)||this.setEventListener(P(this,os))}onUnsubscribe(){var a;this.hasListeners()||((a=P(this,Ra))==null||a.call(this),ft(this,Ra,void 0))}setEventListener(a){var s;ft(this,os,a),(s=P(this,Ra))==null||s.call(this),ft(this,Ra,a(this.setOnline.bind(this)))}setOnline(a){P(this,ls)!==a&&(ft(this,ls,a),this.listeners.forEach(r=>{r(a)}))}isOnline(){return P(this,ls)}},ls=new WeakMap,Ra=new WeakMap,os=new WeakMap,f0),Vo=new KT;function FT(){let n,a;const s=new Promise((o,c)=>{n=o,a=c});s.status="pending",s.catch(()=>{});function r(o){Object.assign(s,o),delete s.resolve,delete s.reject}return s.resolve=o=>{r({status:"fulfilled",value:o}),n(o)},s.reject=o=>{r({status:"rejected",reason:o}),a(o)},s}function ZT(n){return Math.min(1e3*2**n,3e4)}function x0(n){return(n??"online")==="online"?Vo.isOnline():!0}var T0=class extends Error{constructor(n){super("CancelledError"),this.revert=n==null?void 0:n.revert,this.silent=n==null?void 0:n.silent}};function Mf(n){return n instanceof T0}function A0(n){let a=!1,s=0,r=!1,o;const c=FT(),d=T=>{var w;r||(S(new T0(T)),(w=n.abort)==null||w.call(n))},h=()=>{a=!0},p=()=>{a=!1},m=()=>S0.isFocused()&&(n.networkMode==="always"||Vo.isOnline())&&n.canRun(),y=()=>x0(n.networkMode)&&n.canRun(),v=T=>{var w;r||(r=!0,(w=n.onSuccess)==null||w.call(n,T),o==null||o(),c.resolve(T))},S=T=>{var w;r||(r=!0,(w=n.onError)==null||w.call(n,T),o==null||o(),c.reject(T))},E=()=>new Promise(T=>{var w;o=R=>{(r||m())&&T(R)},(w=n.onPause)==null||w.call(n)}).then(()=>{var T;o=void 0,r||(T=n.onContinue)==null||T.call(n)}),x=()=>{if(r)return;let T;const w=s===0?n.initialPromise:void 0;try{T=w??n.fn()}catch(R){T=Promise.reject(R)}Promise.resolve(T).then(v).catch(R=>{var $;if(r)return;const V=n.retry??(Qo?0:3),U=n.retryDelay??ZT,K=typeof U=="function"?U(s,R):U,k=V===!0||typeof V=="number"&&s<V||typeof V=="function"&&V(s,R);if(a||!k){S(R);return}s++,($=n.onFail)==null||$.call(n,s,R),PT(K).then(()=>m()?void 0:E()).then(()=>{a?S(R):x()})})};return{promise:c,cancel:d,continue:()=>(o==null||o(),c),cancelRetry:h,continueRetry:p,canStart:y,start:()=>(y()?x():E().then(x),c)}}var $T=n=>setTimeout(n,0);function JT(){let n=[],a=0,s=h=>{h()},r=h=>{h()},o=$T;const c=h=>{a?n.push(h):o(()=>{s(h)})},d=()=>{const h=n;n=[],h.length&&o(()=>{r(()=>{h.forEach(p=>{s(p)})})})};return{batch:h=>{let p;a++;try{p=h()}finally{a--,a||d()}return p},batchCalls:h=>(...p)=>{c(()=>{h(...p)})},schedule:c,setNotifyFunction:h=>{s=h},setBatchNotifyFunction:h=>{r=h},setScheduler:h=>{o=h}}}var xe=JT(),ii,d0,E0=(d0=class{constructor(){wt(this,ii)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),kT(this.gcTime)&&ft(this,ii,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(n){this.gcTime=Math.max(this.gcTime||0,n??(Qo?1/0:5*60*1e3))}clearGcTimeout(){P(this,ii)&&(clearTimeout(P(this,ii)),ft(this,ii,void 0))}},ii=new WeakMap,d0),us,si,tn,ri,ye,Qr,li,on,Pn,h0,WT=(h0=class extends E0{constructor(a){super();wt(this,on);wt(this,us);wt(this,si);wt(this,tn);wt(this,ri);wt(this,ye);wt(this,Qr);wt(this,li);ft(this,li,!1),ft(this,Qr,a.defaultOptions),this.setOptions(a.options),this.observers=[],ft(this,ri,a.client),ft(this,tn,P(this,ri).getQueryCache()),this.queryKey=a.queryKey,this.queryHash=a.queryHash,ft(this,us,tA(this.options)),this.state=a.state??P(this,us),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var a;return(a=P(this,ye))==null?void 0:a.promise}setOptions(a){this.options={...P(this,Qr),...a},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&P(this,tn).remove(this)}setData(a,s){const r=GT(this.state.data,a,this.options);return me(this,on,Pn).call(this,{data:r,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),r}setState(a,s){me(this,on,Pn).call(this,{type:"setState",state:a,setStateOptions:s})}cancel(a){var r,o;const s=(r=P(this,ye))==null?void 0:r.promise;return(o=P(this,ye))==null||o.cancel(a),s?s.then(ln).catch(ln):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(P(this,us))}isActive(){return this.observers.some(a=>qT(a.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Od||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(a=>Ff(a.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(a=>a.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(a=0){return this.state.data===void 0?!0:a==="static"?!1:this.state.isInvalidated?!0:!HT(this.state.dataUpdatedAt,a)}onFocus(){var s;const a=this.observers.find(r=>r.shouldFetchOnWindowFocus());a==null||a.refetch({cancelRefetch:!1}),(s=P(this,ye))==null||s.continue()}onOnline(){var s;const a=this.observers.find(r=>r.shouldFetchOnReconnect());a==null||a.refetch({cancelRefetch:!1}),(s=P(this,ye))==null||s.continue()}addObserver(a){this.observers.includes(a)||(this.observers.push(a),this.clearGcTimeout(),P(this,tn).notify({type:"observerAdded",query:this,observer:a}))}removeObserver(a){this.observers.includes(a)&&(this.observers=this.observers.filter(s=>s!==a),this.observers.length||(P(this,ye)&&(P(this,li)?P(this,ye).cancel({revert:!0}):P(this,ye).cancelRetry()),this.scheduleGc()),P(this,tn).notify({type:"observerRemoved",query:this,observer:a}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||me(this,on,Pn).call(this,{type:"invalidate"})}fetch(a,s){var m,y,v;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(P(this,ye))return P(this,ye).continueRetry(),P(this,ye).promise}if(a&&this.setOptions(a),!this.options.queryFn){const S=this.observers.find(E=>E.options.queryFn);S&&this.setOptions(S.options)}const r=new AbortController,o=S=>{Object.defineProperty(S,"signal",{enumerable:!0,get:()=>(ft(this,li,!0),r.signal)})},c=()=>{const S=b0(this.options,s),x=(()=>{const T={client:P(this,ri),queryKey:this.queryKey,meta:this.meta};return o(T),T})();return ft(this,li,!1),this.options.persister?this.options.persister(S,x,this):S(x)},h=(()=>{const S={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:P(this,ri),state:this.state,fetchFn:c};return o(S),S})();(m=this.options.behavior)==null||m.onFetch(h,this),ft(this,si,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((y=h.fetchOptions)==null?void 0:y.meta))&&me(this,on,Pn).call(this,{type:"fetch",meta:(v=h.fetchOptions)==null?void 0:v.meta});const p=S=>{var E,x,T,w;Mf(S)&&S.silent||me(this,on,Pn).call(this,{type:"error",error:S}),Mf(S)||((x=(E=P(this,tn).config).onError)==null||x.call(E,S,this),(w=(T=P(this,tn).config).onSettled)==null||w.call(T,this.state.data,S,this)),this.scheduleGc()};return ft(this,ye,A0({initialPromise:s==null?void 0:s.initialPromise,fn:h.fetchFn,abort:r.abort.bind(r),onSuccess:S=>{var E,x,T,w;if(S===void 0){p(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(S)}catch(R){p(R);return}(x=(E=P(this,tn).config).onSuccess)==null||x.call(E,S,this),(w=(T=P(this,tn).config).onSettled)==null||w.call(T,S,this.state.error,this),this.scheduleGc()},onError:p,onFail:(S,E)=>{me(this,on,Pn).call(this,{type:"failed",failureCount:S,error:E})},onPause:()=>{me(this,on,Pn).call(this,{type:"pause"})},onContinue:()=>{me(this,on,Pn).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0})),P(this,ye).start()}},us=new WeakMap,si=new WeakMap,tn=new WeakMap,ri=new WeakMap,ye=new WeakMap,Qr=new WeakMap,li=new WeakMap,on=new WeakSet,Pn=function(a){const s=r=>{switch(a.type){case"failed":return{...r,fetchFailureCount:a.failureCount,fetchFailureReason:a.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...IT(r.data,this.options),fetchMeta:a.meta??null};case"success":return ft(this,si,void 0),{...r,data:a.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:a.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!a.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=a.error;return Mf(o)&&o.revert&&P(this,si)?{...P(this,si),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...a.state}}};this.state=s(this.state),xe.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),P(this,tn).notify({query:this,type:"updated",action:a})})},h0);function IT(n,a){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:x0(a.networkMode)?"fetching":"paused",...n===void 0&&{error:null,status:"pending"}}}function tA(n){const a=typeof n.initialData=="function"?n.initialData():n.initialData,s=a!==void 0,r=s?typeof n.initialDataUpdatedAt=="function"?n.initialDataUpdatedAt():n.initialDataUpdatedAt:0;return{data:a,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var vn,m0,eA=(m0=class extends Xo{constructor(a={}){super();wt(this,vn);this.config=a,ft(this,vn,new Map)}build(a,s,r){const o=s.queryKey,c=s.queryHash??Cd(o,s);let d=this.get(c);return d||(d=new WT({client:a,queryKey:o,queryHash:c,options:a.defaultQueryOptions(s),state:r,defaultOptions:a.getQueryDefaults(o)}),this.add(d)),d}add(a){P(this,vn).has(a.queryHash)||(P(this,vn).set(a.queryHash,a),this.notify({type:"added",query:a}))}remove(a){const s=P(this,vn).get(a.queryHash);s&&(a.destroy(),s===a&&P(this,vn).delete(a.queryHash),this.notify({type:"removed",query:a}))}clear(){xe.batch(()=>{this.getAll().forEach(a=>{this.remove(a)})})}get(a){return P(this,vn).get(a)}getAll(){return[...P(this,vn).values()]}find(a){const s={exact:!0,...a};return this.getAll().find(r=>Tg(s,r))}findAll(a={}){const s=this.getAll();return Object.keys(a).length>0?s.filter(r=>Tg(a,r)):s}notify(a){xe.batch(()=>{this.listeners.forEach(s=>{s(a)})})}onFocus(){xe.batch(()=>{this.getAll().forEach(a=>{a.onFocus()})})}onOnline(){xe.batch(()=>{this.getAll().forEach(a=>{a.onOnline()})})}},vn=new WeakMap,m0),bn,Se,oi,Sn,Aa,p0,nA=(p0=class extends E0{constructor(a){super();wt(this,Sn);wt(this,bn);wt(this,Se);wt(this,oi);this.mutationId=a.mutationId,ft(this,Se,a.mutationCache),ft(this,bn,[]),this.state=a.state||aA(),this.setOptions(a.options),this.scheduleGc()}setOptions(a){this.options=a,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(a){P(this,bn).includes(a)||(P(this,bn).push(a),this.clearGcTimeout(),P(this,Se).notify({type:"observerAdded",mutation:this,observer:a}))}removeObserver(a){ft(this,bn,P(this,bn).filter(s=>s!==a)),this.scheduleGc(),P(this,Se).notify({type:"observerRemoved",mutation:this,observer:a})}optionalRemove(){P(this,bn).length||(this.state.status==="pending"?this.scheduleGc():P(this,Se).remove(this))}continue(){var a;return((a=P(this,oi))==null?void 0:a.continue())??this.execute(this.state.variables)}async execute(a){var c,d,h,p,m,y,v,S,E,x,T,w,R,V,U,K,k,$,at,Y;const s=()=>{me(this,Sn,Aa).call(this,{type:"continue"})};ft(this,oi,A0({fn:()=>this.options.mutationFn?this.options.mutationFn(a):Promise.reject(new Error("No mutationFn found")),onFail:(J,mt)=>{me(this,Sn,Aa).call(this,{type:"failed",failureCount:J,error:mt})},onPause:()=>{me(this,Sn,Aa).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>P(this,Se).canRun(this)}));const r=this.state.status==="pending",o=!P(this,oi).canStart();try{if(r)s();else{me(this,Sn,Aa).call(this,{type:"pending",variables:a,isPaused:o}),await((d=(c=P(this,Se).config).onMutate)==null?void 0:d.call(c,a,this));const mt=await((p=(h=this.options).onMutate)==null?void 0:p.call(h,a));mt!==this.state.context&&me(this,Sn,Aa).call(this,{type:"pending",context:mt,variables:a,isPaused:o})}const J=await P(this,oi).start();return await((y=(m=P(this,Se).config).onSuccess)==null?void 0:y.call(m,J,a,this.state.context,this)),await((S=(v=this.options).onSuccess)==null?void 0:S.call(v,J,a,this.state.context)),await((x=(E=P(this,Se).config).onSettled)==null?void 0:x.call(E,J,null,this.state.variables,this.state.context,this)),await((w=(T=this.options).onSettled)==null?void 0:w.call(T,J,null,a,this.state.context)),me(this,Sn,Aa).call(this,{type:"success",data:J}),J}catch(J){try{throw await((V=(R=P(this,Se).config).onError)==null?void 0:V.call(R,J,a,this.state.context,this)),await((K=(U=this.options).onError)==null?void 0:K.call(U,J,a,this.state.context)),await(($=(k=P(this,Se).config).onSettled)==null?void 0:$.call(k,void 0,J,this.state.variables,this.state.context,this)),await((Y=(at=this.options).onSettled)==null?void 0:Y.call(at,void 0,J,a,this.state.context)),J}finally{me(this,Sn,Aa).call(this,{type:"error",error:J})}}finally{P(this,Se).runNext(this)}}},bn=new WeakMap,Se=new WeakMap,oi=new WeakMap,Sn=new WeakSet,Aa=function(a){const s=r=>{switch(a.type){case"failed":return{...r,failureCount:a.failureCount,failureReason:a.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:a.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:a.isPaused,status:"pending",variables:a.variables,submittedAt:Date.now()};case"success":return{...r,data:a.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:a.error,failureCount:r.failureCount+1,failureReason:a.error,isPaused:!1,status:"error"}}};this.state=s(this.state),xe.batch(()=>{P(this,bn).forEach(r=>{r.onMutationUpdate(a)}),P(this,Se).notify({mutation:this,type:"updated",action:a})})},p0);function aA(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Gn,un,Kr,y0,iA=(y0=class extends Xo{constructor(a={}){super();wt(this,Gn);wt(this,un);wt(this,Kr);this.config=a,ft(this,Gn,new Set),ft(this,un,new Map),ft(this,Kr,0)}build(a,s,r){const o=new nA({mutationCache:this,mutationId:++bo(this,Kr)._,options:a.defaultMutationOptions(s),state:r});return this.add(o),o}add(a){P(this,Gn).add(a);const s=So(a);if(typeof s=="string"){const r=P(this,un).get(s);r?r.push(a):P(this,un).set(s,[a])}this.notify({type:"added",mutation:a})}remove(a){if(P(this,Gn).delete(a)){const s=So(a);if(typeof s=="string"){const r=P(this,un).get(s);if(r)if(r.length>1){const o=r.indexOf(a);o!==-1&&r.splice(o,1)}else r[0]===a&&P(this,un).delete(s)}}this.notify({type:"removed",mutation:a})}canRun(a){const s=So(a);if(typeof s=="string"){const r=P(this,un).get(s),o=r==null?void 0:r.find(c=>c.state.status==="pending");return!o||o===a}else return!0}runNext(a){var r;const s=So(a);if(typeof s=="string"){const o=(r=P(this,un).get(s))==null?void 0:r.find(c=>c!==a&&c.state.isPaused);return(o==null?void 0:o.continue())??Promise.resolve()}else return Promise.resolve()}clear(){xe.batch(()=>{P(this,Gn).forEach(a=>{this.notify({type:"removed",mutation:a})}),P(this,Gn).clear(),P(this,un).clear()})}getAll(){return Array.from(P(this,Gn))}find(a){const s={exact:!0,...a};return this.getAll().find(r=>Ag(s,r))}findAll(a={}){return this.getAll().filter(s=>Ag(a,s))}notify(a){xe.batch(()=>{this.listeners.forEach(s=>{s(a)})})}resumePausedMutations(){const a=this.getAll().filter(s=>s.state.isPaused);return xe.batch(()=>Promise.all(a.map(s=>s.continue().catch(ln))))}},Gn=new WeakMap,un=new WeakMap,Kr=new WeakMap,y0);function So(n){var a;return(a=n.options.scope)==null?void 0:a.id}function Rg(n){return{onFetch:(a,s)=>{var y,v,S,E,x;const r=a.options,o=(S=(v=(y=a.fetchOptions)==null?void 0:y.meta)==null?void 0:v.fetchMore)==null?void 0:S.direction,c=((E=a.state.data)==null?void 0:E.pages)||[],d=((x=a.state.data)==null?void 0:x.pageParams)||[];let h={pages:[],pageParams:[]},p=0;const m=async()=>{let T=!1;const w=U=>{Object.defineProperty(U,"signal",{enumerable:!0,get:()=>(a.signal.aborted?T=!0:a.signal.addEventListener("abort",()=>{T=!0}),a.signal)})},R=b0(a.options,a.fetchOptions),V=async(U,K,k)=>{if(T)return Promise.reject();if(K==null&&U.pages.length)return Promise.resolve(U);const at=(()=>{const Vt={client:a.client,queryKey:a.queryKey,pageParam:K,direction:k?"backward":"forward",meta:a.options.meta};return w(Vt),Vt})(),Y=await R(at),{maxPages:J}=a.options,mt=k?XT:YT;return{pages:mt(U.pages,Y,J),pageParams:mt(U.pageParams,K,J)}};if(o&&c.length){const U=o==="backward",K=U?sA:Mg,k={pages:c,pageParams:d},$=K(r,k);h=await V(k,$,U)}else{const U=n??c.length;do{const K=p===0?d[0]??r.initialPageParam:Mg(r,h);if(p>0&&K==null)break;h=await V(h,K),p++}while(p<U)}return h};a.options.persister?a.fetchFn=()=>{var T,w;return(w=(T=a.options).persister)==null?void 0:w.call(T,m,{client:a.client,queryKey:a.queryKey,meta:a.options.meta,signal:a.signal},s)}:a.fetchFn=m}}}function Mg(n,{pages:a,pageParams:s}){const r=a.length-1;return a.length>0?n.getNextPageParam(a[r],a,s[r],s):void 0}function sA(n,{pages:a,pageParams:s}){var r;return a.length>0?(r=n.getPreviousPageParam)==null?void 0:r.call(n,a[0],a,s[0],s):void 0}var Xt,Ma,Ca,cs,fs,Oa,ds,hs,g0,rA=(g0=class{constructor(n={}){wt(this,Xt);wt(this,Ma);wt(this,Ca);wt(this,cs);wt(this,fs);wt(this,Oa);wt(this,ds);wt(this,hs);ft(this,Xt,n.queryCache||new eA),ft(this,Ma,n.mutationCache||new iA),ft(this,Ca,n.defaultOptions||{}),ft(this,cs,new Map),ft(this,fs,new Map),ft(this,Oa,0)}mount(){bo(this,Oa)._++,P(this,Oa)===1&&(ft(this,ds,S0.subscribe(async n=>{n&&(await this.resumePausedMutations(),P(this,Xt).onFocus())})),ft(this,hs,Vo.subscribe(async n=>{n&&(await this.resumePausedMutations(),P(this,Xt).onOnline())})))}unmount(){var n,a;bo(this,Oa)._--,P(this,Oa)===0&&((n=P(this,ds))==null||n.call(this),ft(this,ds,void 0),(a=P(this,hs))==null||a.call(this),ft(this,hs,void 0))}isFetching(n){return P(this,Xt).findAll({...n,fetchStatus:"fetching"}).length}isMutating(n){return P(this,Ma).findAll({...n,status:"pending"}).length}getQueryData(n){var s;const a=this.defaultQueryOptions({queryKey:n});return(s=P(this,Xt).get(a.queryHash))==null?void 0:s.state.data}ensureQueryData(n){const a=this.defaultQueryOptions(n),s=P(this,Xt).build(this,a),r=s.state.data;return r===void 0?this.fetchQuery(n):(n.revalidateIfStale&&s.isStaleByTime(Ff(a.staleTime,s))&&this.prefetchQuery(a),Promise.resolve(r))}getQueriesData(n){return P(this,Xt).findAll(n).map(({queryKey:a,state:s})=>{const r=s.data;return[a,r]})}setQueryData(n,a,s){const r=this.defaultQueryOptions({queryKey:n}),o=P(this,Xt).get(r.queryHash),c=o==null?void 0:o.state.data,d=jT(a,c);if(d!==void 0)return P(this,Xt).build(this,r).setData(d,{...s,manual:!0})}setQueriesData(n,a,s){return xe.batch(()=>P(this,Xt).findAll(n).map(({queryKey:r})=>[r,this.setQueryData(r,a,s)]))}getQueryState(n){var s;const a=this.defaultQueryOptions({queryKey:n});return(s=P(this,Xt).get(a.queryHash))==null?void 0:s.state}removeQueries(n){const a=P(this,Xt);xe.batch(()=>{a.findAll(n).forEach(s=>{a.remove(s)})})}resetQueries(n,a){const s=P(this,Xt);return xe.batch(()=>(s.findAll(n).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...n},a)))}cancelQueries(n,a={}){const s={revert:!0,...a},r=xe.batch(()=>P(this,Xt).findAll(n).map(o=>o.cancel(s)));return Promise.all(r).then(ln).catch(ln)}invalidateQueries(n,a={}){return xe.batch(()=>(P(this,Xt).findAll(n).forEach(s=>{s.invalidate()}),(n==null?void 0:n.refetchType)==="none"?Promise.resolve():this.refetchQueries({...n,type:(n==null?void 0:n.refetchType)??(n==null?void 0:n.type)??"active"},a)))}refetchQueries(n,a={}){const s={...a,cancelRefetch:a.cancelRefetch??!0},r=xe.batch(()=>P(this,Xt).findAll(n).filter(o=>!o.isDisabled()&&!o.isStatic()).map(o=>{let c=o.fetch(void 0,s);return s.throwOnError||(c=c.catch(ln)),o.state.fetchStatus==="paused"?Promise.resolve():c}));return Promise.all(r).then(ln)}fetchQuery(n){const a=this.defaultQueryOptions(n);a.retry===void 0&&(a.retry=!1);const s=P(this,Xt).build(this,a);return s.isStaleByTime(Ff(a.staleTime,s))?s.fetch(a):Promise.resolve(s.state.data)}prefetchQuery(n){return this.fetchQuery(n).then(ln).catch(ln)}fetchInfiniteQuery(n){return n.behavior=Rg(n.pages),this.fetchQuery(n)}prefetchInfiniteQuery(n){return this.fetchInfiniteQuery(n).then(ln).catch(ln)}ensureInfiniteQueryData(n){return n.behavior=Rg(n.pages),this.ensureQueryData(n)}resumePausedMutations(){return Vo.isOnline()?P(this,Ma).resumePausedMutations():Promise.resolve()}getQueryCache(){return P(this,Xt)}getMutationCache(){return P(this,Ma)}getDefaultOptions(){return P(this,Ca)}setDefaultOptions(n){ft(this,Ca,n)}setQueryDefaults(n,a){P(this,cs).set(Lr(n),{queryKey:n,defaultOptions:a})}getQueryDefaults(n){const a=[...P(this,cs).values()],s={};return a.forEach(r=>{Vr(n,r.queryKey)&&Object.assign(s,r.defaultOptions)}),s}setMutationDefaults(n,a){P(this,fs).set(Lr(n),{mutationKey:n,defaultOptions:a})}getMutationDefaults(n){const a=[...P(this,fs).values()],s={};return a.forEach(r=>{Vr(n,r.mutationKey)&&Object.assign(s,r.defaultOptions)}),s}defaultQueryOptions(n){if(n._defaulted)return n;const a={...P(this,Ca).queries,...this.getQueryDefaults(n.queryKey),...n,_defaulted:!0};return a.queryHash||(a.queryHash=Cd(a.queryKey,a)),a.refetchOnReconnect===void 0&&(a.refetchOnReconnect=a.networkMode!=="always"),a.throwOnError===void 0&&(a.throwOnError=!!a.suspense),!a.networkMode&&a.persister&&(a.networkMode="offlineFirst"),a.queryFn===Od&&(a.enabled=!1),a}defaultMutationOptions(n){return n!=null&&n._defaulted?n:{...P(this,Ca).mutations,...(n==null?void 0:n.mutationKey)&&this.getMutationDefaults(n.mutationKey),...n,_defaulted:!0}}clear(){P(this,Xt).clear(),P(this,Ma).clear()}},Xt=new WeakMap,Ma=new WeakMap,Ca=new WeakMap,cs=new WeakMap,fs=new WeakMap,Oa=new WeakMap,ds=new WeakMap,hs=new WeakMap,g0),lA=z.createContext(void 0),oA=({client:n,children:a})=>(z.useEffect(()=>(n.mount(),()=>{n.unmount()}),[n]),it.jsx(lA.Provider,{value:n,children:a})),Ar={},Cg;function uA(){if(Cg)return Ar;Cg=1,Object.defineProperty(Ar,"__esModule",{value:!0}),Ar.parse=d,Ar.serialize=m;const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,a=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,c=(()=>{const S=function(){};return S.prototype=Object.create(null),S})();function d(S,E){const x=new c,T=S.length;if(T<2)return x;const w=(E==null?void 0:E.decode)||y;let R=0;do{const V=S.indexOf("=",R);if(V===-1)break;const U=S.indexOf(";",R),K=U===-1?T:U;if(V>K){R=S.lastIndexOf(";",V-1)+1;continue}const k=h(S,R,V),$=p(S,V,k),at=S.slice(k,$);if(x[at]===void 0){let Y=h(S,V+1,K),J=p(S,K,Y);const mt=w(S.slice(Y,J));x[at]=mt}R=K+1}while(R<T);return x}function h(S,E,x){do{const T=S.charCodeAt(E);if(T!==32&&T!==9)return E}while(++E<x);return x}function p(S,E,x){for(;E>x;){const T=S.charCodeAt(--E);if(T!==32&&T!==9)return E+1}return x}function m(S,E,x){const T=(x==null?void 0:x.encode)||encodeURIComponent;if(!n.test(S))throw new TypeError(`argument name is invalid: ${S}`);const w=T(E);if(!a.test(w))throw new TypeError(`argument val is invalid: ${E}`);let R=S+"="+w;if(!x)return R;if(x.maxAge!==void 0){if(!Number.isInteger(x.maxAge))throw new TypeError(`option maxAge is invalid: ${x.maxAge}`);R+="; Max-Age="+x.maxAge}if(x.domain){if(!s.test(x.domain))throw new TypeError(`option domain is invalid: ${x.domain}`);R+="; Domain="+x.domain}if(x.path){if(!r.test(x.path))throw new TypeError(`option path is invalid: ${x.path}`);R+="; Path="+x.path}if(x.expires){if(!v(x.expires)||!Number.isFinite(x.expires.valueOf()))throw new TypeError(`option expires is invalid: ${x.expires}`);R+="; Expires="+x.expires.toUTCString()}if(x.httpOnly&&(R+="; HttpOnly"),x.secure&&(R+="; Secure"),x.partitioned&&(R+="; Partitioned"),x.priority)switch(typeof x.priority=="string"?x.priority.toLowerCase():void 0){case"low":R+="; Priority=Low";break;case"medium":R+="; Priority=Medium";break;case"high":R+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${x.priority}`)}if(x.sameSite)switch(typeof x.sameSite=="string"?x.sameSite.toLowerCase():x.sameSite){case!0:case"strict":R+="; SameSite=Strict";break;case"lax":R+="; SameSite=Lax";break;case"none":R+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${x.sameSite}`)}return R}function y(S){if(S.indexOf("%")===-1)return S;try{return decodeURIComponent(S)}catch{return S}}function v(S){return o.call(S)==="[object Date]"}return Ar}uA();var Og="popstate";function cA(n={}){function a(r,o){let{pathname:c,search:d,hash:h}=r.location;return $f("",{pathname:c,search:d,hash:h},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function s(r,o){return typeof o=="string"?o:Br(o)}return dA(a,s,null,n)}function ae(n,a){if(n===!1||n===null||typeof n>"u")throw new Error(a)}function En(n,a){if(!n){typeof console<"u"&&console.warn(a);try{throw new Error(a)}catch{}}}function fA(){return Math.random().toString(36).substring(2,10)}function Dg(n,a){return{usr:n.state,key:n.key,idx:a}}function $f(n,a,s=null,r){return{pathname:typeof n=="string"?n:n.pathname,search:"",hash:"",...typeof a=="string"?Fr(a):a,state:s,key:a&&a.key||r||fA()}}function Br({pathname:n="/",search:a="",hash:s=""}){return a&&a!=="?"&&(n+=a.charAt(0)==="?"?a:"?"+a),s&&s!=="#"&&(n+=s.charAt(0)==="#"?s:"#"+s),n}function Fr(n){let a={};if(n){let s=n.indexOf("#");s>=0&&(a.hash=n.substring(s),n=n.substring(0,s));let r=n.indexOf("?");r>=0&&(a.search=n.substring(r),n=n.substring(0,r)),n&&(a.pathname=n)}return a}function dA(n,a,s,r={}){let{window:o=document.defaultView,v5Compat:c=!1}=r,d=o.history,h="POP",p=null,m=y();m==null&&(m=0,d.replaceState({...d.state,idx:m},""));function y(){return(d.state||{idx:null}).idx}function v(){h="POP";let w=y(),R=w==null?null:w-m;m=w,p&&p({action:h,location:T.location,delta:R})}function S(w,R){h="PUSH";let V=$f(T.location,w,R);m=y()+1;let U=Dg(V,m),K=T.createHref(V);try{d.pushState(U,"",K)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;o.location.assign(K)}c&&p&&p({action:h,location:T.location,delta:1})}function E(w,R){h="REPLACE";let V=$f(T.location,w,R);m=y();let U=Dg(V,m),K=T.createHref(V);d.replaceState(U,"",K),c&&p&&p({action:h,location:T.location,delta:0})}function x(w){return hA(w)}let T={get action(){return h},get location(){return n(o,d)},listen(w){if(p)throw new Error("A history only accepts one active listener");return o.addEventListener(Og,v),p=w,()=>{o.removeEventListener(Og,v),p=null}},createHref(w){return a(o,w)},createURL:x,encodeLocation(w){let R=x(w);return{pathname:R.pathname,search:R.search,hash:R.hash}},push:S,replace:E,go(w){return d.go(w)}};return T}function hA(n,a=!1){let s="http://localhost";typeof window<"u"&&(s=window.location.origin!=="null"?window.location.origin:window.location.href),ae(s,"No window.location.(origin|href) available to create URL");let r=typeof n=="string"?n:Br(n);return r=r.replace(/ $/,"%20"),!a&&r.startsWith("//")&&(r=s+r),new URL(r,s)}function w0(n,a,s="/"){return mA(n,a,s,!1)}function mA(n,a,s,r){let o=typeof a=="string"?Fr(a):a,c=Xn(o.pathname||"/",s);if(c==null)return null;let d=R0(n);pA(d);let h=null;for(let p=0;h==null&&p<d.length;++p){let m=RA(c);h=EA(d[p],m,r)}return h}function R0(n,a=[],s=[],r=""){let o=(c,d,h)=>{let p={relativePath:h===void 0?c.path||"":h,caseSensitive:c.caseSensitive===!0,childrenIndex:d,route:c};p.relativePath.startsWith("/")&&(ae(p.relativePath.startsWith(r),`Absolute route path "${p.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(r.length));let m=Yn([r,p.relativePath]),y=s.concat(p);c.children&&c.children.length>0&&(ae(c.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),R0(c.children,a,y,m)),!(c.path==null&&!c.index)&&a.push({path:m,score:TA(m,c.index),routesMeta:y})};return n.forEach((c,d)=>{var h;if(c.path===""||!((h=c.path)!=null&&h.includes("?")))o(c,d);else for(let p of M0(c.path))o(c,d,p)}),a}function M0(n){let a=n.split("/");if(a.length===0)return[];let[s,...r]=a,o=s.endsWith("?"),c=s.replace(/\?$/,"");if(r.length===0)return o?[c,""]:[c];let d=M0(r.join("/")),h=[];return h.push(...d.map(p=>p===""?c:[c,p].join("/"))),o&&h.push(...d),h.map(p=>n.startsWith("/")&&p===""?"/":p)}function pA(n){n.sort((a,s)=>a.score!==s.score?s.score-a.score:AA(a.routesMeta.map(r=>r.childrenIndex),s.routesMeta.map(r=>r.childrenIndex)))}var yA=/^:[\w-]+$/,gA=3,vA=2,bA=1,SA=10,xA=-2,Ug=n=>n==="*";function TA(n,a){let s=n.split("/"),r=s.length;return s.some(Ug)&&(r+=xA),a&&(r+=vA),s.filter(o=>!Ug(o)).reduce((o,c)=>o+(yA.test(c)?gA:c===""?bA:SA),r)}function AA(n,a){return n.length===a.length&&n.slice(0,-1).every((r,o)=>r===a[o])?n[n.length-1]-a[a.length-1]:0}function EA(n,a,s=!1){let{routesMeta:r}=n,o={},c="/",d=[];for(let h=0;h<r.length;++h){let p=r[h],m=h===r.length-1,y=c==="/"?a:a.slice(c.length)||"/",v=Bo({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},y),S=p.route;if(!v&&m&&s&&!r[r.length-1].route.index&&(v=Bo({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},y)),!v)return null;Object.assign(o,v.params),d.push({params:o,pathname:Yn([c,v.pathname]),pathnameBase:DA(Yn([c,v.pathnameBase])),route:S}),v.pathnameBase!=="/"&&(c=Yn([c,v.pathnameBase]))}return d}function Bo(n,a){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[s,r]=wA(n.path,n.caseSensitive,n.end),o=a.match(s);if(!o)return null;let c=o[0],d=c.replace(/(.)\/+$/,"$1"),h=o.slice(1);return{params:r.reduce((m,{paramName:y,isOptional:v},S)=>{if(y==="*"){let x=h[S]||"";d=c.slice(0,c.length-x.length).replace(/(.)\/+$/,"$1")}const E=h[S];return v&&!E?m[y]=void 0:m[y]=(E||"").replace(/%2F/g,"/"),m},{}),pathname:c,pathnameBase:d,pattern:n}}function wA(n,a=!1,s=!0){En(n==="*"||!n.endsWith("*")||n.endsWith("/*"),`Route path "${n}" will be treated as if it were "${n.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${n.replace(/\*$/,"/*")}".`);let r=[],o="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,h,p)=>(r.push({paramName:h,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(r.push({paramName:"*"}),o+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?o+="\\/*$":n!==""&&n!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,a?void 0:"i"),r]}function RA(n){try{return n.split("/").map(a=>decodeURIComponent(a).replace(/\//g,"%2F")).join("/")}catch(a){return En(!1,`The URL path "${n}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${a}).`),n}}function Xn(n,a){if(a==="/")return n;if(!n.toLowerCase().startsWith(a.toLowerCase()))return null;let s=a.endsWith("/")?a.length-1:a.length,r=n.charAt(s);return r&&r!=="/"?null:n.slice(s)||"/"}function MA(n,a="/"){let{pathname:s,search:r="",hash:o=""}=typeof n=="string"?Fr(n):n;return{pathname:s?s.startsWith("/")?s:CA(s,a):a,search:UA(r),hash:NA(o)}}function CA(n,a){let s=a.replace(/\/+$/,"").split("/");return n.split("/").forEach(o=>{o===".."?s.length>1&&s.pop():o!=="."&&s.push(o)}),s.length>1?s.join("/"):"/"}function Cf(n,a,s,r){return`Cannot include a '${n}' character in a manually specified \`to.${a}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${s}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function OA(n){return n.filter((a,s)=>s===0||a.route.path&&a.route.path.length>0)}function C0(n){let a=OA(n);return a.map((s,r)=>r===a.length-1?s.pathname:s.pathnameBase)}function O0(n,a,s,r=!1){let o;typeof n=="string"?o=Fr(n):(o={...n},ae(!o.pathname||!o.pathname.includes("?"),Cf("?","pathname","search",o)),ae(!o.pathname||!o.pathname.includes("#"),Cf("#","pathname","hash",o)),ae(!o.search||!o.search.includes("#"),Cf("#","search","hash",o)));let c=n===""||o.pathname==="",d=c?"/":o.pathname,h;if(d==null)h=s;else{let v=a.length-1;if(!r&&d.startsWith("..")){let S=d.split("/");for(;S[0]==="..";)S.shift(),v-=1;o.pathname=S.join("/")}h=v>=0?a[v]:"/"}let p=MA(o,h),m=d&&d!=="/"&&d.endsWith("/"),y=(c||d===".")&&s.endsWith("/");return!p.pathname.endsWith("/")&&(m||y)&&(p.pathname+="/"),p}var Yn=n=>n.join("/").replace(/\/\/+/g,"/"),DA=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),UA=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,NA=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n;function zA(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}var D0=["POST","PUT","PATCH","DELETE"];new Set(D0);var _A=["GET",...D0];new Set(_A);var ys=z.createContext(null);ys.displayName="DataRouter";var Ko=z.createContext(null);Ko.displayName="DataRouterState";var U0=z.createContext({isTransitioning:!1});U0.displayName="ViewTransition";var LA=z.createContext(new Map);LA.displayName="Fetchers";var VA=z.createContext(null);VA.displayName="Await";var wn=z.createContext(null);wn.displayName="Navigation";var Fo=z.createContext(null);Fo.displayName="Location";var Fn=z.createContext({outlet:null,matches:[],isDataRoute:!1});Fn.displayName="Route";var Dd=z.createContext(null);Dd.displayName="RouteError";function BA(n,{relative:a}={}){ae(Zr(),"useHref() may be used only in the context of a <Router> component.");let{basename:s,navigator:r}=z.useContext(wn),{hash:o,pathname:c,search:d}=$r(n,{relative:a}),h=c;return s!=="/"&&(h=c==="/"?s:Yn([s,c])),r.createHref({pathname:h,search:d,hash:o})}function Zr(){return z.useContext(Fo)!=null}function hi(){return ae(Zr(),"useLocation() may be used only in the context of a <Router> component."),z.useContext(Fo).location}var N0="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function z0(n){z.useContext(wn).static||z.useLayoutEffect(n)}function jA(){let{isDataRoute:n}=z.useContext(Fn);return n?JA():kA()}function kA(){ae(Zr(),"useNavigate() may be used only in the context of a <Router> component.");let n=z.useContext(ys),{basename:a,navigator:s}=z.useContext(wn),{matches:r}=z.useContext(Fn),{pathname:o}=hi(),c=JSON.stringify(C0(r)),d=z.useRef(!1);return z0(()=>{d.current=!0}),z.useCallback((p,m={})=>{if(En(d.current,N0),!d.current)return;if(typeof p=="number"){s.go(p);return}let y=O0(p,JSON.parse(c),o,m.relative==="path");n==null&&a!=="/"&&(y.pathname=y.pathname==="/"?a:Yn([a,y.pathname])),(m.replace?s.replace:s.push)(y,m.state,m)},[a,s,c,o,n])}z.createContext(null);function $r(n,{relative:a}={}){let{matches:s}=z.useContext(Fn),{pathname:r}=hi(),o=JSON.stringify(C0(s));return z.useMemo(()=>O0(n,JSON.parse(o),r,a==="path"),[n,o,r,a])}function HA(n,a,s,r){ae(Zr(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=z.useContext(wn),{matches:c}=z.useContext(Fn),d=c[c.length-1],h=d?d.params:{},p=d?d.pathname:"/",m=d?d.pathnameBase:"/",y=d&&d.route;{let R=y&&y.path||"";_0(p,!y||R.endsWith("*")||R.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${p}" (under <Route path="${R}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${R}"> to <Route path="${R==="/"?"*":`${R}/*`}">.`)}let v=hi(),S;S=v;let E=S.pathname||"/",x=E;if(m!=="/"){let R=m.replace(/^\//,"").split("/");x="/"+E.replace(/^\//,"").split("/").slice(R.length).join("/")}let T=w0(n,{pathname:x});return En(y||T!=null,`No routes matched location "${S.pathname}${S.search}${S.hash}" `),En(T==null||T[T.length-1].route.element!==void 0||T[T.length-1].route.Component!==void 0||T[T.length-1].route.lazy!==void 0,`Matched leaf route at location "${S.pathname}${S.search}${S.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),XA(T&&T.map(R=>Object.assign({},R,{params:Object.assign({},h,R.params),pathname:Yn([m,o.encodeLocation?o.encodeLocation(R.pathname).pathname:R.pathname]),pathnameBase:R.pathnameBase==="/"?m:Yn([m,o.encodeLocation?o.encodeLocation(R.pathnameBase).pathname:R.pathnameBase])})),c,s,r)}function qA(){let n=$A(),a=zA(n)?`${n.status} ${n.statusText}`:n instanceof Error?n.message:JSON.stringify(n),s=n instanceof Error?n.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},c={padding:"2px 4px",backgroundColor:r},d=null;return console.error("Error handled by React Router default ErrorBoundary:",n),d=z.createElement(z.Fragment,null,z.createElement("p",null,"💿 Hey developer 👋"),z.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",z.createElement("code",{style:c},"ErrorBoundary")," or"," ",z.createElement("code",{style:c},"errorElement")," prop on your route.")),z.createElement(z.Fragment,null,z.createElement("h2",null,"Unexpected Application Error!"),z.createElement("h3",{style:{fontStyle:"italic"}},a),s?z.createElement("pre",{style:o},s):null,d)}var PA=z.createElement(qA,null),GA=class extends z.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,a){return a.location!==n.location||a.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:a.error,location:a.location,revalidation:n.revalidation||a.revalidation}}componentDidCatch(n,a){console.error("React Router caught the following error during render",n,a)}render(){return this.state.error!==void 0?z.createElement(Fn.Provider,{value:this.props.routeContext},z.createElement(Dd.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function YA({routeContext:n,match:a,children:s}){let r=z.useContext(ys);return r&&r.static&&r.staticContext&&(a.route.errorElement||a.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=a.route.id),z.createElement(Fn.Provider,{value:n},s)}function XA(n,a=[],s=null,r=null){if(n==null){if(!s)return null;if(s.errors)n=s.matches;else if(a.length===0&&!s.initialized&&s.matches.length>0)n=s.matches;else return null}let o=n,c=s==null?void 0:s.errors;if(c!=null){let p=o.findIndex(m=>m.route.id&&(c==null?void 0:c[m.route.id])!==void 0);ae(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(c).join(",")}`),o=o.slice(0,Math.min(o.length,p+1))}let d=!1,h=-1;if(s)for(let p=0;p<o.length;p++){let m=o[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(h=p),m.route.id){let{loaderData:y,errors:v}=s,S=m.route.loader&&!y.hasOwnProperty(m.route.id)&&(!v||v[m.route.id]===void 0);if(m.route.lazy||S){d=!0,h>=0?o=o.slice(0,h+1):o=[o[0]];break}}}return o.reduceRight((p,m,y)=>{let v,S=!1,E=null,x=null;s&&(v=c&&m.route.id?c[m.route.id]:void 0,E=m.route.errorElement||PA,d&&(h<0&&y===0?(_0("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),S=!0,x=null):h===y&&(S=!0,x=m.route.hydrateFallbackElement||null)));let T=a.concat(o.slice(0,y+1)),w=()=>{let R;return v?R=E:S?R=x:m.route.Component?R=z.createElement(m.route.Component,null):m.route.element?R=m.route.element:R=p,z.createElement(YA,{match:m,routeContext:{outlet:p,matches:T,isDataRoute:s!=null},children:R})};return s&&(m.route.ErrorBoundary||m.route.errorElement||y===0)?z.createElement(GA,{location:s.location,revalidation:s.revalidation,component:E,error:v,children:w(),routeContext:{outlet:null,matches:T,isDataRoute:!0}}):w()},null)}function Ud(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function QA(n){let a=z.useContext(ys);return ae(a,Ud(n)),a}function KA(n){let a=z.useContext(Ko);return ae(a,Ud(n)),a}function FA(n){let a=z.useContext(Fn);return ae(a,Ud(n)),a}function Nd(n){let a=FA(n),s=a.matches[a.matches.length-1];return ae(s.route.id,`${n} can only be used on routes that contain a unique "id"`),s.route.id}function ZA(){return Nd("useRouteId")}function $A(){var r;let n=z.useContext(Dd),a=KA("useRouteError"),s=Nd("useRouteError");return n!==void 0?n:(r=a.errors)==null?void 0:r[s]}function JA(){let{router:n}=QA("useNavigate"),a=Nd("useNavigate"),s=z.useRef(!1);return z0(()=>{s.current=!0}),z.useCallback(async(o,c={})=>{En(s.current,N0),s.current&&(typeof o=="number"?n.navigate(o):await n.navigate(o,{fromRouteId:a,...c}))},[n,a])}var Ng={};function _0(n,a,s){!a&&!Ng[n]&&(Ng[n]=!0,En(!1,s))}z.memo(WA);function WA({routes:n,future:a,state:s}){return HA(n,void 0,s,a)}function IA({basename:n="/",children:a=null,location:s,navigationType:r="POP",navigator:o,static:c=!1}){ae(!Zr(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=n.replace(/^\/*/,"/"),h=z.useMemo(()=>({basename:d,navigator:o,static:c,future:{}}),[d,o,c]);typeof s=="string"&&(s=Fr(s));let{pathname:p="/",search:m="",hash:y="",state:v=null,key:S="default"}=s,E=z.useMemo(()=>{let x=Xn(p,d);return x==null?null:{location:{pathname:x,search:m,hash:y,state:v,key:S},navigationType:r}},[d,p,m,y,v,S,r]);return En(E!=null,`<Router basename="${d}"> is not able to match the URL "${p}${m}${y}" because it does not start with the basename, so the <Router> won't render anything.`),E==null?null:z.createElement(wn.Provider,{value:h},z.createElement(Fo.Provider,{children:a,value:E}))}var Mo="get",Co="application/x-www-form-urlencoded";function Zo(n){return n!=null&&typeof n.tagName=="string"}function tE(n){return Zo(n)&&n.tagName.toLowerCase()==="button"}function eE(n){return Zo(n)&&n.tagName.toLowerCase()==="form"}function nE(n){return Zo(n)&&n.tagName.toLowerCase()==="input"}function aE(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function iE(n,a){return n.button===0&&(!a||a==="_self")&&!aE(n)}var xo=null;function sE(){if(xo===null)try{new FormData(document.createElement("form"),0),xo=!1}catch{xo=!0}return xo}var rE=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Of(n){return n!=null&&!rE.has(n)?(En(!1,`"${n}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Co}"`),null):n}function lE(n,a){let s,r,o,c,d;if(eE(n)){let h=n.getAttribute("action");r=h?Xn(h,a):null,s=n.getAttribute("method")||Mo,o=Of(n.getAttribute("enctype"))||Co,c=new FormData(n)}else if(tE(n)||nE(n)&&(n.type==="submit"||n.type==="image")){let h=n.form;if(h==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=n.getAttribute("formaction")||h.getAttribute("action");if(r=p?Xn(p,a):null,s=n.getAttribute("formmethod")||h.getAttribute("method")||Mo,o=Of(n.getAttribute("formenctype"))||Of(h.getAttribute("enctype"))||Co,c=new FormData(h,n),!sE()){let{name:m,type:y,value:v}=n;if(y==="image"){let S=m?`${m}.`:"";c.append(`${S}x`,"0"),c.append(`${S}y`,"0")}else m&&c.append(m,v)}}else{if(Zo(n))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');s=Mo,r=null,o=Co,d=n}return c&&o==="text/plain"&&(d=c,c=void 0),{action:r,method:s.toLowerCase(),encType:o,formData:c,body:d}}function zd(n,a){if(n===!1||n===null||typeof n>"u")throw new Error(a)}async function oE(n,a){if(n.id in a)return a[n.id];try{let s=await import(n.module);return a[n.id]=s,s}catch(s){return console.error(`Error loading route module \`${n.module}\`, reloading page...`),console.error(s),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function uE(n){return n==null?!1:n.href==null?n.rel==="preload"&&typeof n.imageSrcSet=="string"&&typeof n.imageSizes=="string":typeof n.rel=="string"&&typeof n.href=="string"}async function cE(n,a,s){let r=await Promise.all(n.map(async o=>{let c=a.routes[o.route.id];if(c){let d=await oE(c,s);return d.links?d.links():[]}return[]}));return mE(r.flat(1).filter(uE).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function zg(n,a,s,r,o,c){let d=(p,m)=>s[m]?p.route.id!==s[m].route.id:!0,h=(p,m)=>{var y;return s[m].pathname!==p.pathname||((y=s[m].route.path)==null?void 0:y.endsWith("*"))&&s[m].params["*"]!==p.params["*"]};return c==="assets"?a.filter((p,m)=>d(p,m)||h(p,m)):c==="data"?a.filter((p,m)=>{var v;let y=r.routes[p.route.id];if(!y||!y.hasLoader)return!1;if(d(p,m)||h(p,m))return!0;if(p.route.shouldRevalidate){let S=p.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:((v=s[0])==null?void 0:v.params)||{},nextUrl:new URL(n,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof S=="boolean")return S}return!0}):[]}function fE(n,a,{includeHydrateFallback:s}={}){return dE(n.map(r=>{let o=a.routes[r.route.id];if(!o)return[];let c=[o.module];return o.clientActionModule&&(c=c.concat(o.clientActionModule)),o.clientLoaderModule&&(c=c.concat(o.clientLoaderModule)),s&&o.hydrateFallbackModule&&(c=c.concat(o.hydrateFallbackModule)),o.imports&&(c=c.concat(o.imports)),c}).flat(1))}function dE(n){return[...new Set(n)]}function hE(n){let a={},s=Object.keys(n).sort();for(let r of s)a[r]=n[r];return a}function mE(n,a){let s=new Set;return new Set(a),n.reduce((r,o)=>{let c=JSON.stringify(hE(o));return s.has(c)||(s.add(c),r.push({key:c,link:o})),r},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var pE=new Set([100,101,204,205]);function yE(n,a){let s=typeof n=="string"?new URL(n,typeof window>"u"?"server://singlefetch/":window.location.origin):n;return s.pathname==="/"?s.pathname="_root.data":a&&Xn(s.pathname,a)==="/"?s.pathname=`${a.replace(/\/$/,"")}/_root.data`:s.pathname=`${s.pathname.replace(/\/$/,"")}.data`,s}function L0(){let n=z.useContext(ys);return zd(n,"You must render this element inside a <DataRouterContext.Provider> element"),n}function gE(){let n=z.useContext(Ko);return zd(n,"You must render this element inside a <DataRouterStateContext.Provider> element"),n}var _d=z.createContext(void 0);_d.displayName="FrameworkContext";function V0(){let n=z.useContext(_d);return zd(n,"You must render this element inside a <HydratedRouter> element"),n}function vE(n,a){let s=z.useContext(_d),[r,o]=z.useState(!1),[c,d]=z.useState(!1),{onFocus:h,onBlur:p,onMouseEnter:m,onMouseLeave:y,onTouchStart:v}=a,S=z.useRef(null);z.useEffect(()=>{if(n==="render"&&d(!0),n==="viewport"){let T=R=>{R.forEach(V=>{d(V.isIntersecting)})},w=new IntersectionObserver(T,{threshold:.5});return S.current&&w.observe(S.current),()=>{w.disconnect()}}},[n]),z.useEffect(()=>{if(r){let T=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(T)}}},[r]);let E=()=>{o(!0)},x=()=>{o(!1),d(!1)};return s?n!=="intent"?[c,S,{}]:[c,S,{onFocus:Er(h,E),onBlur:Er(p,x),onMouseEnter:Er(m,E),onMouseLeave:Er(y,x),onTouchStart:Er(v,E)}]:[!1,S,{}]}function Er(n,a){return s=>{n&&n(s),s.defaultPrevented||a(s)}}function bE({page:n,...a}){let{router:s}=L0(),r=z.useMemo(()=>w0(s.routes,n,s.basename),[s.routes,n,s.basename]);return r?z.createElement(xE,{page:n,matches:r,...a}):null}function SE(n){let{manifest:a,routeModules:s}=V0(),[r,o]=z.useState([]);return z.useEffect(()=>{let c=!1;return cE(n,a,s).then(d=>{c||o(d)}),()=>{c=!0}},[n,a,s]),r}function xE({page:n,matches:a,...s}){let r=hi(),{manifest:o,routeModules:c}=V0(),{basename:d}=L0(),{loaderData:h,matches:p}=gE(),m=z.useMemo(()=>zg(n,a,p,o,r,"data"),[n,a,p,o,r]),y=z.useMemo(()=>zg(n,a,p,o,r,"assets"),[n,a,p,o,r]),v=z.useMemo(()=>{if(n===r.pathname+r.search+r.hash)return[];let x=new Set,T=!1;if(a.forEach(R=>{var U;let V=o.routes[R.route.id];!V||!V.hasLoader||(!m.some(K=>K.route.id===R.route.id)&&R.route.id in h&&((U=c[R.route.id])!=null&&U.shouldRevalidate)||V.hasClientLoader?T=!0:x.add(R.route.id))}),x.size===0)return[];let w=yE(n,d);return T&&x.size>0&&w.searchParams.set("_routes",a.filter(R=>x.has(R.route.id)).map(R=>R.route.id).join(",")),[w.pathname+w.search]},[d,h,r,o,m,a,n,c]),S=z.useMemo(()=>fE(y,o),[y,o]),E=SE(y);return z.createElement(z.Fragment,null,v.map(x=>z.createElement("link",{key:x,rel:"prefetch",as:"fetch",href:x,...s})),S.map(x=>z.createElement("link",{key:x,rel:"modulepreload",href:x,...s})),E.map(({key:x,link:T})=>z.createElement("link",{key:x,...T})))}function TE(...n){return a=>{n.forEach(s=>{typeof s=="function"?s(a):s!=null&&(s.current=a)})}}var B0=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{B0&&(window.__reactRouterVersion="7.6.2")}catch{}function AE({basename:n,children:a,window:s}){let r=z.useRef();r.current==null&&(r.current=cA({window:s,v5Compat:!0}));let o=r.current,[c,d]=z.useState({action:o.action,location:o.location}),h=z.useCallback(p=>{z.startTransition(()=>d(p))},[d]);return z.useLayoutEffect(()=>o.listen(h),[o,h]),z.createElement(IA,{basename:n,children:a,location:c.location,navigationType:c.action,navigator:o})}var j0=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,k0=z.forwardRef(function({onClick:a,discover:s="render",prefetch:r="none",relative:o,reloadDocument:c,replace:d,state:h,target:p,to:m,preventScrollReset:y,viewTransition:v,...S},E){let{basename:x}=z.useContext(wn),T=typeof m=="string"&&j0.test(m),w,R=!1;if(typeof m=="string"&&T&&(w=m,B0))try{let J=new URL(window.location.href),mt=m.startsWith("//")?new URL(J.protocol+m):new URL(m),Vt=Xn(mt.pathname,x);mt.origin===J.origin&&Vt!=null?m=Vt+mt.search+mt.hash:R=!0}catch{En(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let V=BA(m,{relative:o}),[U,K,k]=vE(r,S),$=ME(m,{replace:d,state:h,target:p,preventScrollReset:y,relative:o,viewTransition:v});function at(J){a&&a(J),J.defaultPrevented||$(J)}let Y=z.createElement("a",{...S,...k,href:w||V,onClick:R||c?a:at,ref:TE(E,K),target:p,"data-discover":!T&&s==="render"?"true":void 0});return U&&!T?z.createElement(z.Fragment,null,Y,z.createElement(bE,{page:V})):Y});k0.displayName="Link";var EE=z.forwardRef(function({"aria-current":a="page",caseSensitive:s=!1,className:r="",end:o=!1,style:c,to:d,viewTransition:h,children:p,...m},y){let v=$r(d,{relative:m.relative}),S=hi(),E=z.useContext(Ko),{navigator:x,basename:T}=z.useContext(wn),w=E!=null&&NE(v)&&h===!0,R=x.encodeLocation?x.encodeLocation(v).pathname:v.pathname,V=S.pathname,U=E&&E.navigation&&E.navigation.location?E.navigation.location.pathname:null;s||(V=V.toLowerCase(),U=U?U.toLowerCase():null,R=R.toLowerCase()),U&&T&&(U=Xn(U,T)||U);const K=R!=="/"&&R.endsWith("/")?R.length-1:R.length;let k=V===R||!o&&V.startsWith(R)&&V.charAt(K)==="/",$=U!=null&&(U===R||!o&&U.startsWith(R)&&U.charAt(R.length)==="/"),at={isActive:k,isPending:$,isTransitioning:w},Y=k?a:void 0,J;typeof r=="function"?J=r(at):J=[r,k?"active":null,$?"pending":null,w?"transitioning":null].filter(Boolean).join(" ");let mt=typeof c=="function"?c(at):c;return z.createElement(k0,{...m,"aria-current":Y,className:J,ref:y,style:mt,to:d,viewTransition:h},typeof p=="function"?p(at):p)});EE.displayName="NavLink";var wE=z.forwardRef(({discover:n="render",fetcherKey:a,navigate:s,reloadDocument:r,replace:o,state:c,method:d=Mo,action:h,onSubmit:p,relative:m,preventScrollReset:y,viewTransition:v,...S},E)=>{let x=DE(),T=UE(h,{relative:m}),w=d.toLowerCase()==="get"?"get":"post",R=typeof h=="string"&&j0.test(h),V=U=>{if(p&&p(U),U.defaultPrevented)return;U.preventDefault();let K=U.nativeEvent.submitter,k=(K==null?void 0:K.getAttribute("formmethod"))||d;x(K||U.currentTarget,{fetcherKey:a,method:k,navigate:s,replace:o,state:c,relative:m,preventScrollReset:y,viewTransition:v})};return z.createElement("form",{ref:E,method:w,action:T,onSubmit:r?p:V,...S,"data-discover":!R&&n==="render"?"true":void 0})});wE.displayName="Form";function RE(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function H0(n){let a=z.useContext(ys);return ae(a,RE(n)),a}function ME(n,{target:a,replace:s,state:r,preventScrollReset:o,relative:c,viewTransition:d}={}){let h=jA(),p=hi(),m=$r(n,{relative:c});return z.useCallback(y=>{if(iE(y,a)){y.preventDefault();let v=s!==void 0?s:Br(p)===Br(m);h(n,{replace:v,state:r,preventScrollReset:o,relative:c,viewTransition:d})}},[p,h,m,s,r,a,n,o,c,d])}var CE=0,OE=()=>`__${String(++CE)}__`;function DE(){let{router:n}=H0("useSubmit"),{basename:a}=z.useContext(wn),s=ZA();return z.useCallback(async(r,o={})=>{let{action:c,method:d,encType:h,formData:p,body:m}=lE(r,a);if(o.navigate===!1){let y=o.fetcherKey||OE();await n.fetch(y,s,o.action||c,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||d,formEncType:o.encType||h,flushSync:o.flushSync})}else await n.navigate(o.action||c,{preventScrollReset:o.preventScrollReset,formData:p,body:m,formMethod:o.method||d,formEncType:o.encType||h,replace:o.replace,state:o.state,fromRouteId:s,flushSync:o.flushSync,viewTransition:o.viewTransition})},[n,a,s])}function UE(n,{relative:a}={}){let{basename:s}=z.useContext(wn),r=z.useContext(Fn);ae(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),c={...$r(n||".",{relative:a})},d=hi();if(n==null){c.search=d.search;let h=new URLSearchParams(c.search),p=h.getAll("index");if(p.some(y=>y==="")){h.delete("index"),p.filter(v=>v).forEach(v=>h.append("index",v));let y=h.toString();c.search=y?`?${y}`:""}}return(!n||n===".")&&o.route.index&&(c.search=c.search?c.search.replace(/^\?/,"?index&"):"?index"),s!=="/"&&(c.pathname=c.pathname==="/"?s:Yn([s,c.pathname])),Br(c)}function NE(n,a={}){let s=z.useContext(U0);ae(s!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=H0("useViewTransitionState"),o=$r(n,{relative:a.relative});if(!s.isTransitioning)return!1;let c=Xn(s.currentLocation.pathname,r)||s.currentLocation.pathname,d=Xn(s.nextLocation.pathname,r)||s.nextLocation.pathname;return Bo(o.pathname,d)!=null||Bo(o.pathname,c)!=null}[...pE];const _g=n=>{let a;const s=new Set,r=(m,y)=>{const v=typeof m=="function"?m(a):m;if(!Object.is(v,a)){const S=a;a=y??(typeof v!="object"||v===null)?v:Object.assign({},a,v),s.forEach(E=>E(a,S))}},o=()=>a,h={setState:r,getState:o,getInitialState:()=>p,subscribe:m=>(s.add(m),()=>s.delete(m))},p=a=n(r,o,h);return h},zE=n=>n?_g(n):_g,_E=n=>n;function LE(n,a=_E){const s=pg.useSyncExternalStore(n.subscribe,()=>a(n.getState()),()=>a(n.getInitialState()));return pg.useDebugValue(s),s}const VE=n=>{const a=zE(n),s=r=>LE(a,r);return Object.assign(s,a),s},q0=n=>VE;function Ld(n,a){let s;try{s=n()}catch{return}return{getItem:o=>{var c;const d=p=>p===null?null:JSON.parse(p,void 0),h=(c=s.getItem(o))!=null?c:null;return h instanceof Promise?h.then(d):d(h)},setItem:(o,c)=>s.setItem(o,JSON.stringify(c,void 0)),removeItem:o=>s.removeItem(o)}}const Jf=n=>a=>{try{const s=n(a);return s instanceof Promise?s:{then(r){return Jf(r)(s)},catch(r){return this}}}catch(s){return{then(r){return this},catch(r){return Jf(r)(s)}}}},BE=(n,a)=>(s,r,o)=>{let c={storage:Ld(()=>localStorage),partialize:T=>T,version:0,merge:(T,w)=>({...w,...T}),...a},d=!1;const h=new Set,p=new Set;let m=c.storage;if(!m)return n((...T)=>{console.warn(`[zustand persist middleware] Unable to update item '${c.name}', the given storage is currently unavailable.`),s(...T)},r,o);const y=()=>{const T=c.partialize({...r()});return m.setItem(c.name,{state:T,version:c.version})},v=o.setState;o.setState=(T,w)=>{v(T,w),y()};const S=n((...T)=>{s(...T),y()},r,o);o.getInitialState=()=>S;let E;const x=()=>{var T,w;if(!m)return;d=!1,h.forEach(V=>{var U;return V((U=r())!=null?U:S)});const R=((w=c.onRehydrateStorage)==null?void 0:w.call(c,(T=r())!=null?T:S))||void 0;return Jf(m.getItem.bind(m))(c.name).then(V=>{if(V)if(typeof V.version=="number"&&V.version!==c.version){if(c.migrate){const U=c.migrate(V.state,V.version);return U instanceof Promise?U.then(K=>[!0,K]):[!0,U]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,V.state];return[!1,void 0]}).then(V=>{var U;const[K,k]=V;if(E=c.merge(k,(U=r())!=null?U:S),s(E,!0),K)return y()}).then(()=>{R==null||R(E,void 0),E=r(),d=!0,p.forEach(V=>V(E))}).catch(V=>{R==null||R(void 0,V)})};return o.persist={setOptions:T=>{c={...c,...T},T.storage&&(m=T.storage)},clearStorage:()=>{m==null||m.removeItem(c.name)},getOptions:()=>c,rehydrate:()=>x(),hasHydrated:()=>d,onHydrate:T=>(h.add(T),()=>{h.delete(T)}),onFinishHydration:T=>(p.add(T),()=>{p.delete(T)})},c.skipHydration||x(),E||S},P0=BE;function G0(n,a){return function(){return n.apply(a,arguments)}}const{toString:jE}=Object.prototype,{getPrototypeOf:Vd}=Object,{iterator:$o,toStringTag:Y0}=Symbol,Jo=(n=>a=>{const s=jE.call(a);return n[s]||(n[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),cn=n=>(n=n.toLowerCase(),a=>Jo(a)===n),Wo=n=>a=>typeof a===n,{isArray:gs}=Array,jr=Wo("undefined");function kE(n){return n!==null&&!jr(n)&&n.constructor!==null&&!jr(n.constructor)&&De(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const X0=cn("ArrayBuffer");function HE(n){let a;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?a=ArrayBuffer.isView(n):a=n&&n.buffer&&X0(n.buffer),a}const qE=Wo("string"),De=Wo("function"),Q0=Wo("number"),Io=n=>n!==null&&typeof n=="object",PE=n=>n===!0||n===!1,Oo=n=>{if(Jo(n)!=="object")return!1;const a=Vd(n);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(Y0 in n)&&!($o in n)},GE=cn("Date"),YE=cn("File"),XE=cn("Blob"),QE=cn("FileList"),KE=n=>Io(n)&&De(n.pipe),FE=n=>{let a;return n&&(typeof FormData=="function"&&n instanceof FormData||De(n.append)&&((a=Jo(n))==="formdata"||a==="object"&&De(n.toString)&&n.toString()==="[object FormData]"))},ZE=cn("URLSearchParams"),[$E,JE,WE,IE]=["ReadableStream","Request","Response","Headers"].map(cn),tw=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Jr(n,a,{allOwnKeys:s=!1}={}){if(n===null||typeof n>"u")return;let r,o;if(typeof n!="object"&&(n=[n]),gs(n))for(r=0,o=n.length;r<o;r++)a.call(null,n[r],r,n);else{const c=s?Object.getOwnPropertyNames(n):Object.keys(n),d=c.length;let h;for(r=0;r<d;r++)h=c[r],a.call(null,n[h],h,n)}}function K0(n,a){a=a.toLowerCase();const s=Object.keys(n);let r=s.length,o;for(;r-- >0;)if(o=s[r],a===o.toLowerCase())return o;return null}const ti=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,F0=n=>!jr(n)&&n!==ti;function Wf(){const{caseless:n}=F0(this)&&this||{},a={},s=(r,o)=>{const c=n&&K0(a,o)||o;Oo(a[c])&&Oo(r)?a[c]=Wf(a[c],r):Oo(r)?a[c]=Wf({},r):gs(r)?a[c]=r.slice():a[c]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&Jr(arguments[r],s);return a}const ew=(n,a,s,{allOwnKeys:r}={})=>(Jr(a,(o,c)=>{s&&De(o)?n[c]=G0(o,s):n[c]=o},{allOwnKeys:r}),n),nw=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),aw=(n,a,s,r)=>{n.prototype=Object.create(a.prototype,r),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:a.prototype}),s&&Object.assign(n.prototype,s)},iw=(n,a,s,r)=>{let o,c,d;const h={};if(a=a||{},n==null)return a;do{for(o=Object.getOwnPropertyNames(n),c=o.length;c-- >0;)d=o[c],(!r||r(d,n,a))&&!h[d]&&(a[d]=n[d],h[d]=!0);n=s!==!1&&Vd(n)}while(n&&(!s||s(n,a))&&n!==Object.prototype);return a},sw=(n,a,s)=>{n=String(n),(s===void 0||s>n.length)&&(s=n.length),s-=a.length;const r=n.indexOf(a,s);return r!==-1&&r===s},rw=n=>{if(!n)return null;if(gs(n))return n;let a=n.length;if(!Q0(a))return null;const s=new Array(a);for(;a-- >0;)s[a]=n[a];return s},lw=(n=>a=>n&&a instanceof n)(typeof Uint8Array<"u"&&Vd(Uint8Array)),ow=(n,a)=>{const r=(n&&n[$o]).call(n);let o;for(;(o=r.next())&&!o.done;){const c=o.value;a.call(n,c[0],c[1])}},uw=(n,a)=>{let s;const r=[];for(;(s=n.exec(a))!==null;)r.push(s);return r},cw=cn("HTMLFormElement"),fw=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,r,o){return r.toUpperCase()+o}),Lg=(({hasOwnProperty:n})=>(a,s)=>n.call(a,s))(Object.prototype),dw=cn("RegExp"),Z0=(n,a)=>{const s=Object.getOwnPropertyDescriptors(n),r={};Jr(s,(o,c)=>{let d;(d=a(o,c,n))!==!1&&(r[c]=d||o)}),Object.defineProperties(n,r)},hw=n=>{Z0(n,(a,s)=>{if(De(n)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const r=n[s];if(De(r)){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},mw=(n,a)=>{const s={},r=o=>{o.forEach(c=>{s[c]=!0})};return gs(n)?r(n):r(String(n).split(a)),s},pw=()=>{},yw=(n,a)=>n!=null&&Number.isFinite(n=+n)?n:a;function gw(n){return!!(n&&De(n.append)&&n[Y0]==="FormData"&&n[$o])}const vw=n=>{const a=new Array(10),s=(r,o)=>{if(Io(r)){if(a.indexOf(r)>=0)return;if(!("toJSON"in r)){a[o]=r;const c=gs(r)?[]:{};return Jr(r,(d,h)=>{const p=s(d,o+1);!jr(p)&&(c[h]=p)}),a[o]=void 0,c}}return r};return s(n,0)},bw=cn("AsyncFunction"),Sw=n=>n&&(Io(n)||De(n))&&De(n.then)&&De(n.catch),$0=((n,a)=>n?setImmediate:a?((s,r)=>(ti.addEventListener("message",({source:o,data:c})=>{o===ti&&c===s&&r.length&&r.shift()()},!1),o=>{r.push(o),ti.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",De(ti.postMessage)),xw=typeof queueMicrotask<"u"?queueMicrotask.bind(ti):typeof process<"u"&&process.nextTick||$0,Tw=n=>n!=null&&De(n[$o]),B={isArray:gs,isArrayBuffer:X0,isBuffer:kE,isFormData:FE,isArrayBufferView:HE,isString:qE,isNumber:Q0,isBoolean:PE,isObject:Io,isPlainObject:Oo,isReadableStream:$E,isRequest:JE,isResponse:WE,isHeaders:IE,isUndefined:jr,isDate:GE,isFile:YE,isBlob:XE,isRegExp:dw,isFunction:De,isStream:KE,isURLSearchParams:ZE,isTypedArray:lw,isFileList:QE,forEach:Jr,merge:Wf,extend:ew,trim:tw,stripBOM:nw,inherits:aw,toFlatObject:iw,kindOf:Jo,kindOfTest:cn,endsWith:sw,toArray:rw,forEachEntry:ow,matchAll:uw,isHTMLForm:cw,hasOwnProperty:Lg,hasOwnProp:Lg,reduceDescriptors:Z0,freezeMethods:hw,toObjectSet:mw,toCamelCase:fw,noop:pw,toFiniteNumber:yw,findKey:K0,global:ti,isContextDefined:F0,isSpecCompliantForm:gw,toJSONObject:vw,isAsyncFn:bw,isThenable:Sw,setImmediate:$0,asap:xw,isIterable:Tw};function dt(n,a,s,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",a&&(this.code=a),s&&(this.config=s),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}B.inherits(dt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:B.toJSONObject(this.config),code:this.code,status:this.status}}});const J0=dt.prototype,W0={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{W0[n]={value:n}});Object.defineProperties(dt,W0);Object.defineProperty(J0,"isAxiosError",{value:!0});dt.from=(n,a,s,r,o,c)=>{const d=Object.create(J0);return B.toFlatObject(n,d,function(p){return p!==Error.prototype},h=>h!=="isAxiosError"),dt.call(d,n.message,a,s,r,o),d.cause=n,d.name=n.name,c&&Object.assign(d,c),d};const Aw=null;function If(n){return B.isPlainObject(n)||B.isArray(n)}function I0(n){return B.endsWith(n,"[]")?n.slice(0,-2):n}function Vg(n,a,s){return n?n.concat(a).map(function(o,c){return o=I0(o),!s&&c?"["+o+"]":o}).join(s?".":""):a}function Ew(n){return B.isArray(n)&&!n.some(If)}const ww=B.toFlatObject(B,{},null,function(a){return/^is[A-Z]/.test(a)});function tu(n,a,s){if(!B.isObject(n))throw new TypeError("target must be an object");a=a||new FormData,s=B.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(T,w){return!B.isUndefined(w[T])});const r=s.metaTokens,o=s.visitor||y,c=s.dots,d=s.indexes,p=(s.Blob||typeof Blob<"u"&&Blob)&&B.isSpecCompliantForm(a);if(!B.isFunction(o))throw new TypeError("visitor must be a function");function m(x){if(x===null)return"";if(B.isDate(x))return x.toISOString();if(B.isBoolean(x))return x.toString();if(!p&&B.isBlob(x))throw new dt("Blob is not supported. Use a Buffer instead.");return B.isArrayBuffer(x)||B.isTypedArray(x)?p&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function y(x,T,w){let R=x;if(x&&!w&&typeof x=="object"){if(B.endsWith(T,"{}"))T=r?T:T.slice(0,-2),x=JSON.stringify(x);else if(B.isArray(x)&&Ew(x)||(B.isFileList(x)||B.endsWith(T,"[]"))&&(R=B.toArray(x)))return T=I0(T),R.forEach(function(U,K){!(B.isUndefined(U)||U===null)&&a.append(d===!0?Vg([T],K,c):d===null?T:T+"[]",m(U))}),!1}return If(x)?!0:(a.append(Vg(w,T,c),m(x)),!1)}const v=[],S=Object.assign(ww,{defaultVisitor:y,convertValue:m,isVisitable:If});function E(x,T){if(!B.isUndefined(x)){if(v.indexOf(x)!==-1)throw Error("Circular reference detected in "+T.join("."));v.push(x),B.forEach(x,function(R,V){(!(B.isUndefined(R)||R===null)&&o.call(a,R,B.isString(V)?V.trim():V,T,S))===!0&&E(R,T?T.concat(V):[V])}),v.pop()}}if(!B.isObject(n))throw new TypeError("data must be an object");return E(n),a}function Bg(n){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(r){return a[r]})}function Bd(n,a){this._pairs=[],n&&tu(n,this,a)}const tb=Bd.prototype;tb.append=function(a,s){this._pairs.push([a,s])};tb.toString=function(a){const s=a?function(r){return a.call(this,r,Bg)}:Bg;return this._pairs.map(function(o){return s(o[0])+"="+s(o[1])},"").join("&")};function Rw(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eb(n,a,s){if(!a)return n;const r=s&&s.encode||Rw;B.isFunction(s)&&(s={serialize:s});const o=s&&s.serialize;let c;if(o?c=o(a,s):c=B.isURLSearchParams(a)?a.toString():new Bd(a,s).toString(r),c){const d=n.indexOf("#");d!==-1&&(n=n.slice(0,d)),n+=(n.indexOf("?")===-1?"?":"&")+c}return n}class jg{constructor(){this.handlers=[]}use(a,s,r){return this.handlers.push({fulfilled:a,rejected:s,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){B.forEach(this.handlers,function(r){r!==null&&a(r)})}}const nb={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Mw=typeof URLSearchParams<"u"?URLSearchParams:Bd,Cw=typeof FormData<"u"?FormData:null,Ow=typeof Blob<"u"?Blob:null,Dw={isBrowser:!0,classes:{URLSearchParams:Mw,FormData:Cw,Blob:Ow},protocols:["http","https","file","blob","url","data"]},jd=typeof window<"u"&&typeof document<"u",td=typeof navigator=="object"&&navigator||void 0,Uw=jd&&(!td||["ReactNative","NativeScript","NS"].indexOf(td.product)<0),Nw=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",zw=jd&&window.location.href||"http://localhost",_w=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:jd,hasStandardBrowserEnv:Uw,hasStandardBrowserWebWorkerEnv:Nw,navigator:td,origin:zw},Symbol.toStringTag,{value:"Module"})),ge={..._w,...Dw};function Lw(n,a){return tu(n,new ge.classes.URLSearchParams,Object.assign({visitor:function(s,r,o,c){return ge.isNode&&B.isBuffer(s)?(this.append(r,s.toString("base64")),!1):c.defaultVisitor.apply(this,arguments)}},a))}function Vw(n){return B.matchAll(/\w+|\[(\w*)]/g,n).map(a=>a[0]==="[]"?"":a[1]||a[0])}function Bw(n){const a={},s=Object.keys(n);let r;const o=s.length;let c;for(r=0;r<o;r++)c=s[r],a[c]=n[c];return a}function ab(n){function a(s,r,o,c){let d=s[c++];if(d==="__proto__")return!0;const h=Number.isFinite(+d),p=c>=s.length;return d=!d&&B.isArray(o)?o.length:d,p?(B.hasOwnProp(o,d)?o[d]=[o[d],r]:o[d]=r,!h):((!o[d]||!B.isObject(o[d]))&&(o[d]=[]),a(s,r,o[d],c)&&B.isArray(o[d])&&(o[d]=Bw(o[d])),!h)}if(B.isFormData(n)&&B.isFunction(n.entries)){const s={};return B.forEachEntry(n,(r,o)=>{a(Vw(r),o,s,0)}),s}return null}function jw(n,a,s){if(B.isString(n))try{return(a||JSON.parse)(n),B.trim(n)}catch(r){if(r.name!=="SyntaxError")throw r}return(s||JSON.stringify)(n)}const Wr={transitional:nb,adapter:["xhr","http","fetch"],transformRequest:[function(a,s){const r=s.getContentType()||"",o=r.indexOf("application/json")>-1,c=B.isObject(a);if(c&&B.isHTMLForm(a)&&(a=new FormData(a)),B.isFormData(a))return o?JSON.stringify(ab(a)):a;if(B.isArrayBuffer(a)||B.isBuffer(a)||B.isStream(a)||B.isFile(a)||B.isBlob(a)||B.isReadableStream(a))return a;if(B.isArrayBufferView(a))return a.buffer;if(B.isURLSearchParams(a))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();let h;if(c){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Lw(a,this.formSerializer).toString();if((h=B.isFileList(a))||r.indexOf("multipart/form-data")>-1){const p=this.env&&this.env.FormData;return tu(h?{"files[]":a}:a,p&&new p,this.formSerializer)}}return c||o?(s.setContentType("application/json",!1),jw(a)):a}],transformResponse:[function(a){const s=this.transitional||Wr.transitional,r=s&&s.forcedJSONParsing,o=this.responseType==="json";if(B.isResponse(a)||B.isReadableStream(a))return a;if(a&&B.isString(a)&&(r&&!this.responseType||o)){const d=!(s&&s.silentJSONParsing)&&o;try{return JSON.parse(a)}catch(h){if(d)throw h.name==="SyntaxError"?dt.from(h,dt.ERR_BAD_RESPONSE,this,null,this.response):h}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ge.classes.FormData,Blob:ge.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};B.forEach(["delete","get","head","post","put","patch"],n=>{Wr.headers[n]={}});const kw=B.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Hw=n=>{const a={};let s,r,o;return n&&n.split(`
`).forEach(function(d){o=d.indexOf(":"),s=d.substring(0,o).trim().toLowerCase(),r=d.substring(o+1).trim(),!(!s||a[s]&&kw[s])&&(s==="set-cookie"?a[s]?a[s].push(r):a[s]=[r]:a[s]=a[s]?a[s]+", "+r:r)}),a},kg=Symbol("internals");function wr(n){return n&&String(n).trim().toLowerCase()}function Do(n){return n===!1||n==null?n:B.isArray(n)?n.map(Do):String(n)}function qw(n){const a=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=s.exec(n);)a[r[1]]=r[2];return a}const Pw=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function Df(n,a,s,r,o){if(B.isFunction(r))return r.call(this,a,s);if(o&&(a=s),!!B.isString(a)){if(B.isString(r))return a.indexOf(r)!==-1;if(B.isRegExp(r))return r.test(a)}}function Gw(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,s,r)=>s.toUpperCase()+r)}function Yw(n,a){const s=B.toCamelCase(" "+a);["get","set","has"].forEach(r=>{Object.defineProperty(n,r+s,{value:function(o,c,d){return this[r].call(this,a,o,c,d)},configurable:!0})})}let Ue=class{constructor(a){a&&this.set(a)}set(a,s,r){const o=this;function c(h,p,m){const y=wr(p);if(!y)throw new Error("header name must be a non-empty string");const v=B.findKey(o,y);(!v||o[v]===void 0||m===!0||m===void 0&&o[v]!==!1)&&(o[v||p]=Do(h))}const d=(h,p)=>B.forEach(h,(m,y)=>c(m,y,p));if(B.isPlainObject(a)||a instanceof this.constructor)d(a,s);else if(B.isString(a)&&(a=a.trim())&&!Pw(a))d(Hw(a),s);else if(B.isObject(a)&&B.isIterable(a)){let h={},p,m;for(const y of a){if(!B.isArray(y))throw TypeError("Object iterator must return a key-value pair");h[m=y[0]]=(p=h[m])?B.isArray(p)?[...p,y[1]]:[p,y[1]]:y[1]}d(h,s)}else a!=null&&c(s,a,r);return this}get(a,s){if(a=wr(a),a){const r=B.findKey(this,a);if(r){const o=this[r];if(!s)return o;if(s===!0)return qw(o);if(B.isFunction(s))return s.call(this,o,r);if(B.isRegExp(s))return s.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(a,s){if(a=wr(a),a){const r=B.findKey(this,a);return!!(r&&this[r]!==void 0&&(!s||Df(this,this[r],r,s)))}return!1}delete(a,s){const r=this;let o=!1;function c(d){if(d=wr(d),d){const h=B.findKey(r,d);h&&(!s||Df(r,r[h],h,s))&&(delete r[h],o=!0)}}return B.isArray(a)?a.forEach(c):c(a),o}clear(a){const s=Object.keys(this);let r=s.length,o=!1;for(;r--;){const c=s[r];(!a||Df(this,this[c],c,a,!0))&&(delete this[c],o=!0)}return o}normalize(a){const s=this,r={};return B.forEach(this,(o,c)=>{const d=B.findKey(r,c);if(d){s[d]=Do(o),delete s[c];return}const h=a?Gw(c):String(c).trim();h!==c&&delete s[c],s[h]=Do(o),r[h]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){const s=Object.create(null);return B.forEach(this,(r,o)=>{r!=null&&r!==!1&&(s[o]=a&&B.isArray(r)?r.join(", "):r)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,s])=>a+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...s){const r=new this(a);return s.forEach(o=>r.set(o)),r}static accessor(a){const r=(this[kg]=this[kg]={accessors:{}}).accessors,o=this.prototype;function c(d){const h=wr(d);r[h]||(Yw(o,d),r[h]=!0)}return B.isArray(a)?a.forEach(c):c(a),this}};Ue.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);B.reduceDescriptors(Ue.prototype,({value:n},a)=>{let s=a[0].toUpperCase()+a.slice(1);return{get:()=>n,set(r){this[s]=r}}});B.freezeMethods(Ue);function Uf(n,a){const s=this||Wr,r=a||s,o=Ue.from(r.headers);let c=r.data;return B.forEach(n,function(h){c=h.call(s,c,o.normalize(),a?a.status:void 0)}),o.normalize(),c}function ib(n){return!!(n&&n.__CANCEL__)}function vs(n,a,s){dt.call(this,n??"canceled",dt.ERR_CANCELED,a,s),this.name="CanceledError"}B.inherits(vs,dt,{__CANCEL__:!0});function sb(n,a,s){const r=s.config.validateStatus;!s.status||!r||r(s.status)?n(s):a(new dt("Request failed with status code "+s.status,[dt.ERR_BAD_REQUEST,dt.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Xw(n){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return a&&a[1]||""}function Qw(n,a){n=n||10;const s=new Array(n),r=new Array(n);let o=0,c=0,d;return a=a!==void 0?a:1e3,function(p){const m=Date.now(),y=r[c];d||(d=m),s[o]=p,r[o]=m;let v=c,S=0;for(;v!==o;)S+=s[v++],v=v%n;if(o=(o+1)%n,o===c&&(c=(c+1)%n),m-d<a)return;const E=y&&m-y;return E?Math.round(S*1e3/E):void 0}}function Kw(n,a){let s=0,r=1e3/a,o,c;const d=(m,y=Date.now())=>{s=y,o=null,c&&(clearTimeout(c),c=null),n.apply(null,m)};return[(...m)=>{const y=Date.now(),v=y-s;v>=r?d(m,y):(o=m,c||(c=setTimeout(()=>{c=null,d(o)},r-v)))},()=>o&&d(o)]}const jo=(n,a,s=3)=>{let r=0;const o=Qw(50,250);return Kw(c=>{const d=c.loaded,h=c.lengthComputable?c.total:void 0,p=d-r,m=o(p),y=d<=h;r=d;const v={loaded:d,total:h,progress:h?d/h:void 0,bytes:p,rate:m||void 0,estimated:m&&h&&y?(h-d)/m:void 0,event:c,lengthComputable:h!=null,[a?"download":"upload"]:!0};n(v)},s)},Hg=(n,a)=>{const s=n!=null;return[r=>a[0]({lengthComputable:s,total:n,loaded:r}),a[1]]},qg=n=>(...a)=>B.asap(()=>n(...a)),Fw=ge.hasStandardBrowserEnv?((n,a)=>s=>(s=new URL(s,ge.origin),n.protocol===s.protocol&&n.host===s.host&&(a||n.port===s.port)))(new URL(ge.origin),ge.navigator&&/(msie|trident)/i.test(ge.navigator.userAgent)):()=>!0,Zw=ge.hasStandardBrowserEnv?{write(n,a,s,r,o,c){const d=[n+"="+encodeURIComponent(a)];B.isNumber(s)&&d.push("expires="+new Date(s).toGMTString()),B.isString(r)&&d.push("path="+r),B.isString(o)&&d.push("domain="+o),c===!0&&d.push("secure"),document.cookie=d.join("; ")},read(n){const a=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function $w(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function Jw(n,a){return a?n.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):n}function rb(n,a,s){let r=!$w(a);return n&&(r||s==!1)?Jw(n,a):a}const Pg=n=>n instanceof Ue?{...n}:n;function di(n,a){a=a||{};const s={};function r(m,y,v,S){return B.isPlainObject(m)&&B.isPlainObject(y)?B.merge.call({caseless:S},m,y):B.isPlainObject(y)?B.merge({},y):B.isArray(y)?y.slice():y}function o(m,y,v,S){if(B.isUndefined(y)){if(!B.isUndefined(m))return r(void 0,m,v,S)}else return r(m,y,v,S)}function c(m,y){if(!B.isUndefined(y))return r(void 0,y)}function d(m,y){if(B.isUndefined(y)){if(!B.isUndefined(m))return r(void 0,m)}else return r(void 0,y)}function h(m,y,v){if(v in a)return r(m,y);if(v in n)return r(void 0,m)}const p={url:c,method:c,data:c,baseURL:d,transformRequest:d,transformResponse:d,paramsSerializer:d,timeout:d,timeoutMessage:d,withCredentials:d,withXSRFToken:d,adapter:d,responseType:d,xsrfCookieName:d,xsrfHeaderName:d,onUploadProgress:d,onDownloadProgress:d,decompress:d,maxContentLength:d,maxBodyLength:d,beforeRedirect:d,transport:d,httpAgent:d,httpsAgent:d,cancelToken:d,socketPath:d,responseEncoding:d,validateStatus:h,headers:(m,y,v)=>o(Pg(m),Pg(y),v,!0)};return B.forEach(Object.keys(Object.assign({},n,a)),function(y){const v=p[y]||o,S=v(n[y],a[y],y);B.isUndefined(S)&&v!==h||(s[y]=S)}),s}const lb=n=>{const a=di({},n);let{data:s,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:c,headers:d,auth:h}=a;a.headers=d=Ue.from(d),a.url=eb(rb(a.baseURL,a.url,a.allowAbsoluteUrls),n.params,n.paramsSerializer),h&&d.set("Authorization","Basic "+btoa((h.username||"")+":"+(h.password?unescape(encodeURIComponent(h.password)):"")));let p;if(B.isFormData(s)){if(ge.hasStandardBrowserEnv||ge.hasStandardBrowserWebWorkerEnv)d.setContentType(void 0);else if((p=d.getContentType())!==!1){const[m,...y]=p?p.split(";").map(v=>v.trim()).filter(Boolean):[];d.setContentType([m||"multipart/form-data",...y].join("; "))}}if(ge.hasStandardBrowserEnv&&(r&&B.isFunction(r)&&(r=r(a)),r||r!==!1&&Fw(a.url))){const m=o&&c&&Zw.read(c);m&&d.set(o,m)}return a},Ww=typeof XMLHttpRequest<"u",Iw=Ww&&function(n){return new Promise(function(s,r){const o=lb(n);let c=o.data;const d=Ue.from(o.headers).normalize();let{responseType:h,onUploadProgress:p,onDownloadProgress:m}=o,y,v,S,E,x;function T(){E&&E(),x&&x(),o.cancelToken&&o.cancelToken.unsubscribe(y),o.signal&&o.signal.removeEventListener("abort",y)}let w=new XMLHttpRequest;w.open(o.method.toUpperCase(),o.url,!0),w.timeout=o.timeout;function R(){if(!w)return;const U=Ue.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),k={data:!h||h==="text"||h==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:U,config:n,request:w};sb(function(at){s(at),T()},function(at){r(at),T()},k),w=null}"onloadend"in w?w.onloadend=R:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(R)},w.onabort=function(){w&&(r(new dt("Request aborted",dt.ECONNABORTED,n,w)),w=null)},w.onerror=function(){r(new dt("Network Error",dt.ERR_NETWORK,n,w)),w=null},w.ontimeout=function(){let K=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const k=o.transitional||nb;o.timeoutErrorMessage&&(K=o.timeoutErrorMessage),r(new dt(K,k.clarifyTimeoutError?dt.ETIMEDOUT:dt.ECONNABORTED,n,w)),w=null},c===void 0&&d.setContentType(null),"setRequestHeader"in w&&B.forEach(d.toJSON(),function(K,k){w.setRequestHeader(k,K)}),B.isUndefined(o.withCredentials)||(w.withCredentials=!!o.withCredentials),h&&h!=="json"&&(w.responseType=o.responseType),m&&([S,x]=jo(m,!0),w.addEventListener("progress",S)),p&&w.upload&&([v,E]=jo(p),w.upload.addEventListener("progress",v),w.upload.addEventListener("loadend",E)),(o.cancelToken||o.signal)&&(y=U=>{w&&(r(!U||U.type?new vs(null,n,w):U),w.abort(),w=null)},o.cancelToken&&o.cancelToken.subscribe(y),o.signal&&(o.signal.aborted?y():o.signal.addEventListener("abort",y)));const V=Xw(o.url);if(V&&ge.protocols.indexOf(V)===-1){r(new dt("Unsupported protocol "+V+":",dt.ERR_BAD_REQUEST,n));return}w.send(c||null)})},tR=(n,a)=>{const{length:s}=n=n?n.filter(Boolean):[];if(a||s){let r=new AbortController,o;const c=function(m){if(!o){o=!0,h();const y=m instanceof Error?m:this.reason;r.abort(y instanceof dt?y:new vs(y instanceof Error?y.message:y))}};let d=a&&setTimeout(()=>{d=null,c(new dt(`timeout ${a} of ms exceeded`,dt.ETIMEDOUT))},a);const h=()=>{n&&(d&&clearTimeout(d),d=null,n.forEach(m=>{m.unsubscribe?m.unsubscribe(c):m.removeEventListener("abort",c)}),n=null)};n.forEach(m=>m.addEventListener("abort",c));const{signal:p}=r;return p.unsubscribe=()=>B.asap(h),p}},eR=function*(n,a){let s=n.byteLength;if(s<a){yield n;return}let r=0,o;for(;r<s;)o=r+a,yield n.slice(r,o),r=o},nR=async function*(n,a){for await(const s of aR(n))yield*eR(s,a)},aR=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const a=n.getReader();try{for(;;){const{done:s,value:r}=await a.read();if(s)break;yield r}}finally{await a.cancel()}},Gg=(n,a,s,r)=>{const o=nR(n,a);let c=0,d,h=p=>{d||(d=!0,r&&r(p))};return new ReadableStream({async pull(p){try{const{done:m,value:y}=await o.next();if(m){h(),p.close();return}let v=y.byteLength;if(s){let S=c+=v;s(S)}p.enqueue(new Uint8Array(y))}catch(m){throw h(m),m}},cancel(p){return h(p),o.return()}},{highWaterMark:2})},eu=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ob=eu&&typeof ReadableStream=="function",iR=eu&&(typeof TextEncoder=="function"?(n=>a=>n.encode(a))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),ub=(n,...a)=>{try{return!!n(...a)}catch{return!1}},sR=ob&&ub(()=>{let n=!1;const a=new Request(ge.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!a}),Yg=64*1024,ed=ob&&ub(()=>B.isReadableStream(new Response("").body)),ko={stream:ed&&(n=>n.body)};eu&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(a=>{!ko[a]&&(ko[a]=B.isFunction(n[a])?s=>s[a]():(s,r)=>{throw new dt(`Response type '${a}' is not supported`,dt.ERR_NOT_SUPPORT,r)})})})(new Response);const rR=async n=>{if(n==null)return 0;if(B.isBlob(n))return n.size;if(B.isSpecCompliantForm(n))return(await new Request(ge.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(B.isArrayBufferView(n)||B.isArrayBuffer(n))return n.byteLength;if(B.isURLSearchParams(n)&&(n=n+""),B.isString(n))return(await iR(n)).byteLength},lR=async(n,a)=>{const s=B.toFiniteNumber(n.getContentLength());return s??rR(a)},oR=eu&&(async n=>{let{url:a,method:s,data:r,signal:o,cancelToken:c,timeout:d,onDownloadProgress:h,onUploadProgress:p,responseType:m,headers:y,withCredentials:v="same-origin",fetchOptions:S}=lb(n);m=m?(m+"").toLowerCase():"text";let E=tR([o,c&&c.toAbortSignal()],d),x;const T=E&&E.unsubscribe&&(()=>{E.unsubscribe()});let w;try{if(p&&sR&&s!=="get"&&s!=="head"&&(w=await lR(y,r))!==0){let k=new Request(a,{method:"POST",body:r,duplex:"half"}),$;if(B.isFormData(r)&&($=k.headers.get("content-type"))&&y.setContentType($),k.body){const[at,Y]=Hg(w,jo(qg(p)));r=Gg(k.body,Yg,at,Y)}}B.isString(v)||(v=v?"include":"omit");const R="credentials"in Request.prototype;x=new Request(a,{...S,signal:E,method:s.toUpperCase(),headers:y.normalize().toJSON(),body:r,duplex:"half",credentials:R?v:void 0});let V=await fetch(x,S);const U=ed&&(m==="stream"||m==="response");if(ed&&(h||U&&T)){const k={};["status","statusText","headers"].forEach(J=>{k[J]=V[J]});const $=B.toFiniteNumber(V.headers.get("content-length")),[at,Y]=h&&Hg($,jo(qg(h),!0))||[];V=new Response(Gg(V.body,Yg,at,()=>{Y&&Y(),T&&T()}),k)}m=m||"text";let K=await ko[B.findKey(ko,m)||"text"](V,n);return!U&&T&&T(),await new Promise((k,$)=>{sb(k,$,{data:K,headers:Ue.from(V.headers),status:V.status,statusText:V.statusText,config:n,request:x})})}catch(R){throw T&&T(),R&&R.name==="TypeError"&&/Load failed|fetch/i.test(R.message)?Object.assign(new dt("Network Error",dt.ERR_NETWORK,n,x),{cause:R.cause||R}):dt.from(R,R&&R.code,n,x)}}),nd={http:Aw,xhr:Iw,fetch:oR};B.forEach(nd,(n,a)=>{if(n){try{Object.defineProperty(n,"name",{value:a})}catch{}Object.defineProperty(n,"adapterName",{value:a})}});const Xg=n=>`- ${n}`,uR=n=>B.isFunction(n)||n===null||n===!1,cb={getAdapter:n=>{n=B.isArray(n)?n:[n];const{length:a}=n;let s,r;const o={};for(let c=0;c<a;c++){s=n[c];let d;if(r=s,!uR(s)&&(r=nd[(d=String(s)).toLowerCase()],r===void 0))throw new dt(`Unknown adapter '${d}'`);if(r)break;o[d||"#"+c]=r}if(!r){const c=Object.entries(o).map(([h,p])=>`adapter ${h} `+(p===!1?"is not supported by the environment":"is not available in the build"));let d=a?c.length>1?`since :
`+c.map(Xg).join(`
`):" "+Xg(c[0]):"as no adapter specified";throw new dt("There is no suitable adapter to dispatch the request "+d,"ERR_NOT_SUPPORT")}return r},adapters:nd};function Nf(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new vs(null,n)}function Qg(n){return Nf(n),n.headers=Ue.from(n.headers),n.data=Uf.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),cb.getAdapter(n.adapter||Wr.adapter)(n).then(function(r){return Nf(n),r.data=Uf.call(n,n.transformResponse,r),r.headers=Ue.from(r.headers),r},function(r){return ib(r)||(Nf(n),r&&r.response&&(r.response.data=Uf.call(n,n.transformResponse,r.response),r.response.headers=Ue.from(r.response.headers))),Promise.reject(r)})}const fb="1.10.0",nu={};["object","boolean","number","function","string","symbol"].forEach((n,a)=>{nu[n]=function(r){return typeof r===n||"a"+(a<1?"n ":" ")+n}});const Kg={};nu.transitional=function(a,s,r){function o(c,d){return"[Axios v"+fb+"] Transitional option '"+c+"'"+d+(r?". "+r:"")}return(c,d,h)=>{if(a===!1)throw new dt(o(d," has been removed"+(s?" in "+s:"")),dt.ERR_DEPRECATED);return s&&!Kg[d]&&(Kg[d]=!0,console.warn(o(d," has been deprecated since v"+s+" and will be removed in the near future"))),a?a(c,d,h):!0}};nu.spelling=function(a){return(s,r)=>(console.warn(`${r} is likely a misspelling of ${a}`),!0)};function cR(n,a,s){if(typeof n!="object")throw new dt("options must be an object",dt.ERR_BAD_OPTION_VALUE);const r=Object.keys(n);let o=r.length;for(;o-- >0;){const c=r[o],d=a[c];if(d){const h=n[c],p=h===void 0||d(h,c,n);if(p!==!0)throw new dt("option "+c+" must be "+p,dt.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new dt("Unknown option "+c,dt.ERR_BAD_OPTION)}}const Uo={assertOptions:cR,validators:nu},gn=Uo.validators;let ui=class{constructor(a){this.defaults=a||{},this.interceptors={request:new jg,response:new jg}}async request(a,s){try{return await this._request(a,s)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const c=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?c&&!String(r.stack).endsWith(c.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+c):r.stack=c}catch{}}throw r}}_request(a,s){typeof a=="string"?(s=s||{},s.url=a):s=a||{},s=di(this.defaults,s);const{transitional:r,paramsSerializer:o,headers:c}=s;r!==void 0&&Uo.assertOptions(r,{silentJSONParsing:gn.transitional(gn.boolean),forcedJSONParsing:gn.transitional(gn.boolean),clarifyTimeoutError:gn.transitional(gn.boolean)},!1),o!=null&&(B.isFunction(o)?s.paramsSerializer={serialize:o}:Uo.assertOptions(o,{encode:gn.function,serialize:gn.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Uo.assertOptions(s,{baseUrl:gn.spelling("baseURL"),withXsrfToken:gn.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let d=c&&B.merge(c.common,c[s.method]);c&&B.forEach(["delete","get","head","post","put","patch","common"],x=>{delete c[x]}),s.headers=Ue.concat(d,c);const h=[];let p=!0;this.interceptors.request.forEach(function(T){typeof T.runWhen=="function"&&T.runWhen(s)===!1||(p=p&&T.synchronous,h.unshift(T.fulfilled,T.rejected))});const m=[];this.interceptors.response.forEach(function(T){m.push(T.fulfilled,T.rejected)});let y,v=0,S;if(!p){const x=[Qg.bind(this),void 0];for(x.unshift.apply(x,h),x.push.apply(x,m),S=x.length,y=Promise.resolve(s);v<S;)y=y.then(x[v++],x[v++]);return y}S=h.length;let E=s;for(v=0;v<S;){const x=h[v++],T=h[v++];try{E=x(E)}catch(w){T.call(this,w);break}}try{y=Qg.call(this,E)}catch(x){return Promise.reject(x)}for(v=0,S=m.length;v<S;)y=y.then(m[v++],m[v++]);return y}getUri(a){a=di(this.defaults,a);const s=rb(a.baseURL,a.url,a.allowAbsoluteUrls);return eb(s,a.params,a.paramsSerializer)}};B.forEach(["delete","get","head","options"],function(a){ui.prototype[a]=function(s,r){return this.request(di(r||{},{method:a,url:s,data:(r||{}).data}))}});B.forEach(["post","put","patch"],function(a){function s(r){return function(c,d,h){return this.request(di(h||{},{method:a,headers:r?{"Content-Type":"multipart/form-data"}:{},url:c,data:d}))}}ui.prototype[a]=s(),ui.prototype[a+"Form"]=s(!0)});let fR=class db{constructor(a){if(typeof a!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(c){s=c});const r=this;this.promise.then(o=>{if(!r._listeners)return;let c=r._listeners.length;for(;c-- >0;)r._listeners[c](o);r._listeners=null}),this.promise.then=o=>{let c;const d=new Promise(h=>{r.subscribe(h),c=h}).then(o);return d.cancel=function(){r.unsubscribe(c)},d},a(function(c,d,h){r.reason||(r.reason=new vs(c,d,h),s(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason){a(this.reason);return}this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;const s=this._listeners.indexOf(a);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const a=new AbortController,s=r=>{a.abort(r)};return this.subscribe(s),a.signal.unsubscribe=()=>this.unsubscribe(s),a.signal}static source(){let a;return{token:new db(function(o){a=o}),cancel:a}}};function dR(n){return function(s){return n.apply(null,s)}}function hR(n){return B.isObject(n)&&n.isAxiosError===!0}const ad={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ad).forEach(([n,a])=>{ad[a]=n});function hb(n){const a=new ui(n),s=G0(ui.prototype.request,a);return B.extend(s,ui.prototype,a,{allOwnKeys:!0}),B.extend(s,a,null,{allOwnKeys:!0}),s.create=function(o){return hb(di(n,o))},s}const $t=hb(Wr);$t.Axios=ui;$t.CanceledError=vs;$t.CancelToken=fR;$t.isCancel=ib;$t.VERSION=fb;$t.toFormData=tu;$t.AxiosError=dt;$t.Cancel=$t.CanceledError;$t.all=function(a){return Promise.all(a)};$t.spread=dR;$t.isAxiosError=hR;$t.mergeConfig=di;$t.AxiosHeaders=Ue;$t.formToJSON=n=>ab(B.isHTMLForm(n)?new FormData(n):n);$t.getAdapter=cb.getAdapter;$t.HttpStatusCode=ad;$t.default=$t;const{Axios:t3,AxiosError:e3,CanceledError:n3,isCancel:a3,CancelToken:i3,VERSION:s3,all:r3,Cancel:l3,isAxiosError:o3,spread:u3,toFormData:c3,AxiosHeaders:f3,HttpStatusCode:d3,formToJSON:h3,getAdapter:m3,mergeConfig:p3}=$t;class mR{constructor(a="http://localhost:8000"){vo(this,"client");vo(this,"baseURL");this.baseURL=a,this.client=$t.create({baseURL:this.baseURL,timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(a=>{const s=this.getAuthToken();return s&&(a.headers.Authorization=`Bearer ${s}`),a.metadata={startTime:new Date},a},a=>(console.error("❌ Request Error:",a),Promise.reject(a))),this.client.interceptors.response.use(a=>{var s,r;return new Date().getTime()-((r=(s=a.config.metadata)==null?void 0:s.startTime)==null?void 0:r.getTime()),a},async a=>{var r,o,c,d;const s=a.config;if(((r=a.response)==null?void 0:r.status)===401&&!s._retry){s._retry=!0;try{const h=this.getRefreshToken();if(h){const p=await this.post("/auth/refresh",{refreshToken:h});if(p.success)return this.setAuthToken(p.data.token),this.setRefreshToken(p.data.refreshToken),s.headers.Authorization=`Bearer ${p.data.token}`,this.client(s)}}catch(h){return this.clearAuthTokens(),window.location.href="/login",Promise.reject(h)}}if(((o=a.response)==null?void 0:o.status)===403&&console.warn("Access denied. Insufficient permissions."),((c=a.response)==null?void 0:c.status)===429){const h=a.response.headers["retry-after"];console.warn(`Rate limit exceeded. Retry after ${h} seconds.`)}return((d=a.response)==null?void 0:d.status)>=500&&console.error("Server error occurred. Please try again later."),Promise.reject(a)})}getAuthToken(){return localStorage.getItem("auth-token")}setAuthToken(a){localStorage.setItem("auth-token",a)}getRefreshToken(){return localStorage.getItem("refresh-token")}setRefreshToken(a){localStorage.setItem("refresh-token",a)}clearAuthTokens(){localStorage.removeItem("auth-token"),localStorage.removeItem("refresh-token")}async get(a,s){try{const r=await this.client.get(a,s);return this.handleResponse(r)}catch(r){return this.handleError(r)}}async post(a,s,r){try{const o=await this.client.post(a,s,r);return this.handleResponse(o)}catch(o){return this.handleError(o)}}async put(a,s,r){try{const o=await this.client.put(a,s,r);return this.handleResponse(o)}catch(o){return this.handleError(o)}}async patch(a,s,r){try{const o=await this.client.patch(a,s,r);return this.handleResponse(o)}catch(o){return this.handleError(o)}}async delete(a,s){try{const r=await this.client.delete(a,s);return this.handleResponse(r)}catch(r){return this.handleError(r)}}async upload(a,s,r){const o=new FormData;o.append("file",s);try{const c=await this.client.post(a,o,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:d=>{if(r&&d.total){const h=Math.round(d.loaded*100/d.total);r(h)}}});return this.handleResponse(c)}catch(c){return this.handleError(c)}}handleResponse(a){return{success:!0,data:a.data.data||a.data,message:a.data.message||"Success",meta:a.data.meta}}handleError(a){var s,r;return a.response?{success:!1,data:null,message:((s=a.response.data)==null?void 0:s.message)||"An error occurred",errors:(r=a.response.data)==null?void 0:r.errors}:a.request?{success:!1,data:null,message:"Network error. Please check your connection."}:{success:!1,data:null,message:a.message||"An unexpected error occurred"}}setBaseURL(a){this.baseURL=a,this.client.defaults.baseURL=a}setDefaultHeader(a,s){this.client.defaults.headers.common[a]=s}removeDefaultHeader(a){delete this.client.defaults.headers.common[a]}async healthCheck(){try{return(await this.get("/health")).success}catch{return!1}}}const _t=new mR;class pR{constructor(){vo(this,"baseUrl","/auth")}async login(a){return _t.post(`${this.baseUrl}/login`,a)}async register(a){return _t.post(`${this.baseUrl}/register`,a)}async logout(){try{const a=await _t.post(`${this.baseUrl}/logout`);return localStorage.removeItem("auth-token"),localStorage.removeItem("refresh-token"),a}catch(a){throw localStorage.removeItem("auth-token"),localStorage.removeItem("refresh-token"),a}}async refreshToken(a){return _t.post(`${this.baseUrl}/refresh`,{refreshToken:a})}async getCurrentUser(){return _t.get(`${this.baseUrl}/me`)}async updateProfile(a){return _t.patch(`${this.baseUrl}/profile`,a)}async changePassword(a){return _t.post(`${this.baseUrl}/change-password`,a)}async requestPasswordReset(a){return _t.post(`${this.baseUrl}/reset-password`,a)}async resetPassword(a,s){return _t.post(`${this.baseUrl}/reset-password/confirm`,{token:a,newPassword:s})}async verifyEmail(a){return _t.post(`${this.baseUrl}/verify-email`,{token:a})}async resendEmailVerification(){return _t.post(`${this.baseUrl}/verify-email/resend`)}async enableTwoFactor(){return _t.post(`${this.baseUrl}/2fa/enable`)}async confirmTwoFactor(a){return _t.post(`${this.baseUrl}/2fa/confirm`,{code:a})}async disableTwoFactor(a){return _t.post(`${this.baseUrl}/2fa/disable`,{password:a})}async verifyTwoFactor(a){return _t.post(`${this.baseUrl}/2fa/verify`,{code:a})}async getSessions(){return _t.get(`${this.baseUrl}/sessions`)}async revokeSession(a){return _t.delete(`${this.baseUrl}/sessions/${a}`)}async revokeAllOtherSessions(){return _t.post(`${this.baseUrl}/sessions/revoke-all`)}async uploadAvatar(a,s){return _t.upload(`${this.baseUrl}/avatar`,a,s)}async deleteAvatar(){return _t.delete(`${this.baseUrl}/avatar`)}async getPreferences(){return _t.get(`${this.baseUrl}/preferences`)}async updatePreferences(a){return _t.patch(`${this.baseUrl}/preferences`,a)}async deleteAccount(a){return _t.post(`${this.baseUrl}/delete-account`,{password:a})}async exportUserData(){return _t.post(`${this.baseUrl}/export-data`)}async checkEmailAvailability(a){return _t.get(`${this.baseUrl}/check-email?email=${encodeURIComponent(a)}`)}async checkUsernameAvailability(a){return _t.get(`${this.baseUrl}/check-username?username=${encodeURIComponent(a)}`)}async getAuthLogs(a=1,s=20){return _t.get(`${this.baseUrl}/logs?page=${a}&limit=${s}`)}}const Rr=new pR,yR=q0()(P0((n,a)=>({user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,login:async s=>{try{n({isLoading:!0,error:null});const r=await Rr.login(s);if(r.success)n({user:r.data.user,token:r.data.token,refreshToken:r.data.refreshToken,isAuthenticated:!0,isLoading:!1,error:null});else throw new Error(r.message)}catch(r){throw n({isLoading:!1,error:r instanceof Error?r.message:"Login failed",isAuthenticated:!1,user:null,token:null,refreshToken:null}),r}},register:async s=>{try{n({isLoading:!0,error:null});const r=await Rr.register(s);if(r.success)n({user:r.data.user,token:r.data.token,refreshToken:r.data.refreshToken,isAuthenticated:!0,isLoading:!1,error:null});else throw new Error(r.message)}catch(r){throw n({isLoading:!1,error:r instanceof Error?r.message:"Registration failed"}),r}},logout:()=>{Rr.logout(),n({user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null})},refreshTokenAction:async()=>{try{const{refreshToken:s}=a();if(!s)throw new Error("No refresh token available");const r=await Rr.refreshToken(s);if(r.success)n({token:r.data.token,refreshToken:r.data.refreshToken,user:r.data.user,isAuthenticated:!0});else throw new Error(r.message)}catch(s){throw a().logout(),s}},updateUser:s=>{const{user:r}=a();r&&n({user:{...r,...s}})},clearError:()=>{n({error:null})},setLoading:s=>{n({isLoading:s})},checkAuth:async()=>{try{const{token:s}=a();if(!s)return;n({isLoading:!0});const r=await Rr.getCurrentUser();r.success?n({user:r.data,isAuthenticated:!0,isLoading:!1}):a().logout()}catch{a().logout()}finally{n({isLoading:!1})}}}),{name:"auth-storage",storage:Ld(()=>localStorage),partialize:n=>({user:n.user,token:n.token,refreshToken:n.refreshToken,isAuthenticated:n.isAuthenticated})})),gR={mode:"light",primaryColor:"#3b82f6",secondaryColor:"#64748b",borderRadius:8,fontSize:14,fontFamily:"Inter"},vR={apiUrl:"http://localhost:8000",wsUrl:"ws://localhost:8000",environment:"development",features:{},analytics:{enabled:!1,trackingId:"",debug:!0}},bR=q0()(P0((n,a)=>({theme:gR,sidebarOpen:!0,mobileMenuOpen:!1,toasts:[],currentPath:"/",breadcrumbs:[],globalLoading:!1,config:vR,features:{},setTheme:s=>{n(r=>({theme:{...r.theme,...s}}))},toggleTheme:()=>{n(s=>({theme:{...s.theme,mode:s.theme.mode==="light"?"dark":"light"}}))},setSidebarOpen:s=>{n({sidebarOpen:s})},toggleSidebar:()=>{n(s=>({sidebarOpen:!s.sidebarOpen}))},setMobileMenuOpen:s=>{n({mobileMenuOpen:s})},toggleMobileMenu:()=>{n(s=>({mobileMenuOpen:!s.mobileMenuOpen}))},addToast:s=>{const r=Math.random().toString(36).substr(2,9),o={...s,id:r};n(c=>({toasts:[...c.toasts,o]})),s.duration!==0&&setTimeout(()=>{a().removeToast(r)},s.duration||5e3)},removeToast:s=>{n(r=>({toasts:r.toasts.filter(o=>o.id!==s)}))},clearToasts:()=>{n({toasts:[]})},setCurrentPath:s=>{n({currentPath:s})},setBreadcrumbs:s=>{n({breadcrumbs:s})},setGlobalLoading:s=>{n({globalLoading:s})},updateConfig:s=>{n(r=>({config:{...r.config,...s}}))},setFeature:(s,r)=>{n(o=>({features:{...o.features,[s]:r}}))},isFeatureEnabled:s=>{const{features:r,config:o}=a();return r[s]??o.features[s]??!1}}),{name:"app-storage",storage:Ld(()=>localStorage),partialize:n=>({theme:n.theme,sidebarOpen:n.sidebarOpen,features:n.features})})),mb=z.createContext({});function SR(n){const a=z.useRef(null);return a.current===null&&(a.current=n()),a.current}const kd=typeof window<"u",xR=kd?z.useLayoutEffect:z.useEffect,Hd=z.createContext(null);function qd(n,a){n.indexOf(a)===-1&&n.push(a)}function Pd(n,a){const s=n.indexOf(a);s>-1&&n.splice(s,1)}const Qn=(n,a,s)=>s>a?a:s<n?n:s;let Gd=()=>{};const Kn={},pb=n=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(n);function yb(n){return typeof n=="object"&&n!==null}const gb=n=>/^0[^.\s]+$/u.test(n);function Yd(n){let a;return()=>(a===void 0&&(a=n()),a)}const en=n=>n,TR=(n,a)=>s=>a(n(s)),Ir=(...n)=>n.reduce(TR),kr=(n,a,s)=>{const r=a-n;return r===0?1:(s-n)/r};class Xd{constructor(){this.subscriptions=[]}add(a){return qd(this.subscriptions,a),()=>Pd(this.subscriptions,a)}notify(a,s,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](a,s,r);else for(let c=0;c<o;c++){const d=this.subscriptions[c];d&&d(a,s,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const xn=n=>n*1e3,Tn=n=>n/1e3;function vb(n,a){return a?n*(1e3/a):0}const bb=(n,a,s)=>(((1-3*s+3*a)*n+(3*s-6*a))*n+3*a)*n,AR=1e-7,ER=12;function wR(n,a,s,r,o){let c,d,h=0;do d=a+(s-a)/2,c=bb(d,r,o)-n,c>0?s=d:a=d;while(Math.abs(c)>AR&&++h<ER);return d}function tl(n,a,s,r){if(n===a&&s===r)return en;const o=c=>wR(c,0,1,n,s);return c=>c===0||c===1?c:bb(o(c),a,r)}const Sb=n=>a=>a<=.5?n(2*a)/2:(2-n(2*(1-a)))/2,xb=n=>a=>1-n(1-a),Tb=tl(.33,1.53,.69,.99),Qd=xb(Tb),Ab=Sb(Qd),Eb=n=>(n*=2)<1?.5*Qd(n):.5*(2-Math.pow(2,-10*(n-1))),Kd=n=>1-Math.sin(Math.acos(n)),wb=xb(Kd),Rb=Sb(Kd),RR=tl(.42,0,1,1),MR=tl(0,0,.58,1),Mb=tl(.42,0,.58,1),CR=n=>Array.isArray(n)&&typeof n[0]!="number",Cb=n=>Array.isArray(n)&&typeof n[0]=="number",OR={linear:en,easeIn:RR,easeInOut:Mb,easeOut:MR,circIn:Kd,circInOut:Rb,circOut:wb,backIn:Qd,backInOut:Ab,backOut:Tb,anticipate:Eb},DR=n=>typeof n=="string",Fg=n=>{if(Cb(n)){Gd(n.length===4);const[a,s,r,o]=n;return tl(a,s,r,o)}else if(DR(n))return OR[n];return n},To=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Zg={value:null};function UR(n,a){let s=new Set,r=new Set,o=!1,c=!1;const d=new WeakSet;let h={delta:0,timestamp:0,isProcessing:!1},p=0;function m(v){d.has(v)&&(y.schedule(v),n()),p++,v(h)}const y={schedule:(v,S=!1,E=!1)=>{const T=E&&o?s:r;return S&&d.add(v),T.has(v)||T.add(v),v},cancel:v=>{r.delete(v),d.delete(v)},process:v=>{if(h=v,o){c=!0;return}o=!0,[s,r]=[r,s],s.forEach(m),a&&Zg.value&&Zg.value.frameloop[a].push(p),p=0,s.clear(),o=!1,c&&(c=!1,y.process(v))}};return y}const NR=40;function Ob(n,a){let s=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},c=()=>s=!0,d=To.reduce((U,K)=>(U[K]=UR(c,a?K:void 0),U),{}),{setup:h,read:p,resolveKeyframes:m,preUpdate:y,update:v,preRender:S,render:E,postRender:x}=d,T=()=>{const U=Kn.useManualTiming?o.timestamp:performance.now();s=!1,Kn.useManualTiming||(o.delta=r?1e3/60:Math.max(Math.min(U-o.timestamp,NR),1)),o.timestamp=U,o.isProcessing=!0,h.process(o),p.process(o),m.process(o),y.process(o),v.process(o),S.process(o),E.process(o),x.process(o),o.isProcessing=!1,s&&a&&(r=!1,n(T))},w=()=>{s=!0,r=!0,o.isProcessing||n(T)};return{schedule:To.reduce((U,K)=>{const k=d[K];return U[K]=($,at=!1,Y=!1)=>(s||w(),k.schedule($,at,Y)),U},{}),cancel:U=>{for(let K=0;K<To.length;K++)d[To[K]].cancel(U)},state:o,steps:d}}const{schedule:Pt,cancel:Da,state:fe,steps:zf}=Ob(typeof requestAnimationFrame<"u"?requestAnimationFrame:en,!0);let No;function zR(){No=void 0}const Oe={now:()=>(No===void 0&&Oe.set(fe.isProcessing||Kn.useManualTiming?fe.timestamp:performance.now()),No),set:n=>{No=n,queueMicrotask(zR)}},Db=n=>a=>typeof a=="string"&&a.startsWith(n),Fd=Db("--"),_R=Db("var(--"),Zd=n=>_R(n)?LR.test(n.split("/*")[0].trim()):!1,LR=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,bs={test:n=>typeof n=="number",parse:parseFloat,transform:n=>n},Hr={...bs,transform:n=>Qn(0,1,n)},Ao={...bs,default:1},Dr=n=>Math.round(n*1e5)/1e5,$d=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function VR(n){return n==null}const BR=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Jd=(n,a)=>s=>!!(typeof s=="string"&&BR.test(s)&&s.startsWith(n)||a&&!VR(s)&&Object.prototype.hasOwnProperty.call(s,a)),Ub=(n,a,s)=>r=>{if(typeof r!="string")return r;const[o,c,d,h]=r.match($d);return{[n]:parseFloat(o),[a]:parseFloat(c),[s]:parseFloat(d),alpha:h!==void 0?parseFloat(h):1}},jR=n=>Qn(0,255,n),_f={...bs,transform:n=>Math.round(jR(n))},ei={test:Jd("rgb","red"),parse:Ub("red","green","blue"),transform:({red:n,green:a,blue:s,alpha:r=1})=>"rgba("+_f.transform(n)+", "+_f.transform(a)+", "+_f.transform(s)+", "+Dr(Hr.transform(r))+")"};function kR(n){let a="",s="",r="",o="";return n.length>5?(a=n.substring(1,3),s=n.substring(3,5),r=n.substring(5,7),o=n.substring(7,9)):(a=n.substring(1,2),s=n.substring(2,3),r=n.substring(3,4),o=n.substring(4,5),a+=a,s+=s,r+=r,o+=o),{red:parseInt(a,16),green:parseInt(s,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const id={test:Jd("#"),parse:kR,transform:ei.transform},el=n=>({test:a=>typeof a=="string"&&a.endsWith(n)&&a.split(" ").length===1,parse:parseFloat,transform:a=>`${a}${n}`}),Ea=el("deg"),An=el("%"),ut=el("px"),HR=el("vh"),qR=el("vw"),$g={...An,parse:n=>An.parse(n)/100,transform:n=>An.transform(n*100)},es={test:Jd("hsl","hue"),parse:Ub("hue","saturation","lightness"),transform:({hue:n,saturation:a,lightness:s,alpha:r=1})=>"hsla("+Math.round(n)+", "+An.transform(Dr(a))+", "+An.transform(Dr(s))+", "+Dr(Hr.transform(r))+")"},It={test:n=>ei.test(n)||id.test(n)||es.test(n),parse:n=>ei.test(n)?ei.parse(n):es.test(n)?es.parse(n):id.parse(n),transform:n=>typeof n=="string"?n:n.hasOwnProperty("red")?ei.transform(n):es.transform(n),getAnimatableNone:n=>{const a=It.parse(n);return a.alpha=0,It.transform(a)}},PR=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function GR(n){var a,s;return isNaN(n)&&typeof n=="string"&&(((a=n.match($d))==null?void 0:a.length)||0)+(((s=n.match(PR))==null?void 0:s.length)||0)>0}const Nb="number",zb="color",YR="var",XR="var(",Jg="${}",QR=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function qr(n){const a=n.toString(),s=[],r={color:[],number:[],var:[]},o=[];let c=0;const h=a.replace(QR,p=>(It.test(p)?(r.color.push(c),o.push(zb),s.push(It.parse(p))):p.startsWith(XR)?(r.var.push(c),o.push(YR),s.push(p)):(r.number.push(c),o.push(Nb),s.push(parseFloat(p))),++c,Jg)).split(Jg);return{values:s,split:h,indexes:r,types:o}}function _b(n){return qr(n).values}function Lb(n){const{split:a,types:s}=qr(n),r=a.length;return o=>{let c="";for(let d=0;d<r;d++)if(c+=a[d],o[d]!==void 0){const h=s[d];h===Nb?c+=Dr(o[d]):h===zb?c+=It.transform(o[d]):c+=o[d]}return c}}const KR=n=>typeof n=="number"?0:It.test(n)?It.getAnimatableNone(n):n;function FR(n){const a=_b(n);return Lb(n)(a.map(KR))}const Ua={test:GR,parse:_b,createTransformer:Lb,getAnimatableNone:FR};function Lf(n,a,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?n+(a-n)*6*s:s<1/2?a:s<2/3?n+(a-n)*(2/3-s)*6:n}function ZR({hue:n,saturation:a,lightness:s,alpha:r}){n/=360,a/=100,s/=100;let o=0,c=0,d=0;if(!a)o=c=d=s;else{const h=s<.5?s*(1+a):s+a-s*a,p=2*s-h;o=Lf(p,h,n+1/3),c=Lf(p,h,n),d=Lf(p,h,n-1/3)}return{red:Math.round(o*255),green:Math.round(c*255),blue:Math.round(d*255),alpha:r}}function Ho(n,a){return s=>s>0?a:n}const qt=(n,a,s)=>n+(a-n)*s,Vf=(n,a,s)=>{const r=n*n,o=s*(a*a-r)+r;return o<0?0:Math.sqrt(o)},$R=[id,ei,es],JR=n=>$R.find(a=>a.test(n));function Wg(n){const a=JR(n);if(!a)return!1;let s=a.parse(n);return a===es&&(s=ZR(s)),s}const Ig=(n,a)=>{const s=Wg(n),r=Wg(a);if(!s||!r)return Ho(n,a);const o={...s};return c=>(o.red=Vf(s.red,r.red,c),o.green=Vf(s.green,r.green,c),o.blue=Vf(s.blue,r.blue,c),o.alpha=qt(s.alpha,r.alpha,c),ei.transform(o))},sd=new Set(["none","hidden"]);function WR(n,a){return sd.has(n)?s=>s<=0?n:a:s=>s>=1?a:n}function IR(n,a){return s=>qt(n,a,s)}function Wd(n){return typeof n=="number"?IR:typeof n=="string"?Zd(n)?Ho:It.test(n)?Ig:nM:Array.isArray(n)?Vb:typeof n=="object"?It.test(n)?Ig:tM:Ho}function Vb(n,a){const s=[...n],r=s.length,o=n.map((c,d)=>Wd(c)(c,a[d]));return c=>{for(let d=0;d<r;d++)s[d]=o[d](c);return s}}function tM(n,a){const s={...n,...a},r={};for(const o in s)n[o]!==void 0&&a[o]!==void 0&&(r[o]=Wd(n[o])(n[o],a[o]));return o=>{for(const c in r)s[c]=r[c](o);return s}}function eM(n,a){const s=[],r={color:0,var:0,number:0};for(let o=0;o<a.values.length;o++){const c=a.types[o],d=n.indexes[c][r[c]],h=n.values[d]??0;s[o]=h,r[c]++}return s}const nM=(n,a)=>{const s=Ua.createTransformer(a),r=qr(n),o=qr(a);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?sd.has(n)&&!o.values.length||sd.has(a)&&!r.values.length?WR(n,a):Ir(Vb(eM(r,o),o.values),s):Ho(n,a)};function Bb(n,a,s){return typeof n=="number"&&typeof a=="number"&&typeof s=="number"?qt(n,a,s):Wd(n)(n,a)}const aM=n=>{const a=({timestamp:s})=>n(s);return{start:(s=!0)=>Pt.update(a,s),stop:()=>Da(a),now:()=>fe.isProcessing?fe.timestamp:Oe.now()}},jb=(n,a,s=10)=>{let r="";const o=Math.max(Math.round(a/s),2);for(let c=0;c<o;c++)r+=Math.round(n(c/(o-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},qo=2e4;function Id(n){let a=0;const s=50;let r=n.next(a);for(;!r.done&&a<qo;)a+=s,r=n.next(a);return a>=qo?1/0:a}function iM(n,a=100,s){const r=s({...n,keyframes:[0,a]}),o=Math.min(Id(r),qo);return{type:"keyframes",ease:c=>r.next(o*c).value/a,duration:Tn(o)}}const sM=5;function kb(n,a,s){const r=Math.max(a-sM,0);return vb(s-n(r),a-r)}const Qt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Bf=.001;function rM({duration:n=Qt.duration,bounce:a=Qt.bounce,velocity:s=Qt.velocity,mass:r=Qt.mass}){let o,c,d=1-a;d=Qn(Qt.minDamping,Qt.maxDamping,d),n=Qn(Qt.minDuration,Qt.maxDuration,Tn(n)),d<1?(o=m=>{const y=m*d,v=y*n,S=y-s,E=rd(m,d),x=Math.exp(-v);return Bf-S/E*x},c=m=>{const v=m*d*n,S=v*s+s,E=Math.pow(d,2)*Math.pow(m,2)*n,x=Math.exp(-v),T=rd(Math.pow(m,2),d);return(-o(m)+Bf>0?-1:1)*((S-E)*x)/T}):(o=m=>{const y=Math.exp(-m*n),v=(m-s)*n+1;return-Bf+y*v},c=m=>{const y=Math.exp(-m*n),v=(s-m)*(n*n);return y*v});const h=5/n,p=oM(o,c,h);if(n=xn(n),isNaN(p))return{stiffness:Qt.stiffness,damping:Qt.damping,duration:n};{const m=Math.pow(p,2)*r;return{stiffness:m,damping:d*2*Math.sqrt(r*m),duration:n}}}const lM=12;function oM(n,a,s){let r=s;for(let o=1;o<lM;o++)r=r-n(r)/a(r);return r}function rd(n,a){return n*Math.sqrt(1-a*a)}const uM=["duration","bounce"],cM=["stiffness","damping","mass"];function tv(n,a){return a.some(s=>n[s]!==void 0)}function fM(n){let a={velocity:Qt.velocity,stiffness:Qt.stiffness,damping:Qt.damping,mass:Qt.mass,isResolvedFromDuration:!1,...n};if(!tv(n,cM)&&tv(n,uM))if(n.visualDuration){const s=n.visualDuration,r=2*Math.PI/(s*1.2),o=r*r,c=2*Qn(.05,1,1-(n.bounce||0))*Math.sqrt(o);a={...a,mass:Qt.mass,stiffness:o,damping:c}}else{const s=rM(n);a={...a,...s,mass:Qt.mass},a.isResolvedFromDuration=!0}return a}function Po(n=Qt.visualDuration,a=Qt.bounce){const s=typeof n!="object"?{visualDuration:n,keyframes:[0,1],bounce:a}:n;let{restSpeed:r,restDelta:o}=s;const c=s.keyframes[0],d=s.keyframes[s.keyframes.length-1],h={done:!1,value:c},{stiffness:p,damping:m,mass:y,duration:v,velocity:S,isResolvedFromDuration:E}=fM({...s,velocity:-Tn(s.velocity||0)}),x=S||0,T=m/(2*Math.sqrt(p*y)),w=d-c,R=Tn(Math.sqrt(p/y)),V=Math.abs(w)<5;r||(r=V?Qt.restSpeed.granular:Qt.restSpeed.default),o||(o=V?Qt.restDelta.granular:Qt.restDelta.default);let U;if(T<1){const k=rd(R,T);U=$=>{const at=Math.exp(-T*R*$);return d-at*((x+T*R*w)/k*Math.sin(k*$)+w*Math.cos(k*$))}}else if(T===1)U=k=>d-Math.exp(-R*k)*(w+(x+R*w)*k);else{const k=R*Math.sqrt(T*T-1);U=$=>{const at=Math.exp(-T*R*$),Y=Math.min(k*$,300);return d-at*((x+T*R*w)*Math.sinh(Y)+k*w*Math.cosh(Y))/k}}const K={calculatedDuration:E&&v||null,next:k=>{const $=U(k);if(E)h.done=k>=v;else{let at=k===0?x:0;T<1&&(at=k===0?xn(x):kb(U,k,$));const Y=Math.abs(at)<=r,J=Math.abs(d-$)<=o;h.done=Y&&J}return h.value=h.done?d:$,h},toString:()=>{const k=Math.min(Id(K),qo),$=jb(at=>K.next(k*at).value,k,30);return k+"ms "+$},toTransition:()=>{}};return K}Po.applyToOptions=n=>{const a=iM(n,100,Po);return n.ease=a.ease,n.duration=xn(a.duration),n.type="keyframes",n};function ld({keyframes:n,velocity:a=0,power:s=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:c=500,modifyTarget:d,min:h,max:p,restDelta:m=.5,restSpeed:y}){const v=n[0],S={done:!1,value:v},E=Y=>h!==void 0&&Y<h||p!==void 0&&Y>p,x=Y=>h===void 0?p:p===void 0||Math.abs(h-Y)<Math.abs(p-Y)?h:p;let T=s*a;const w=v+T,R=d===void 0?w:d(w);R!==w&&(T=R-v);const V=Y=>-T*Math.exp(-Y/r),U=Y=>R+V(Y),K=Y=>{const J=V(Y),mt=U(Y);S.done=Math.abs(J)<=m,S.value=S.done?R:mt};let k,$;const at=Y=>{E(S.value)&&(k=Y,$=Po({keyframes:[S.value,x(S.value)],velocity:kb(U,Y,S.value),damping:o,stiffness:c,restDelta:m,restSpeed:y}))};return at(0),{calculatedDuration:null,next:Y=>{let J=!1;return!$&&k===void 0&&(J=!0,K(Y),at(Y)),k!==void 0&&Y>=k?$.next(Y-k):(!J&&K(Y),S)}}}function dM(n,a,s){const r=[],o=s||Kn.mix||Bb,c=n.length-1;for(let d=0;d<c;d++){let h=o(n[d],n[d+1]);if(a){const p=Array.isArray(a)?a[d]||en:a;h=Ir(p,h)}r.push(h)}return r}function hM(n,a,{clamp:s=!0,ease:r,mixer:o}={}){const c=n.length;if(Gd(c===a.length),c===1)return()=>a[0];if(c===2&&a[0]===a[1])return()=>a[1];const d=n[0]===n[1];n[0]>n[c-1]&&(n=[...n].reverse(),a=[...a].reverse());const h=dM(a,r,o),p=h.length,m=y=>{if(d&&y<n[0])return a[0];let v=0;if(p>1)for(;v<n.length-2&&!(y<n[v+1]);v++);const S=kr(n[v],n[v+1],y);return h[v](S)};return s?y=>m(Qn(n[0],n[c-1],y)):m}function mM(n,a){const s=n[n.length-1];for(let r=1;r<=a;r++){const o=kr(0,a,r);n.push(qt(s,1,o))}}function pM(n){const a=[0];return mM(a,n.length-1),a}function yM(n,a){return n.map(s=>s*a)}function gM(n,a){return n.map(()=>a||Mb).splice(0,n.length-1)}function Ur({duration:n=300,keyframes:a,times:s,ease:r="easeInOut"}){const o=CR(r)?r.map(Fg):Fg(r),c={done:!1,value:a[0]},d=yM(s&&s.length===a.length?s:pM(a),n),h=hM(d,a,{ease:Array.isArray(o)?o:gM(a,o)});return{calculatedDuration:n,next:p=>(c.value=h(p),c.done=p>=n,c)}}const vM=n=>n!==null;function th(n,{repeat:a,repeatType:s="loop"},r,o=1){const c=n.filter(vM),h=o<0||a&&s!=="loop"&&a%2===1?0:c.length-1;return!h||r===void 0?c[h]:r}const bM={decay:ld,inertia:ld,tween:Ur,keyframes:Ur,spring:Po};function Hb(n){typeof n.type=="string"&&(n.type=bM[n.type])}class eh{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,s){return this.finished.then(a,s)}}const SM=n=>n/100;class nh extends eh{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var r,o;const{motionValue:s}=this.options;s&&s.updatedAt!==Oe.now()&&this.tick(Oe.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(o=(r=this.options).onStop)==null||o.call(r))},this.options=a,this.initAnimation(),this.play(),a.autoplay===!1&&this.pause()}initAnimation(){const{options:a}=this;Hb(a);const{type:s=Ur,repeat:r=0,repeatDelay:o=0,repeatType:c,velocity:d=0}=a;let{keyframes:h}=a;const p=s||Ur;p!==Ur&&typeof h[0]!="number"&&(this.mixKeyframes=Ir(SM,Bb(h[0],h[1])),h=[0,100]);const m=p({...a,keyframes:h});c==="mirror"&&(this.mirroredGenerator=p({...a,keyframes:[...h].reverse(),velocity:-d})),m.calculatedDuration===null&&(m.calculatedDuration=Id(m));const{calculatedDuration:y}=m;this.calculatedDuration=y,this.resolvedDuration=y+o,this.totalDuration=this.resolvedDuration*(r+1)-o,this.generator=m}updateTime(a){const s=Math.round(a-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=s}tick(a,s=!1){const{generator:r,totalDuration:o,mixKeyframes:c,mirroredGenerator:d,resolvedDuration:h,calculatedDuration:p}=this;if(this.startTime===null)return r.next(0);const{delay:m=0,keyframes:y,repeat:v,repeatType:S,repeatDelay:E,type:x,onUpdate:T,finalKeyframe:w}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-o/this.speed,this.startTime)),s?this.currentTime=a:this.updateTime(a);const R=this.currentTime-m*(this.playbackSpeed>=0?1:-1),V=this.playbackSpeed>=0?R<0:R>o;this.currentTime=Math.max(R,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=o);let U=this.currentTime,K=r;if(v){const Y=Math.min(this.currentTime,o)/h;let J=Math.floor(Y),mt=Y%1;!mt&&Y>=1&&(mt=1),mt===1&&J--,J=Math.min(J,v+1),!!(J%2)&&(S==="reverse"?(mt=1-mt,E&&(mt-=E/h)):S==="mirror"&&(K=d)),U=Qn(0,1,mt)*h}const k=V?{done:!1,value:y[0]}:K.next(U);c&&(k.value=c(k.value));let{done:$}=k;!V&&p!==null&&($=this.playbackSpeed>=0?this.currentTime>=o:this.currentTime<=0);const at=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&$);return at&&x!==ld&&(k.value=th(y,this.options,w,this.speed)),T&&T(k.value),at&&this.finish(),k}then(a,s){return this.finished.then(a,s)}get duration(){return Tn(this.calculatedDuration)}get time(){return Tn(this.currentTime)}set time(a){var s;a=xn(a),this.currentTime=a,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),(s=this.driver)==null||s.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(Oe.now());const s=this.playbackSpeed!==a;this.playbackSpeed=a,s&&(this.time=Tn(this.currentTime))}play(){var o,c;if(this.isStopped)return;const{driver:a=aM,startTime:s}=this.options;this.driver||(this.driver=a(d=>this.tick(d))),(c=(o=this.options).onPlay)==null||c.call(o);const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=s??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Oe.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var a,s;this.notifyFinished(),this.teardown(),this.state="finished",(s=(a=this.options).onComplete)==null||s.call(a)}cancel(){var a,s;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(s=(a=this.options).onCancel)==null||s.call(a)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){var s;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(s=this.driver)==null||s.stop(),a.observe(this)}}function xM(n){for(let a=1;a<n.length;a++)n[a]??(n[a]=n[a-1])}const ni=n=>n*180/Math.PI,od=n=>{const a=ni(Math.atan2(n[1],n[0]));return ud(a)},TM={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:n=>(Math.abs(n[0])+Math.abs(n[3]))/2,rotate:od,rotateZ:od,skewX:n=>ni(Math.atan(n[1])),skewY:n=>ni(Math.atan(n[2])),skew:n=>(Math.abs(n[1])+Math.abs(n[2]))/2},ud=n=>(n=n%360,n<0&&(n+=360),n),ev=od,nv=n=>Math.sqrt(n[0]*n[0]+n[1]*n[1]),av=n=>Math.sqrt(n[4]*n[4]+n[5]*n[5]),AM={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:nv,scaleY:av,scale:n=>(nv(n)+av(n))/2,rotateX:n=>ud(ni(Math.atan2(n[6],n[5]))),rotateY:n=>ud(ni(Math.atan2(-n[2],n[0]))),rotateZ:ev,rotate:ev,skewX:n=>ni(Math.atan(n[4])),skewY:n=>ni(Math.atan(n[1])),skew:n=>(Math.abs(n[1])+Math.abs(n[4]))/2};function cd(n){return n.includes("scale")?1:0}function fd(n,a){if(!n||n==="none")return cd(a);const s=n.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,o;if(s)r=AM,o=s;else{const h=n.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=TM,o=h}if(!o)return cd(a);const c=r[a],d=o[1].split(",").map(wM);return typeof c=="function"?c(d):d[c]}const EM=(n,a)=>{const{transform:s="none"}=getComputedStyle(n);return fd(s,a)};function wM(n){return parseFloat(n.trim())}const Ss=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],xs=new Set(Ss),iv=n=>n===bs||n===ut,RM=new Set(["x","y","z"]),MM=Ss.filter(n=>!RM.has(n));function CM(n){const a=[];return MM.forEach(s=>{const r=n.getValue(s);r!==void 0&&(a.push([s,r.get()]),r.set(s.startsWith("scale")?1:0))}),a}const ci={width:({x:n},{paddingLeft:a="0",paddingRight:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),height:({y:n},{paddingTop:a="0",paddingBottom:s="0"})=>n.max-n.min-parseFloat(a)-parseFloat(s),top:(n,{top:a})=>parseFloat(a),left:(n,{left:a})=>parseFloat(a),bottom:({y:n},{top:a})=>parseFloat(a)+(n.max-n.min),right:({x:n},{left:a})=>parseFloat(a)+(n.max-n.min),x:(n,{transform:a})=>fd(a,"x"),y:(n,{transform:a})=>fd(a,"y")};ci.translateX=ci.x;ci.translateY=ci.y;const fi=new Set;let dd=!1,hd=!1,md=!1;function qb(){if(hd){const n=Array.from(fi).filter(r=>r.needsMeasurement),a=new Set(n.map(r=>r.element)),s=new Map;a.forEach(r=>{const o=CM(r);o.length&&(s.set(r,o),r.render())}),n.forEach(r=>r.measureInitialState()),a.forEach(r=>{r.render();const o=s.get(r);o&&o.forEach(([c,d])=>{var h;(h=r.getValue(c))==null||h.set(d)})}),n.forEach(r=>r.measureEndState()),n.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}hd=!1,dd=!1,fi.forEach(n=>n.complete(md)),fi.clear()}function Pb(){fi.forEach(n=>{n.readKeyframes(),n.needsMeasurement&&(hd=!0)})}function OM(){md=!0,Pb(),qb(),md=!1}class ah{constructor(a,s,r,o,c,d=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=s,this.name=r,this.motionValue=o,this.element=c,this.isAsync=d}scheduleResolve(){this.state="scheduled",this.isAsync?(fi.add(this),dd||(dd=!0,Pt.read(Pb),Pt.resolveKeyframes(qb))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:a,name:s,element:r,motionValue:o}=this;if(a[0]===null){const c=o==null?void 0:o.get(),d=a[a.length-1];if(c!==void 0)a[0]=c;else if(r&&s){const h=r.readValue(s,d);h!=null&&(a[0]=h)}a[0]===void 0&&(a[0]=d),o&&c===void 0&&o.set(a[0])}xM(a)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),fi.delete(this)}cancel(){this.state==="scheduled"&&(fi.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const DM=n=>n.startsWith("--");function UM(n,a,s){DM(a)?n.style.setProperty(a,s):n.style[a]=s}const NM=Yd(()=>window.ScrollTimeline!==void 0),zM={};function _M(n,a){const s=Yd(n);return()=>zM[a]??s()}const Gb=_M(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Or=([n,a,s,r])=>`cubic-bezier(${n}, ${a}, ${s}, ${r})`,sv={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Or([0,.65,.55,1]),circOut:Or([.55,0,1,.45]),backIn:Or([.31,.01,.66,-.59]),backOut:Or([.33,1.53,.69,.99])};function Yb(n,a){if(n)return typeof n=="function"?Gb()?jb(n,a):"ease-out":Cb(n)?Or(n):Array.isArray(n)?n.map(s=>Yb(s,a)||sv.easeOut):sv[n]}function LM(n,a,s,{delay:r=0,duration:o=300,repeat:c=0,repeatType:d="loop",ease:h="easeOut",times:p}={},m=void 0){const y={[a]:s};p&&(y.offset=p);const v=Yb(h,o);Array.isArray(v)&&(y.easing=v);const S={delay:r,duration:o,easing:Array.isArray(v)?"linear":v,fill:"both",iterations:c+1,direction:d==="reverse"?"alternate":"normal"};return m&&(S.pseudoElement=m),n.animate(y,S)}function Xb(n){return typeof n=="function"&&"applyToOptions"in n}function VM({type:n,...a}){return Xb(n)&&Gb()?n.applyToOptions(a):(a.duration??(a.duration=300),a.ease??(a.ease="easeOut"),a)}class BM extends eh{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;const{element:s,name:r,keyframes:o,pseudoElement:c,allowFlatten:d=!1,finalKeyframe:h,onComplete:p}=a;this.isPseudoElement=!!c,this.allowFlatten=d,this.options=a,Gd(typeof a.type!="string");const m=VM(a);this.animation=LM(s,r,o,m,c),m.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!c){const y=th(o,this.options,h,this.speed);this.updateMotionValue?this.updateMotionValue(y):UM(s,r,y),this.animation.cancel()}p==null||p(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var a,s;(s=(a=this.animation).finish)==null||s.call(a)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:a}=this;a==="idle"||a==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var a,s;this.isPseudoElement||(s=(a=this.animation).commitStyles)==null||s.call(a)}get duration(){var s,r;const a=((r=(s=this.animation.effect)==null?void 0:s.getComputedTiming)==null?void 0:r.call(s).duration)||0;return Tn(Number(a))}get time(){return Tn(Number(this.animation.currentTime)||0)}set time(a){this.finishedTime=null,this.animation.currentTime=xn(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:s}){var r;return this.allowFlatten&&((r=this.animation.effect)==null||r.updateTiming({easing:"linear"})),this.animation.onfinish=null,a&&NM()?(this.animation.timeline=a,en):s(this)}}const Qb={anticipate:Eb,backInOut:Ab,circInOut:Rb};function jM(n){return n in Qb}function kM(n){typeof n.ease=="string"&&jM(n.ease)&&(n.ease=Qb[n.ease])}const rv=10;class HM extends BM{constructor(a){kM(a),Hb(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){const{motionValue:s,onUpdate:r,onComplete:o,element:c,...d}=this.options;if(!s)return;if(a!==void 0){s.set(a);return}const h=new nh({...d,autoplay:!1}),p=xn(this.finishedTime??this.time);s.setWithVelocity(h.sample(p-rv).value,h.sample(p).value,rv),h.stop()}}const lv=(n,a)=>a==="zIndex"?!1:!!(typeof n=="number"||Array.isArray(n)||typeof n=="string"&&(Ua.test(n)||n==="0")&&!n.startsWith("url("));function qM(n){const a=n[0];if(n.length===1)return!0;for(let s=0;s<n.length;s++)if(n[s]!==a)return!0}function PM(n,a,s,r){const o=n[0];if(o===null)return!1;if(a==="display"||a==="visibility")return!0;const c=n[n.length-1],d=lv(o,a),h=lv(c,a);return!d||!h?!1:qM(n)||(s==="spring"||Xb(s))&&r}function Kb(n){return yb(n)&&"offsetHeight"in n}const GM=new Set(["opacity","clipPath","filter","transform"]),YM=Yd(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function XM(n){var m;const{motionValue:a,name:s,repeatDelay:r,repeatType:o,damping:c,type:d}=n;if(!Kb((m=a==null?void 0:a.owner)==null?void 0:m.current))return!1;const{onUpdate:h,transformTemplate:p}=a.owner.getProps();return YM()&&s&&GM.has(s)&&(s!=="transform"||!p)&&!h&&!r&&o!=="mirror"&&c!==0&&d!=="inertia"}const QM=40;class KM extends eh{constructor({autoplay:a=!0,delay:s=0,type:r="keyframes",repeat:o=0,repeatDelay:c=0,repeatType:d="loop",keyframes:h,name:p,motionValue:m,element:y,...v}){var x;super(),this.stop=()=>{var T,w;this._animation&&(this._animation.stop(),(T=this.stopTimeline)==null||T.call(this)),(w=this.keyframeResolver)==null||w.cancel()},this.createdAt=Oe.now();const S={autoplay:a,delay:s,type:r,repeat:o,repeatDelay:c,repeatType:d,name:p,motionValue:m,element:y,...v},E=(y==null?void 0:y.KeyframeResolver)||ah;this.keyframeResolver=new E(h,(T,w,R)=>this.onKeyframesResolved(T,w,S,!R),p,m,y),(x=this.keyframeResolver)==null||x.scheduleResolve()}onKeyframesResolved(a,s,r,o){this.keyframeResolver=void 0;const{name:c,type:d,velocity:h,delay:p,isHandoff:m,onUpdate:y}=r;this.resolvedAt=Oe.now(),PM(a,c,d,h)||((Kn.instantAnimations||!p)&&(y==null||y(th(a,r,s))),a[0]=a[a.length-1],r.duration=0,r.repeat=0);const S={startTime:o?this.resolvedAt?this.resolvedAt-this.createdAt>QM?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:s,...r,keyframes:a},E=!m&&XM(S)?new HM({...S,element:S.motionValue.owner.current}):new nh(S);E.finished.then(()=>this.notifyFinished()).catch(en),this.pendingTimeline&&(this.stopTimeline=E.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=E}get finished(){return this._animation?this.animation.finished:this._finished}then(a,s){return this.finished.finally(a).then(()=>{})}get animation(){var a;return this._animation||((a=this.keyframeResolver)==null||a.resume(),OM()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var a;this._animation&&this.animation.cancel(),(a=this.keyframeResolver)==null||a.cancel()}}const FM=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ZM(n){const a=FM.exec(n);if(!a)return[,];const[,s,r,o]=a;return[`--${s??r}`,o]}function Fb(n,a,s=1){const[r,o]=ZM(n);if(!r)return;const c=window.getComputedStyle(a).getPropertyValue(r);if(c){const d=c.trim();return pb(d)?parseFloat(d):d}return Zd(o)?Fb(o,a,s+1):o}function ih(n,a){return(n==null?void 0:n[a])??(n==null?void 0:n.default)??n}const Zb=new Set(["width","height","top","left","right","bottom",...Ss]),$M={test:n=>n==="auto",parse:n=>n},$b=n=>a=>a.test(n),Jb=[bs,ut,An,Ea,qR,HR,$M],ov=n=>Jb.find($b(n));function JM(n){return typeof n=="number"?n===0:n!==null?n==="none"||n==="0"||gb(n):!0}const WM=new Set(["brightness","contrast","saturate","opacity"]);function IM(n){const[a,s]=n.slice(0,-1).split("(");if(a==="drop-shadow")return n;const[r]=s.match($d)||[];if(!r)return n;const o=s.replace(r,"");let c=WM.has(a)?1:0;return r!==s&&(c*=100),a+"("+c+o+")"}const tC=/\b([a-z-]*)\(.*?\)/gu,pd={...Ua,getAnimatableNone:n=>{const a=n.match(tC);return a?a.map(IM).join(" "):n}},uv={...bs,transform:Math.round},eC={rotate:Ea,rotateX:Ea,rotateY:Ea,rotateZ:Ea,scale:Ao,scaleX:Ao,scaleY:Ao,scaleZ:Ao,skew:Ea,skewX:Ea,skewY:Ea,distance:ut,translateX:ut,translateY:ut,translateZ:ut,x:ut,y:ut,z:ut,perspective:ut,transformPerspective:ut,opacity:Hr,originX:$g,originY:$g,originZ:ut},sh={borderWidth:ut,borderTopWidth:ut,borderRightWidth:ut,borderBottomWidth:ut,borderLeftWidth:ut,borderRadius:ut,radius:ut,borderTopLeftRadius:ut,borderTopRightRadius:ut,borderBottomRightRadius:ut,borderBottomLeftRadius:ut,width:ut,maxWidth:ut,height:ut,maxHeight:ut,top:ut,right:ut,bottom:ut,left:ut,padding:ut,paddingTop:ut,paddingRight:ut,paddingBottom:ut,paddingLeft:ut,margin:ut,marginTop:ut,marginRight:ut,marginBottom:ut,marginLeft:ut,backgroundPositionX:ut,backgroundPositionY:ut,...eC,zIndex:uv,fillOpacity:Hr,strokeOpacity:Hr,numOctaves:uv},nC={...sh,color:It,backgroundColor:It,outlineColor:It,fill:It,stroke:It,borderColor:It,borderTopColor:It,borderRightColor:It,borderBottomColor:It,borderLeftColor:It,filter:pd,WebkitFilter:pd},Wb=n=>nC[n];function Ib(n,a){let s=Wb(n);return s!==pd&&(s=Ua),s.getAnimatableNone?s.getAnimatableNone(a):void 0}const aC=new Set(["auto","none","0"]);function iC(n,a,s){let r=0,o;for(;r<n.length&&!o;){const c=n[r];typeof c=="string"&&!aC.has(c)&&qr(c).values.length&&(o=n[r]),r++}if(o&&s)for(const c of a)n[c]=Ib(s,o)}class sC extends ah{constructor(a,s,r,o,c){super(a,s,r,o,c,!0)}readKeyframes(){const{unresolvedKeyframes:a,element:s,name:r}=this;if(!s||!s.current)return;super.readKeyframes();for(let p=0;p<a.length;p++){let m=a[p];if(typeof m=="string"&&(m=m.trim(),Zd(m))){const y=Fb(m,s.current);y!==void 0&&(a[p]=y),p===a.length-1&&(this.finalKeyframe=m)}}if(this.resolveNoneKeyframes(),!Zb.has(r)||a.length!==2)return;const[o,c]=a,d=ov(o),h=ov(c);if(d!==h)if(iv(d)&&iv(h))for(let p=0;p<a.length;p++){const m=a[p];typeof m=="string"&&(a[p]=parseFloat(m))}else ci[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:a,name:s}=this,r=[];for(let o=0;o<a.length;o++)(a[o]===null||JM(a[o]))&&r.push(o);r.length&&iC(a,r,s)}measureInitialState(){const{element:a,unresolvedKeyframes:s,name:r}=this;if(!a||!a.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ci[r](a.measureViewportBox(),window.getComputedStyle(a.current)),s[0]=this.measuredOrigin;const o=s[s.length-1];o!==void 0&&a.getValue(r,o).jump(o,!1)}measureEndState(){var h;const{element:a,name:s,unresolvedKeyframes:r}=this;if(!a||!a.current)return;const o=a.getValue(s);o&&o.jump(this.measuredOrigin,!1);const c=r.length-1,d=r[c];r[c]=ci[s](a.measureViewportBox(),window.getComputedStyle(a.current)),d!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=d),(h=this.removedTransforms)!=null&&h.length&&this.removedTransforms.forEach(([p,m])=>{a.getValue(p).set(m)}),this.resolveNoneKeyframes()}}function rC(n,a,s){if(n instanceof EventTarget)return[n];if(typeof n=="string"){let r=document;const o=(s==null?void 0:s[n])??r.querySelectorAll(n);return o?Array.from(o):[]}return Array.from(n)}const tS=(n,a)=>a&&typeof n=="number"?a.transform(n):n,cv=30,lC=n=>!isNaN(parseFloat(n));class oC{constructor(a,s={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,o=!0)=>{var d,h;const c=Oe.now();if(this.updatedAt!==c&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&((d=this.events.change)==null||d.notify(this.current),this.dependents))for(const p of this.dependents)p.dirty();o&&((h=this.events.renderRequest)==null||h.notify(this.current))},this.hasAnimated=!1,this.setCurrent(a),this.owner=s.owner}setCurrent(a){this.current=a,this.updatedAt=Oe.now(),this.canTrackVelocity===null&&a!==void 0&&(this.canTrackVelocity=lC(this.current))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,s){this.events[a]||(this.events[a]=new Xd);const r=this.events[a].add(s);return a==="change"?()=>{r(),Pt.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const a in this.events)this.events[a].clear()}attach(a,s){this.passiveEffect=a,this.stopPassiveEffect=s}set(a,s=!0){!s||!this.passiveEffect?this.updateAndNotify(a,s):this.passiveEffect(a,this.updateAndNotify)}setWithVelocity(a,s,r){this.set(s),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-r}jump(a,s=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var a;(a=this.events.change)==null||a.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const a=Oe.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||a-this.updatedAt>cv)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,cv);return vb(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(a){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=a(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var a,s;(a=this.dependents)==null||a.clear(),(s=this.events.destroy)==null||s.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ms(n,a){return new oC(n,a)}const{schedule:rh}=Ob(queueMicrotask,!1),rn={x:!1,y:!1};function eS(){return rn.x||rn.y}function uC(n){return n==="x"||n==="y"?rn[n]?null:(rn[n]=!0,()=>{rn[n]=!1}):rn.x||rn.y?null:(rn.x=rn.y=!0,()=>{rn.x=rn.y=!1})}function nS(n,a){const s=rC(n),r=new AbortController,o={passive:!0,...a,signal:r.signal};return[s,o,()=>r.abort()]}function fv(n){return!(n.pointerType==="touch"||eS())}function cC(n,a,s={}){const[r,o,c]=nS(n,s),d=h=>{if(!fv(h))return;const{target:p}=h,m=a(p,h);if(typeof m!="function"||!p)return;const y=v=>{fv(v)&&(m(v),p.removeEventListener("pointerleave",y))};p.addEventListener("pointerleave",y,o)};return r.forEach(h=>{h.addEventListener("pointerenter",d,o)}),c}const aS=(n,a)=>a?n===a?!0:aS(n,a.parentElement):!1,lh=n=>n.pointerType==="mouse"?typeof n.button!="number"||n.button<=0:n.isPrimary!==!1,fC=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function dC(n){return fC.has(n.tagName)||n.tabIndex!==-1}const zo=new WeakSet;function dv(n){return a=>{a.key==="Enter"&&n(a)}}function jf(n,a){n.dispatchEvent(new PointerEvent("pointer"+a,{isPrimary:!0,bubbles:!0}))}const hC=(n,a)=>{const s=n.currentTarget;if(!s)return;const r=dv(()=>{if(zo.has(s))return;jf(s,"down");const o=dv(()=>{jf(s,"up")}),c=()=>jf(s,"cancel");s.addEventListener("keyup",o,a),s.addEventListener("blur",c,a)});s.addEventListener("keydown",r,a),s.addEventListener("blur",()=>s.removeEventListener("keydown",r),a)};function hv(n){return lh(n)&&!eS()}function mC(n,a,s={}){const[r,o,c]=nS(n,s),d=h=>{const p=h.currentTarget;if(!hv(h))return;zo.add(p);const m=a(p,h),y=(E,x)=>{window.removeEventListener("pointerup",v),window.removeEventListener("pointercancel",S),zo.has(p)&&zo.delete(p),hv(E)&&typeof m=="function"&&m(E,{success:x})},v=E=>{y(E,p===window||p===document||s.useGlobalTarget||aS(p,E.target))},S=E=>{y(E,!1)};window.addEventListener("pointerup",v,o),window.addEventListener("pointercancel",S,o)};return r.forEach(h=>{(s.useGlobalTarget?window:h).addEventListener("pointerdown",d,o),Kb(h)&&(h.addEventListener("focus",m=>hC(m,o)),!dC(h)&&!h.hasAttribute("tabindex")&&(h.tabIndex=0))}),c}function iS(n){return yb(n)&&"ownerSVGElement"in n}function pC(n){return iS(n)&&n.tagName==="svg"}const ve=n=>!!(n&&n.getVelocity),yC=[...Jb,It,Ua],gC=n=>yC.find($b(n)),sS=z.createContext({transformPagePoint:n=>n,isStatic:!1,reducedMotion:"never"});function vC(n=!0){const a=z.useContext(Hd);if(a===null)return[!0,null];const{isPresent:s,onExitComplete:r,register:o}=a,c=z.useId();z.useEffect(()=>{if(n)return o(c)},[n]);const d=z.useCallback(()=>n&&r&&r(c),[c,r,n]);return!s&&r?[!1,d]:[!0]}const rS=z.createContext({strict:!1}),mv={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ps={};for(const n in mv)ps[n]={isEnabled:a=>mv[n].some(s=>!!a[s])};function bC(n){for(const a in n)ps[a]={...ps[a],...n[a]}}const SC=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Go(n){return n.startsWith("while")||n.startsWith("drag")&&n!=="draggable"||n.startsWith("layout")||n.startsWith("onTap")||n.startsWith("onPan")||n.startsWith("onLayout")||SC.has(n)}let lS=n=>!Go(n);function xC(n){typeof n=="function"&&(lS=a=>a.startsWith("on")?!Go(a):n(a))}try{xC(require("@emotion/is-prop-valid").default)}catch{}function TC(n,a,s){const r={};for(const o in n)o==="values"&&typeof n.values=="object"||(lS(o)||s===!0&&Go(o)||!a&&!Go(o)||n.draggable&&o.startsWith("onDrag"))&&(r[o]=n[o]);return r}function AC(n){if(typeof Proxy>"u")return n;const a=new Map,s=(...r)=>n(...r);return new Proxy(s,{get:(r,o)=>o==="create"?n:(a.has(o)||a.set(o,n(o)),a.get(o))})}const au=z.createContext({});function iu(n){return n!==null&&typeof n=="object"&&typeof n.start=="function"}function Pr(n){return typeof n=="string"||Array.isArray(n)}const oh=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],uh=["initial",...oh];function su(n){return iu(n.animate)||uh.some(a=>Pr(n[a]))}function oS(n){return!!(su(n)||n.variants)}function EC(n,a){if(su(n)){const{initial:s,animate:r}=n;return{initial:s===!1||Pr(s)?s:void 0,animate:Pr(r)?r:void 0}}return n.inherit!==!1?a:{}}function wC(n){const{initial:a,animate:s}=EC(n,z.useContext(au));return z.useMemo(()=>({initial:a,animate:s}),[pv(a),pv(s)])}function pv(n){return Array.isArray(n)?n.join(" "):n}const RC=Symbol.for("motionComponentSymbol");function ns(n){return n&&typeof n=="object"&&Object.prototype.hasOwnProperty.call(n,"current")}function MC(n,a,s){return z.useCallback(r=>{r&&n.onMount&&n.onMount(r),a&&(r?a.mount(r):a.unmount()),s&&(typeof s=="function"?s(r):ns(s)&&(s.current=r))},[a])}const ch=n=>n.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),CC="framerAppearId",uS="data-"+ch(CC),cS=z.createContext({});function OC(n,a,s,r,o){var T,w;const{visualElement:c}=z.useContext(au),d=z.useContext(rS),h=z.useContext(Hd),p=z.useContext(sS).reducedMotion,m=z.useRef(null);r=r||d.renderer,!m.current&&r&&(m.current=r(n,{visualState:a,parent:c,props:s,presenceContext:h,blockInitialAnimation:h?h.initial===!1:!1,reducedMotionConfig:p}));const y=m.current,v=z.useContext(cS);y&&!y.projection&&o&&(y.type==="html"||y.type==="svg")&&DC(m.current,s,o,v);const S=z.useRef(!1);z.useInsertionEffect(()=>{y&&S.current&&y.update(s,h)});const E=s[uS],x=z.useRef(!!E&&!((T=window.MotionHandoffIsComplete)!=null&&T.call(window,E))&&((w=window.MotionHasOptimisedAnimation)==null?void 0:w.call(window,E)));return xR(()=>{y&&(S.current=!0,window.MotionIsMounted=!0,y.updateFeatures(),rh.render(y.render),x.current&&y.animationState&&y.animationState.animateChanges())}),z.useEffect(()=>{y&&(!x.current&&y.animationState&&y.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var R;(R=window.MotionHandoffMarkAsComplete)==null||R.call(window,E)}),x.current=!1))}),y}function DC(n,a,s,r){const{layoutId:o,layout:c,drag:d,dragConstraints:h,layoutScroll:p,layoutRoot:m,layoutCrossfade:y}=a;n.projection=new s(n.latestValues,a["data-framer-portal-id"]?void 0:fS(n.parent)),n.projection.setOptions({layoutId:o,layout:c,alwaysMeasureLayout:!!d||h&&ns(h),visualElement:n,animationType:typeof c=="string"?c:"both",initialPromotionConfig:r,crossfade:y,layoutScroll:p,layoutRoot:m})}function fS(n){if(n)return n.options.allowProjection!==!1?n.projection:fS(n.parent)}function UC({preloadedFeatures:n,createVisualElement:a,useRender:s,useVisualState:r,Component:o}){n&&bC(n);function c(h,p){let m;const y={...z.useContext(sS),...h,layoutId:NC(h)},{isStatic:v}=y,S=wC(h),E=r(h,v);if(!v&&kd){zC();const x=_C(y);m=x.MeasureLayout,S.visualElement=OC(o,E,y,a,x.ProjectionNode)}return it.jsxs(au.Provider,{value:S,children:[m&&S.visualElement?it.jsx(m,{visualElement:S.visualElement,...y}):null,s(o,h,MC(E,S.visualElement,p),E,v,S.visualElement)]})}c.displayName=`motion.${typeof o=="string"?o:`create(${o.displayName??o.name??""})`}`;const d=z.forwardRef(c);return d[RC]=o,d}function NC({layoutId:n}){const a=z.useContext(mb).id;return a&&n!==void 0?a+"-"+n:n}function zC(n,a){z.useContext(rS).strict}function _C(n){const{drag:a,layout:s}=ps;if(!a&&!s)return{};const r={...a,...s};return{MeasureLayout:a!=null&&a.isEnabled(n)||s!=null&&s.isEnabled(n)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const Gr={};function LC(n){for(const a in n)Gr[a]=n[a],Fd(a)&&(Gr[a].isCSSVariable=!0)}function dS(n,{layout:a,layoutId:s}){return xs.has(n)||n.startsWith("origin")||(a||s!==void 0)&&(!!Gr[n]||n==="opacity")}const VC={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},BC=Ss.length;function jC(n,a,s){let r="",o=!0;for(let c=0;c<BC;c++){const d=Ss[c],h=n[d];if(h===void 0)continue;let p=!0;if(typeof h=="number"?p=h===(d.startsWith("scale")?1:0):p=parseFloat(h)===0,!p||s){const m=tS(h,sh[d]);if(!p){o=!1;const y=VC[d]||d;r+=`${y}(${m}) `}s&&(a[d]=m)}}return r=r.trim(),s?r=s(a,o?"":r):o&&(r="none"),r}function fh(n,a,s){const{style:r,vars:o,transformOrigin:c}=n;let d=!1,h=!1;for(const p in a){const m=a[p];if(xs.has(p)){d=!0;continue}else if(Fd(p)){o[p]=m;continue}else{const y=tS(m,sh[p]);p.startsWith("origin")?(h=!0,c[p]=y):r[p]=y}}if(a.transform||(d||s?r.transform=jC(a,n.transform,s):r.transform&&(r.transform="none")),h){const{originX:p="50%",originY:m="50%",originZ:y=0}=c;r.transformOrigin=`${p} ${m} ${y}`}}const dh=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function hS(n,a,s){for(const r in a)!ve(a[r])&&!dS(r,s)&&(n[r]=a[r])}function kC({transformTemplate:n},a){return z.useMemo(()=>{const s=dh();return fh(s,a,n),Object.assign({},s.vars,s.style)},[a])}function HC(n,a){const s=n.style||{},r={};return hS(r,s,n),Object.assign(r,kC(n,a)),r}function qC(n,a){const s={},r=HC(n,a);return n.drag&&n.dragListener!==!1&&(s.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=n.drag===!0?"none":`pan-${n.drag==="x"?"y":"x"}`),n.tabIndex===void 0&&(n.onTap||n.onTapStart||n.whileTap)&&(s.tabIndex=0),s.style=r,s}const PC={offset:"stroke-dashoffset",array:"stroke-dasharray"},GC={offset:"strokeDashoffset",array:"strokeDasharray"};function YC(n,a,s=1,r=0,o=!0){n.pathLength=1;const c=o?PC:GC;n[c.offset]=ut.transform(-r);const d=ut.transform(a),h=ut.transform(s);n[c.array]=`${d} ${h}`}function mS(n,{attrX:a,attrY:s,attrScale:r,pathLength:o,pathSpacing:c=1,pathOffset:d=0,...h},p,m,y){if(fh(n,h,m),p){n.style.viewBox&&(n.attrs.viewBox=n.style.viewBox);return}n.attrs=n.style,n.style={};const{attrs:v,style:S}=n;v.transform&&(S.transform=v.transform,delete v.transform),(S.transform||v.transformOrigin)&&(S.transformOrigin=v.transformOrigin??"50% 50%",delete v.transformOrigin),S.transform&&(S.transformBox=(y==null?void 0:y.transformBox)??"fill-box",delete v.transformBox),a!==void 0&&(v.x=a),s!==void 0&&(v.y=s),r!==void 0&&(v.scale=r),o!==void 0&&YC(v,o,c,d,!1)}const pS=()=>({...dh(),attrs:{}}),yS=n=>typeof n=="string"&&n.toLowerCase()==="svg";function XC(n,a,s,r){const o=z.useMemo(()=>{const c=pS();return mS(c,a,yS(r),n.transformTemplate,n.style),{...c.attrs,style:{...c.style}}},[a]);if(n.style){const c={};hS(c,n.style,n),o.style={...c,...o.style}}return o}const QC=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function hh(n){return typeof n!="string"||n.includes("-")?!1:!!(QC.indexOf(n)>-1||/[A-Z]/u.test(n))}function KC(n=!1){return(s,r,o,{latestValues:c},d)=>{const p=(hh(s)?XC:qC)(r,c,d,s),m=TC(r,typeof s=="string",n),y=s!==z.Fragment?{...m,...p,ref:o}:{},{children:v}=r,S=z.useMemo(()=>ve(v)?v.get():v,[v]);return z.createElement(s,{...y,children:S})}}function yv(n){const a=[{},{}];return n==null||n.values.forEach((s,r)=>{a[0][r]=s.get(),a[1][r]=s.getVelocity()}),a}function mh(n,a,s,r){if(typeof a=="function"){const[o,c]=yv(r);a=a(s!==void 0?s:n.custom,o,c)}if(typeof a=="string"&&(a=n.variants&&n.variants[a]),typeof a=="function"){const[o,c]=yv(r);a=a(s!==void 0?s:n.custom,o,c)}return a}function _o(n){return ve(n)?n.get():n}function FC({scrapeMotionValuesFromProps:n,createRenderState:a},s,r,o){return{latestValues:ZC(s,r,o,n),renderState:a()}}const gS=n=>(a,s)=>{const r=z.useContext(au),o=z.useContext(Hd),c=()=>FC(n,a,r,o);return s?c():SR(c)};function ZC(n,a,s,r){const o={},c=r(n,{});for(const S in c)o[S]=_o(c[S]);let{initial:d,animate:h}=n;const p=su(n),m=oS(n);a&&m&&!p&&n.inherit!==!1&&(d===void 0&&(d=a.initial),h===void 0&&(h=a.animate));let y=s?s.initial===!1:!1;y=y||d===!1;const v=y?h:d;if(v&&typeof v!="boolean"&&!iu(v)){const S=Array.isArray(v)?v:[v];for(let E=0;E<S.length;E++){const x=mh(n,S[E]);if(x){const{transitionEnd:T,transition:w,...R}=x;for(const V in R){let U=R[V];if(Array.isArray(U)){const K=y?U.length-1:0;U=U[K]}U!==null&&(o[V]=U)}for(const V in T)o[V]=T[V]}}}return o}function ph(n,a,s){var c;const{style:r}=n,o={};for(const d in r)(ve(r[d])||a.style&&ve(a.style[d])||dS(d,n)||((c=s==null?void 0:s.getValue(d))==null?void 0:c.liveStyle)!==void 0)&&(o[d]=r[d]);return o}const $C={useVisualState:gS({scrapeMotionValuesFromProps:ph,createRenderState:dh})};function vS(n,a,s){const r=ph(n,a,s);for(const o in n)if(ve(n[o])||ve(a[o])){const c=Ss.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[c]=n[o]}return r}const JC={useVisualState:gS({scrapeMotionValuesFromProps:vS,createRenderState:pS})};function WC(n,a){return function(r,{forwardMotionProps:o}={forwardMotionProps:!1}){const d={...hh(r)?JC:$C,preloadedFeatures:n,useRender:KC(o),createVisualElement:a,Component:r};return UC(d)}}function Yr(n,a,s){const r=n.getProps();return mh(r,a,s!==void 0?s:r.custom,n)}const yd=n=>Array.isArray(n);function IC(n,a,s){n.hasValue(a)?n.getValue(a).set(s):n.addValue(a,ms(s))}function t2(n){return yd(n)?n[n.length-1]||0:n}function e2(n,a){const s=Yr(n,a);let{transitionEnd:r={},transition:o={},...c}=s||{};c={...c,...r};for(const d in c){const h=t2(c[d]);IC(n,d,h)}}function n2(n){return!!(ve(n)&&n.add)}function gd(n,a){const s=n.getValue("willChange");if(n2(s))return s.add(a);if(!s&&Kn.WillChange){const r=new Kn.WillChange("auto");n.addValue("willChange",r),r.add(a)}}function bS(n){return n.props[uS]}const a2=n=>n!==null;function i2(n,{repeat:a,repeatType:s="loop"},r){const o=n.filter(a2),c=a&&s!=="loop"&&a%2===1?0:o.length-1;return o[c]}const s2={type:"spring",stiffness:500,damping:25,restSpeed:10},r2=n=>({type:"spring",stiffness:550,damping:n===0?2*Math.sqrt(550):30,restSpeed:10}),l2={type:"keyframes",duration:.8},o2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},u2=(n,{keyframes:a})=>a.length>2?l2:xs.has(n)?n.startsWith("scale")?r2(a[1]):s2:o2;function c2({when:n,delay:a,delayChildren:s,staggerChildren:r,staggerDirection:o,repeat:c,repeatType:d,repeatDelay:h,from:p,elapsed:m,...y}){return!!Object.keys(y).length}const yh=(n,a,s,r={},o,c)=>d=>{const h=ih(r,n)||{},p=h.delay||r.delay||0;let{elapsed:m=0}=r;m=m-xn(p);const y={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:a.getVelocity(),...h,delay:-m,onUpdate:S=>{a.set(S),h.onUpdate&&h.onUpdate(S)},onComplete:()=>{d(),h.onComplete&&h.onComplete()},name:n,motionValue:a,element:c?void 0:o};c2(h)||Object.assign(y,u2(n,y)),y.duration&&(y.duration=xn(y.duration)),y.repeatDelay&&(y.repeatDelay=xn(y.repeatDelay)),y.from!==void 0&&(y.keyframes[0]=y.from);let v=!1;if((y.type===!1||y.duration===0&&!y.repeatDelay)&&(y.duration=0,y.delay===0&&(v=!0)),(Kn.instantAnimations||Kn.skipAnimations)&&(v=!0,y.duration=0,y.delay=0),y.allowFlatten=!h.type&&!h.ease,v&&!c&&a.get()!==void 0){const S=i2(y.keyframes,h);if(S!==void 0){Pt.update(()=>{y.onUpdate(S),y.onComplete()});return}}return h.isSync?new nh(y):new KM(y)};function f2({protectedKeys:n,needsAnimating:a},s){const r=n.hasOwnProperty(s)&&a[s]!==!0;return a[s]=!1,r}function SS(n,a,{delay:s=0,transitionOverride:r,type:o}={}){let{transition:c=n.getDefaultTransition(),transitionEnd:d,...h}=a;r&&(c=r);const p=[],m=o&&n.animationState&&n.animationState.getState()[o];for(const y in h){const v=n.getValue(y,n.latestValues[y]??null),S=h[y];if(S===void 0||m&&f2(m,y))continue;const E={delay:s,...ih(c||{},y)},x=v.get();if(x!==void 0&&!v.isAnimating&&!Array.isArray(S)&&S===x&&!E.velocity)continue;let T=!1;if(window.MotionHandoffAnimation){const R=bS(n);if(R){const V=window.MotionHandoffAnimation(R,y,Pt);V!==null&&(E.startTime=V,T=!0)}}gd(n,y),v.start(yh(y,v,S,n.shouldReduceMotion&&Zb.has(y)?{type:!1}:E,n,T));const w=v.animation;w&&p.push(w)}return d&&Promise.all(p).then(()=>{Pt.update(()=>{d&&e2(n,d)})}),p}function vd(n,a,s={}){var p;const r=Yr(n,a,s.type==="exit"?(p=n.presenceContext)==null?void 0:p.custom:void 0);let{transition:o=n.getDefaultTransition()||{}}=r||{};s.transitionOverride&&(o=s.transitionOverride);const c=r?()=>Promise.all(SS(n,r,s)):()=>Promise.resolve(),d=n.variantChildren&&n.variantChildren.size?(m=0)=>{const{delayChildren:y=0,staggerChildren:v,staggerDirection:S}=o;return d2(n,a,y+m,v,S,s)}:()=>Promise.resolve(),{when:h}=o;if(h){const[m,y]=h==="beforeChildren"?[c,d]:[d,c];return m().then(()=>y())}else return Promise.all([c(),d(s.delay)])}function d2(n,a,s=0,r=0,o=1,c){const d=[],h=(n.variantChildren.size-1)*r,p=o===1?(m=0)=>m*r:(m=0)=>h-m*r;return Array.from(n.variantChildren).sort(h2).forEach((m,y)=>{m.notify("AnimationStart",a),d.push(vd(m,a,{...c,delay:s+p(y)}).then(()=>m.notify("AnimationComplete",a)))}),Promise.all(d)}function h2(n,a){return n.sortNodePosition(a)}function m2(n,a,s={}){n.notify("AnimationStart",a);let r;if(Array.isArray(a)){const o=a.map(c=>vd(n,c,s));r=Promise.all(o)}else if(typeof a=="string")r=vd(n,a,s);else{const o=typeof a=="function"?Yr(n,a,s.custom):a;r=Promise.all(SS(n,o,s))}return r.then(()=>{n.notify("AnimationComplete",a)})}function xS(n,a){if(!Array.isArray(a))return!1;const s=a.length;if(s!==n.length)return!1;for(let r=0;r<s;r++)if(a[r]!==n[r])return!1;return!0}const p2=uh.length;function TS(n){if(!n)return;if(!n.isControllingVariants){const s=n.parent?TS(n.parent)||{}:{};return n.props.initial!==void 0&&(s.initial=n.props.initial),s}const a={};for(let s=0;s<p2;s++){const r=uh[s],o=n.props[r];(Pr(o)||o===!1)&&(a[r]=o)}return a}const y2=[...oh].reverse(),g2=oh.length;function v2(n){return a=>Promise.all(a.map(({animation:s,options:r})=>m2(n,s,r)))}function b2(n){let a=v2(n),s=gv(),r=!0;const o=p=>(m,y)=>{var S;const v=Yr(n,y,p==="exit"?(S=n.presenceContext)==null?void 0:S.custom:void 0);if(v){const{transition:E,transitionEnd:x,...T}=v;m={...m,...T,...x}}return m};function c(p){a=p(n)}function d(p){const{props:m}=n,y=TS(n.parent)||{},v=[],S=new Set;let E={},x=1/0;for(let w=0;w<g2;w++){const R=y2[w],V=s[R],U=m[R]!==void 0?m[R]:y[R],K=Pr(U),k=R===p?V.isActive:null;k===!1&&(x=w);let $=U===y[R]&&U!==m[R]&&K;if($&&r&&n.manuallyAnimateOnMount&&($=!1),V.protectedKeys={...E},!V.isActive&&k===null||!U&&!V.prevProp||iu(U)||typeof U=="boolean")continue;const at=S2(V.prevProp,U);let Y=at||R===p&&V.isActive&&!$&&K||w>x&&K,J=!1;const mt=Array.isArray(U)?U:[U];let Vt=mt.reduce(o(R),{});k===!1&&(Vt={});const{prevResolvedValues:Bt={}}=V,Pe={...Bt,...Vt},Ae=F=>{Y=!0,S.has(F)&&(J=!0,S.delete(F)),V.needsAnimating[F]=!0;const X=n.getValue(F);X&&(X.liveStyle=!1)};for(const F in Pe){const X=Vt[F],xt=Bt[F];if(E.hasOwnProperty(F))continue;let M=!1;yd(X)&&yd(xt)?M=!xS(X,xt):M=X!==xt,M?X!=null?Ae(F):S.add(F):X!==void 0&&S.has(F)?Ae(F):V.protectedKeys[F]=!0}V.prevProp=U,V.prevResolvedValues=Vt,V.isActive&&(E={...E,...Vt}),r&&n.blockInitialAnimation&&(Y=!1),Y&&(!($&&at)||J)&&v.push(...mt.map(F=>({animation:F,options:{type:R}})))}if(S.size){const w={};if(typeof m.initial!="boolean"){const R=Yr(n,Array.isArray(m.initial)?m.initial[0]:m.initial);R&&R.transition&&(w.transition=R.transition)}S.forEach(R=>{const V=n.getBaseTarget(R),U=n.getValue(R);U&&(U.liveStyle=!0),w[R]=V??null}),v.push({animation:w})}let T=!!v.length;return r&&(m.initial===!1||m.initial===m.animate)&&!n.manuallyAnimateOnMount&&(T=!1),r=!1,T?a(v):Promise.resolve()}function h(p,m){var v;if(s[p].isActive===m)return Promise.resolve();(v=n.variantChildren)==null||v.forEach(S=>{var E;return(E=S.animationState)==null?void 0:E.setActive(p,m)}),s[p].isActive=m;const y=d(p);for(const S in s)s[S].protectedKeys={};return y}return{animateChanges:d,setActive:h,setAnimateFunction:c,getState:()=>s,reset:()=>{s=gv(),r=!0}}}function S2(n,a){return typeof a=="string"?a!==n:Array.isArray(a)?!xS(a,n):!1}function Ja(n=!1){return{isActive:n,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function gv(){return{animate:Ja(!0),whileInView:Ja(),whileHover:Ja(),whileTap:Ja(),whileDrag:Ja(),whileFocus:Ja(),exit:Ja()}}class Na{constructor(a){this.isMounted=!1,this.node=a}update(){}}class x2 extends Na{constructor(a){super(a),a.animationState||(a.animationState=b2(a))}updateAnimationControlsSubscription(){const{animate:a}=this.node.getProps();iu(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:a}=this.node.getProps(),{animate:s}=this.node.prevProps||{};a!==s&&this.updateAnimationControlsSubscription()}unmount(){var a;this.node.animationState.reset(),(a=this.unmountControls)==null||a.call(this)}}let T2=0;class A2 extends Na{constructor(){super(...arguments),this.id=T2++}update(){if(!this.node.presenceContext)return;const{isPresent:a,onExitComplete:s}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===r)return;const o=this.node.animationState.setActive("exit",!a);s&&!a&&o.then(()=>{s(this.id)})}mount(){const{register:a,onExitComplete:s}=this.node.presenceContext||{};s&&s(this.id),a&&(this.unmount=a(this.id))}unmount(){}}const E2={animation:{Feature:x2},exit:{Feature:A2}};function Xr(n,a,s,r={passive:!0}){return n.addEventListener(a,s,r),()=>n.removeEventListener(a,s)}function nl(n){return{point:{x:n.pageX,y:n.pageY}}}const w2=n=>a=>lh(a)&&n(a,nl(a));function Nr(n,a,s,r){return Xr(n,a,w2(s),r)}function AS({top:n,left:a,right:s,bottom:r}){return{x:{min:a,max:s},y:{min:n,max:r}}}function R2({x:n,y:a}){return{top:a.min,right:n.max,bottom:a.max,left:n.min}}function M2(n,a){if(!a)return n;const s=a({x:n.left,y:n.top}),r=a({x:n.right,y:n.bottom});return{top:s.y,left:s.x,bottom:r.y,right:r.x}}const ES=1e-4,C2=1-ES,O2=1+ES,wS=.01,D2=0-wS,U2=0+wS;function Te(n){return n.max-n.min}function N2(n,a,s){return Math.abs(n-a)<=s}function vv(n,a,s,r=.5){n.origin=r,n.originPoint=qt(a.min,a.max,n.origin),n.scale=Te(s)/Te(a),n.translate=qt(s.min,s.max,n.origin)-n.originPoint,(n.scale>=C2&&n.scale<=O2||isNaN(n.scale))&&(n.scale=1),(n.translate>=D2&&n.translate<=U2||isNaN(n.translate))&&(n.translate=0)}function zr(n,a,s,r){vv(n.x,a.x,s.x,r?r.originX:void 0),vv(n.y,a.y,s.y,r?r.originY:void 0)}function bv(n,a,s){n.min=s.min+a.min,n.max=n.min+Te(a)}function z2(n,a,s){bv(n.x,a.x,s.x),bv(n.y,a.y,s.y)}function Sv(n,a,s){n.min=a.min-s.min,n.max=n.min+Te(a)}function _r(n,a,s){Sv(n.x,a.x,s.x),Sv(n.y,a.y,s.y)}const xv=()=>({translate:0,scale:1,origin:0,originPoint:0}),as=()=>({x:xv(),y:xv()}),Tv=()=>({min:0,max:0}),Zt=()=>({x:Tv(),y:Tv()});function Ie(n){return[n("x"),n("y")]}function kf(n){return n===void 0||n===1}function bd({scale:n,scaleX:a,scaleY:s}){return!kf(n)||!kf(a)||!kf(s)}function Ia(n){return bd(n)||RS(n)||n.z||n.rotate||n.rotateX||n.rotateY||n.skewX||n.skewY}function RS(n){return Av(n.x)||Av(n.y)}function Av(n){return n&&n!=="0%"}function Yo(n,a,s){const r=n-s,o=a*r;return s+o}function Ev(n,a,s,r,o){return o!==void 0&&(n=Yo(n,o,r)),Yo(n,s,r)+a}function Sd(n,a=0,s=1,r,o){n.min=Ev(n.min,a,s,r,o),n.max=Ev(n.max,a,s,r,o)}function MS(n,{x:a,y:s}){Sd(n.x,a.translate,a.scale,a.originPoint),Sd(n.y,s.translate,s.scale,s.originPoint)}const wv=.999999999999,Rv=1.0000000000001;function _2(n,a,s,r=!1){const o=s.length;if(!o)return;a.x=a.y=1;let c,d;for(let h=0;h<o;h++){c=s[h],d=c.projectionDelta;const{visualElement:p}=c.options;p&&p.props.style&&p.props.style.display==="contents"||(r&&c.options.layoutScroll&&c.scroll&&c!==c.root&&ss(n,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),d&&(a.x*=d.x.scale,a.y*=d.y.scale,MS(n,d)),r&&Ia(c.latestValues)&&ss(n,c.latestValues))}a.x<Rv&&a.x>wv&&(a.x=1),a.y<Rv&&a.y>wv&&(a.y=1)}function is(n,a){n.min=n.min+a,n.max=n.max+a}function Mv(n,a,s,r,o=.5){const c=qt(n.min,n.max,o);Sd(n,a,s,c,r)}function ss(n,a){Mv(n.x,a.x,a.scaleX,a.scale,a.originX),Mv(n.y,a.y,a.scaleY,a.scale,a.originY)}function CS(n,a){return AS(M2(n.getBoundingClientRect(),a))}function L2(n,a,s){const r=CS(n,s),{scroll:o}=a;return o&&(is(r.x,o.offset.x),is(r.y,o.offset.y)),r}const OS=({current:n})=>n?n.ownerDocument.defaultView:null,Cv=(n,a)=>Math.abs(n-a);function V2(n,a){const s=Cv(n.x,a.x),r=Cv(n.y,a.y);return Math.sqrt(s**2+r**2)}class DS{constructor(a,s,{transformPagePoint:r,contextWindow:o,dragSnapToOrigin:c=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=qf(this.lastMoveEventInfo,this.history),S=this.startEvent!==null,E=V2(v.offset,{x:0,y:0})>=3;if(!S&&!E)return;const{point:x}=v,{timestamp:T}=fe;this.history.push({...x,timestamp:T});const{onStart:w,onMove:R}=this.handlers;S||(w&&w(this.lastMoveEvent,v),this.startEvent=this.lastMoveEvent),R&&R(this.lastMoveEvent,v)},this.handlePointerMove=(v,S)=>{this.lastMoveEvent=v,this.lastMoveEventInfo=Hf(S,this.transformPagePoint),Pt.update(this.updatePoint,!0)},this.handlePointerUp=(v,S)=>{this.end();const{onEnd:E,onSessionEnd:x,resumeAnimation:T}=this.handlers;if(this.dragSnapToOrigin&&T&&T(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const w=qf(v.type==="pointercancel"?this.lastMoveEventInfo:Hf(S,this.transformPagePoint),this.history);this.startEvent&&E&&E(v,w),x&&x(v,w)},!lh(a))return;this.dragSnapToOrigin=c,this.handlers=s,this.transformPagePoint=r,this.contextWindow=o||window;const d=nl(a),h=Hf(d,this.transformPagePoint),{point:p}=h,{timestamp:m}=fe;this.history=[{...p,timestamp:m}];const{onSessionStart:y}=s;y&&y(a,qf(h,this.history)),this.removeListeners=Ir(Nr(this.contextWindow,"pointermove",this.handlePointerMove),Nr(this.contextWindow,"pointerup",this.handlePointerUp),Nr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),Da(this.updatePoint)}}function Hf(n,a){return a?{point:a(n.point)}:n}function Ov(n,a){return{x:n.x-a.x,y:n.y-a.y}}function qf({point:n},a){return{point:n,delta:Ov(n,US(a)),offset:Ov(n,B2(a)),velocity:j2(a,.1)}}function B2(n){return n[0]}function US(n){return n[n.length-1]}function j2(n,a){if(n.length<2)return{x:0,y:0};let s=n.length-1,r=null;const o=US(n);for(;s>=0&&(r=n[s],!(o.timestamp-r.timestamp>xn(a)));)s--;if(!r)return{x:0,y:0};const c=Tn(o.timestamp-r.timestamp);if(c===0)return{x:0,y:0};const d={x:(o.x-r.x)/c,y:(o.y-r.y)/c};return d.x===1/0&&(d.x=0),d.y===1/0&&(d.y=0),d}function k2(n,{min:a,max:s},r){return a!==void 0&&n<a?n=r?qt(a,n,r.min):Math.max(n,a):s!==void 0&&n>s&&(n=r?qt(s,n,r.max):Math.min(n,s)),n}function Dv(n,a,s){return{min:a!==void 0?n.min+a:void 0,max:s!==void 0?n.max+s-(n.max-n.min):void 0}}function H2(n,{top:a,left:s,bottom:r,right:o}){return{x:Dv(n.x,s,o),y:Dv(n.y,a,r)}}function Uv(n,a){let s=a.min-n.min,r=a.max-n.max;return a.max-a.min<n.max-n.min&&([s,r]=[r,s]),{min:s,max:r}}function q2(n,a){return{x:Uv(n.x,a.x),y:Uv(n.y,a.y)}}function P2(n,a){let s=.5;const r=Te(n),o=Te(a);return o>r?s=kr(a.min,a.max-r,n.min):r>o&&(s=kr(n.min,n.max-o,a.min)),Qn(0,1,s)}function G2(n,a){const s={};return a.min!==void 0&&(s.min=a.min-n.min),a.max!==void 0&&(s.max=a.max-n.min),s}const xd=.35;function Y2(n=xd){return n===!1?n=0:n===!0&&(n=xd),{x:Nv(n,"left","right"),y:Nv(n,"top","bottom")}}function Nv(n,a,s){return{min:zv(n,a),max:zv(n,s)}}function zv(n,a){return typeof n=="number"?n:n[a]||0}const X2=new WeakMap;class Q2{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Zt(),this.visualElement=a}start(a,{snapToCursor:s=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const o=y=>{const{dragSnapToOrigin:v}=this.getProps();v?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(nl(y).point)},c=(y,v)=>{const{drag:S,dragPropagation:E,onDragStart:x}=this.getProps();if(S&&!E&&(this.openDragLock&&this.openDragLock(),this.openDragLock=uC(S),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ie(w=>{let R=this.getAxisMotionValue(w).get()||0;if(An.test(R)){const{projection:V}=this.visualElement;if(V&&V.layout){const U=V.layout.layoutBox[w];U&&(R=Te(U)*(parseFloat(R)/100))}}this.originPoint[w]=R}),x&&Pt.postRender(()=>x(y,v)),gd(this.visualElement,"transform");const{animationState:T}=this.visualElement;T&&T.setActive("whileDrag",!0)},d=(y,v)=>{const{dragPropagation:S,dragDirectionLock:E,onDirectionLock:x,onDrag:T}=this.getProps();if(!S&&!this.openDragLock)return;const{offset:w}=v;if(E&&this.currentDirection===null){this.currentDirection=K2(w),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",v.point,w),this.updateAxis("y",v.point,w),this.visualElement.render(),T&&T(y,v)},h=(y,v)=>this.stop(y,v),p=()=>Ie(y=>{var v;return this.getAnimationState(y)==="paused"&&((v=this.getAxisMotionValue(y).animation)==null?void 0:v.play())}),{dragSnapToOrigin:m}=this.getProps();this.panSession=new DS(a,{onSessionStart:o,onStart:c,onMove:d,onSessionEnd:h,resumeAnimation:p},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:m,contextWindow:OS(this.visualElement)})}stop(a,s){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:o}=s;this.startAnimation(o);const{onDragEnd:c}=this.getProps();c&&Pt.postRender(()=>c(a,s))}cancel(){this.isDragging=!1;const{projection:a,animationState:s}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(a,s,r){const{drag:o}=this.getProps();if(!r||!Eo(a,o,this.currentDirection))return;const c=this.getAxisMotionValue(a);let d=this.originPoint[a]+r[a];this.constraints&&this.constraints[a]&&(d=k2(d,this.constraints[a],this.elastic[a])),c.set(d)}resolveConstraints(){var c;const{dragConstraints:a,dragElastic:s}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(c=this.visualElement.projection)==null?void 0:c.layout,o=this.constraints;a&&ns(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&r?this.constraints=H2(r.layoutBox,a):this.constraints=!1,this.elastic=Y2(s),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Ie(d=>{this.constraints!==!1&&this.getAxisMotionValue(d)&&(this.constraints[d]=G2(r.layoutBox[d],this.constraints[d]))})}resolveRefConstraints(){const{dragConstraints:a,onMeasureDragConstraints:s}=this.getProps();if(!a||!ns(a))return!1;const r=a.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const c=L2(r,o.root,this.visualElement.getTransformPagePoint());let d=q2(o.layout.layoutBox,c);if(s){const h=s(R2(d));this.hasMutatedConstraints=!!h,h&&(d=AS(h))}return d}startAnimation(a){const{drag:s,dragMomentum:r,dragElastic:o,dragTransition:c,dragSnapToOrigin:d,onDragTransitionEnd:h}=this.getProps(),p=this.constraints||{},m=Ie(y=>{if(!Eo(y,s,this.currentDirection))return;let v=p&&p[y]||{};d&&(v={min:0,max:0});const S=o?200:1e6,E=o?40:1e7,x={type:"inertia",velocity:r?a[y]:0,bounceStiffness:S,bounceDamping:E,timeConstant:750,restDelta:1,restSpeed:10,...c,...v};return this.startAxisValueAnimation(y,x)});return Promise.all(m).then(h)}startAxisValueAnimation(a,s){const r=this.getAxisMotionValue(a);return gd(this.visualElement,a),r.start(yh(a,r,0,s,this.visualElement,!1))}stopAnimation(){Ie(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){Ie(a=>{var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.pause()})}getAnimationState(a){var s;return(s=this.getAxisMotionValue(a).animation)==null?void 0:s.state}getAxisMotionValue(a){const s=`_drag${a.toUpperCase()}`,r=this.visualElement.getProps(),o=r[s];return o||this.visualElement.getValue(a,(r.initial?r.initial[a]:void 0)||0)}snapToCursor(a){Ie(s=>{const{drag:r}=this.getProps();if(!Eo(s,r,this.currentDirection))return;const{projection:o}=this.visualElement,c=this.getAxisMotionValue(s);if(o&&o.layout){const{min:d,max:h}=o.layout.layoutBox[s];c.set(a[s]-qt(d,h,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:a,dragConstraints:s}=this.getProps(),{projection:r}=this.visualElement;if(!ns(s)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};Ie(d=>{const h=this.getAxisMotionValue(d);if(h&&this.constraints!==!1){const p=h.get();o[d]=P2({min:p,max:p},this.constraints[d])}});const{transformTemplate:c}=this.visualElement.getProps();this.visualElement.current.style.transform=c?c({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ie(d=>{if(!Eo(d,a,null))return;const h=this.getAxisMotionValue(d),{min:p,max:m}=this.constraints[d];h.set(qt(p,m,o[d]))})}addListeners(){if(!this.visualElement.current)return;X2.set(this.visualElement,this);const a=this.visualElement.current,s=Nr(a,"pointerdown",p=>{const{drag:m,dragListener:y=!0}=this.getProps();m&&y&&this.start(p)}),r=()=>{const{dragConstraints:p}=this.getProps();ns(p)&&p.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,c=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),Pt.read(r);const d=Xr(window,"resize",()=>this.scalePositionWithinConstraints()),h=o.addEventListener("didUpdate",({delta:p,hasLayoutChanged:m})=>{this.isDragging&&m&&(Ie(y=>{const v=this.getAxisMotionValue(y);v&&(this.originPoint[y]+=p[y].translate,v.set(v.get()+p[y].translate))}),this.visualElement.render())});return()=>{d(),s(),c(),h&&h()}}getProps(){const a=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:c=!1,dragElastic:d=xd,dragMomentum:h=!0}=a;return{...a,drag:s,dragDirectionLock:r,dragPropagation:o,dragConstraints:c,dragElastic:d,dragMomentum:h}}}function Eo(n,a,s){return(a===!0||a===n)&&(s===null||s===n)}function K2(n,a=10){let s=null;return Math.abs(n.y)>a?s="y":Math.abs(n.x)>a&&(s="x"),s}class F2 extends Na{constructor(a){super(a),this.removeGroupControls=en,this.removeListeners=en,this.controls=new Q2(a)}mount(){const{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||en}unmount(){this.removeGroupControls(),this.removeListeners()}}const _v=n=>(a,s)=>{n&&Pt.postRender(()=>n(a,s))};class Z2 extends Na{constructor(){super(...arguments),this.removePointerDownListener=en}onPointerDown(a){this.session=new DS(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:OS(this.node)})}createPanHandlers(){const{onPanSessionStart:a,onPanStart:s,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:_v(a),onStart:_v(s),onMove:r,onEnd:(c,d)=>{delete this.session,o&&Pt.postRender(()=>o(c,d))}}}mount(){this.removePointerDownListener=Nr(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Lo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Lv(n,a){return a.max===a.min?0:n/(a.max-a.min)*100}const Mr={correct:(n,a)=>{if(!a.target)return n;if(typeof n=="string")if(ut.test(n))n=parseFloat(n);else return n;const s=Lv(n,a.target.x),r=Lv(n,a.target.y);return`${s}% ${r}%`}},$2={correct:(n,{treeScale:a,projectionDelta:s})=>{const r=n,o=Ua.parse(n);if(o.length>5)return r;const c=Ua.createTransformer(n),d=typeof o[0]!="number"?1:0,h=s.x.scale*a.x,p=s.y.scale*a.y;o[0+d]/=h,o[1+d]/=p;const m=qt(h,p,.5);return typeof o[2+d]=="number"&&(o[2+d]/=m),typeof o[3+d]=="number"&&(o[3+d]/=m),c(o)}};class J2 extends z.Component{componentDidMount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:r,layoutId:o}=this.props,{projection:c}=a;LC(W2),c&&(s.group&&s.group.add(c),r&&r.register&&o&&r.register(c),c.root.didUpdate(),c.addEventListener("animationComplete",()=>{this.safeToRemove()}),c.setOptions({...c.options,onExitComplete:()=>this.safeToRemove()})),Lo.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){const{layoutDependency:s,visualElement:r,drag:o,isPresent:c}=this.props,{projection:d}=r;return d&&(d.isPresent=c,o||a.layoutDependency!==s||s===void 0||a.isPresent!==c?d.willUpdate():this.safeToRemove(),a.isPresent!==c&&(c?d.promote():d.relegate()||Pt.postRender(()=>{const h=d.getStack();(!h||!h.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),rh.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:a,layoutGroup:s,switchLayoutGroup:r}=this.props,{projection:o}=a;o&&(o.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:a}=this.props;a&&a()}render(){return null}}function NS(n){const[a,s]=vC(),r=z.useContext(mb);return it.jsx(J2,{...n,layoutGroup:r,switchLayoutGroup:z.useContext(cS),isPresent:a,safeToRemove:s})}const W2={borderRadius:{...Mr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Mr,borderTopRightRadius:Mr,borderBottomLeftRadius:Mr,borderBottomRightRadius:Mr,boxShadow:$2};function I2(n,a,s){const r=ve(n)?n:ms(n);return r.start(yh("",r,a,s)),r.animation}const tO=(n,a)=>n.depth-a.depth;class eO{constructor(){this.children=[],this.isDirty=!1}add(a){qd(this.children,a),this.isDirty=!0}remove(a){Pd(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(tO),this.isDirty=!1,this.children.forEach(a)}}function nO(n,a){const s=Oe.now(),r=({timestamp:o})=>{const c=o-s;c>=a&&(Da(r),n(c-a))};return Pt.setup(r,!0),()=>Da(r)}const zS=["TopLeft","TopRight","BottomLeft","BottomRight"],aO=zS.length,Vv=n=>typeof n=="string"?parseFloat(n):n,Bv=n=>typeof n=="number"||ut.test(n);function iO(n,a,s,r,o,c){o?(n.opacity=qt(0,s.opacity??1,sO(r)),n.opacityExit=qt(a.opacity??1,0,rO(r))):c&&(n.opacity=qt(a.opacity??1,s.opacity??1,r));for(let d=0;d<aO;d++){const h=`border${zS[d]}Radius`;let p=jv(a,h),m=jv(s,h);if(p===void 0&&m===void 0)continue;p||(p=0),m||(m=0),p===0||m===0||Bv(p)===Bv(m)?(n[h]=Math.max(qt(Vv(p),Vv(m),r),0),(An.test(m)||An.test(p))&&(n[h]+="%")):n[h]=m}(a.rotate||s.rotate)&&(n.rotate=qt(a.rotate||0,s.rotate||0,r))}function jv(n,a){return n[a]!==void 0?n[a]:n.borderRadius}const sO=_S(0,.5,wb),rO=_S(.5,.95,en);function _S(n,a,s){return r=>r<n?0:r>a?1:s(kr(n,a,r))}function kv(n,a){n.min=a.min,n.max=a.max}function We(n,a){kv(n.x,a.x),kv(n.y,a.y)}function Hv(n,a){n.translate=a.translate,n.scale=a.scale,n.originPoint=a.originPoint,n.origin=a.origin}function qv(n,a,s,r,o){return n-=a,n=Yo(n,1/s,r),o!==void 0&&(n=Yo(n,1/o,r)),n}function lO(n,a=0,s=1,r=.5,o,c=n,d=n){if(An.test(a)&&(a=parseFloat(a),a=qt(d.min,d.max,a/100)-d.min),typeof a!="number")return;let h=qt(c.min,c.max,r);n===c&&(h-=a),n.min=qv(n.min,a,s,h,o),n.max=qv(n.max,a,s,h,o)}function Pv(n,a,[s,r,o],c,d){lO(n,a[s],a[r],a[o],a.scale,c,d)}const oO=["x","scaleX","originX"],uO=["y","scaleY","originY"];function Gv(n,a,s,r){Pv(n.x,a,oO,s?s.x:void 0,r?r.x:void 0),Pv(n.y,a,uO,s?s.y:void 0,r?r.y:void 0)}function Yv(n){return n.translate===0&&n.scale===1}function LS(n){return Yv(n.x)&&Yv(n.y)}function Xv(n,a){return n.min===a.min&&n.max===a.max}function cO(n,a){return Xv(n.x,a.x)&&Xv(n.y,a.y)}function Qv(n,a){return Math.round(n.min)===Math.round(a.min)&&Math.round(n.max)===Math.round(a.max)}function VS(n,a){return Qv(n.x,a.x)&&Qv(n.y,a.y)}function Kv(n){return Te(n.x)/Te(n.y)}function Fv(n,a){return n.translate===a.translate&&n.scale===a.scale&&n.originPoint===a.originPoint}class fO{constructor(){this.members=[]}add(a){qd(this.members,a),a.scheduleRender()}remove(a){if(Pd(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(a){const s=this.members.findIndex(o=>a===o);if(s===0)return!1;let r;for(let o=s;o>=0;o--){const c=this.members[o];if(c.isPresent!==!1){r=c;break}}return r?(this.promote(r),!0):!1}promote(a,s){const r=this.lead;if(a!==r&&(this.prevLead=r,this.lead=a,a.show(),r)){r.instance&&r.scheduleRender(),a.scheduleRender(),a.resumeFrom=r,s&&(a.resumeFrom.preserveOpacity=!0),r.snapshot&&(a.snapshot=r.snapshot,a.snapshot.latestValues=r.animationValues||r.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);const{crossfade:o}=a.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(a=>{const{options:s,resumingFrom:r}=a;s.onExitComplete&&s.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function dO(n,a,s){let r="";const o=n.x.translate/a.x,c=n.y.translate/a.y,d=(s==null?void 0:s.z)||0;if((o||c||d)&&(r=`translate3d(${o}px, ${c}px, ${d}px) `),(a.x!==1||a.y!==1)&&(r+=`scale(${1/a.x}, ${1/a.y}) `),s){const{transformPerspective:m,rotate:y,rotateX:v,rotateY:S,skewX:E,skewY:x}=s;m&&(r=`perspective(${m}px) ${r}`),y&&(r+=`rotate(${y}deg) `),v&&(r+=`rotateX(${v}deg) `),S&&(r+=`rotateY(${S}deg) `),E&&(r+=`skewX(${E}deg) `),x&&(r+=`skewY(${x}deg) `)}const h=n.x.scale*a.x,p=n.y.scale*a.y;return(h!==1||p!==1)&&(r+=`scale(${h}, ${p})`),r||"none"}const Pf=["","X","Y","Z"],hO={visibility:"hidden"},mO=1e3;let pO=0;function Gf(n,a,s,r){const{latestValues:o}=a;o[n]&&(s[n]=o[n],a.setStaticValue(n,0),r&&(r[n]=0))}function BS(n){if(n.hasCheckedOptimisedAppear=!0,n.root===n)return;const{visualElement:a}=n.options;if(!a)return;const s=bS(a);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:o,layoutId:c}=n.options;window.MotionCancelOptimisedAnimation(s,"transform",Pt,!(o||c))}const{parent:r}=n;r&&!r.hasCheckedOptimisedAppear&&BS(r)}function jS({attachResizeListener:n,defaultParent:a,measureScroll:s,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(d={},h=a==null?void 0:a()){this.id=pO++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(vO),this.nodes.forEach(AO),this.nodes.forEach(EO),this.nodes.forEach(bO)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=d,this.root=h?h.root||h:this,this.path=h?[...h.path,h]:[],this.parent=h,this.depth=h?h.depth+1:0;for(let p=0;p<this.path.length;p++)this.path[p].shouldResetTransform=!0;this.root===this&&(this.nodes=new eO)}addEventListener(d,h){return this.eventHandlers.has(d)||this.eventHandlers.set(d,new Xd),this.eventHandlers.get(d).add(h)}notifyListeners(d,...h){const p=this.eventHandlers.get(d);p&&p.notify(...h)}hasListeners(d){return this.eventHandlers.has(d)}mount(d){if(this.instance)return;this.isSVG=iS(d)&&!pC(d),this.instance=d;const{layoutId:h,layout:p,visualElement:m}=this.options;if(m&&!m.current&&m.mount(d),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(p||h)&&(this.isLayoutDirty=!0),n){let y;const v=()=>this.root.updateBlockedByResize=!1;n(d,()=>{this.root.updateBlockedByResize=!0,y&&y(),y=nO(v,250),Lo.hasAnimatedSinceResize&&(Lo.hasAnimatedSinceResize=!1,this.nodes.forEach($v))})}h&&this.root.registerSharedNode(h,this),this.options.animate!==!1&&m&&(h||p)&&this.addEventListener("didUpdate",({delta:y,hasLayoutChanged:v,hasRelativeLayoutChanged:S,layout:E})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||m.getDefaultTransition()||OO,{onLayoutAnimationStart:T,onLayoutAnimationComplete:w}=m.getProps(),R=!this.targetLayout||!VS(this.targetLayout,E),V=!v&&S;if(this.options.layoutRoot||this.resumeFrom||V||v&&(R||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const U={...ih(x,"layout"),onPlay:T,onComplete:w};(m.shouldReduceMotion||this.options.layoutRoot)&&(U.delay=0,U.type=!1),this.startAnimation(U),this.setAnimationOrigin(y,V)}else v||$v(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=E})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const d=this.getStack();d&&d.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Da(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(wO),this.animationId++)}getTransformTemplate(){const{visualElement:d}=this.options;return d&&d.getProps().transformTemplate}willUpdate(d=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&BS(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let y=0;y<this.path.length;y++){const v=this.path[y];v.shouldResetTransform=!0,v.updateScroll("snapshot"),v.options.layoutRoot&&v.willUpdate(!1)}const{layoutId:h,layout:p}=this.options;if(h===void 0&&!p)return;const m=this.getTransformTemplate();this.prevTransformTemplateValue=m?m(this.latestValues,""):void 0,this.updateSnapshot(),d&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Zv);return}this.isUpdating||this.nodes.forEach(xO),this.isUpdating=!1,this.nodes.forEach(TO),this.nodes.forEach(yO),this.nodes.forEach(gO),this.clearAllSnapshots();const h=Oe.now();fe.delta=Qn(0,1e3/60,h-fe.timestamp),fe.timestamp=h,fe.isProcessing=!0,zf.update.process(fe),zf.preRender.process(fe),zf.render.process(fe),fe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rh.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(SO),this.sharedNodes.forEach(RO)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Pt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Pt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Te(this.snapshot.measuredBox.x)&&!Te(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let p=0;p<this.path.length;p++)this.path[p].updateScroll();const d=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Zt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:h}=this.options;h&&h.notify("LayoutMeasure",this.layout.layoutBox,d?d.layoutBox:void 0)}updateScroll(d="measure"){let h=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===d&&(h=!1),h&&this.instance){const p=r(this.instance);this.scroll={animationId:this.root.animationId,phase:d,isRoot:p,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:p}}}resetTransform(){if(!o)return;const d=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,h=this.projectionDelta&&!LS(this.projectionDelta),p=this.getTransformTemplate(),m=p?p(this.latestValues,""):void 0,y=m!==this.prevTransformTemplateValue;d&&this.instance&&(h||Ia(this.latestValues)||y)&&(o(this.instance,m),this.shouldResetTransform=!1,this.scheduleRender())}measure(d=!0){const h=this.measurePageBox();let p=this.removeElementScroll(h);return d&&(p=this.removeTransform(p)),DO(p),{animationId:this.root.animationId,measuredBox:h,layoutBox:p,latestValues:{},source:this.id}}measurePageBox(){var m;const{visualElement:d}=this.options;if(!d)return Zt();const h=d.measureViewportBox();if(!(((m=this.scroll)==null?void 0:m.wasRoot)||this.path.some(UO))){const{scroll:y}=this.root;y&&(is(h.x,y.offset.x),is(h.y,y.offset.y))}return h}removeElementScroll(d){var p;const h=Zt();if(We(h,d),(p=this.scroll)!=null&&p.wasRoot)return h;for(let m=0;m<this.path.length;m++){const y=this.path[m],{scroll:v,options:S}=y;y!==this.root&&v&&S.layoutScroll&&(v.wasRoot&&We(h,d),is(h.x,v.offset.x),is(h.y,v.offset.y))}return h}applyTransform(d,h=!1){const p=Zt();We(p,d);for(let m=0;m<this.path.length;m++){const y=this.path[m];!h&&y.options.layoutScroll&&y.scroll&&y!==y.root&&ss(p,{x:-y.scroll.offset.x,y:-y.scroll.offset.y}),Ia(y.latestValues)&&ss(p,y.latestValues)}return Ia(this.latestValues)&&ss(p,this.latestValues),p}removeTransform(d){const h=Zt();We(h,d);for(let p=0;p<this.path.length;p++){const m=this.path[p];if(!m.instance||!Ia(m.latestValues))continue;bd(m.latestValues)&&m.updateSnapshot();const y=Zt(),v=m.measurePageBox();We(y,v),Gv(h,m.latestValues,m.snapshot?m.snapshot.layoutBox:void 0,y)}return Ia(this.latestValues)&&Gv(h,this.latestValues),h}setTargetDelta(d){this.targetDelta=d,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(d){this.options={...this.options,...d,crossfade:d.crossfade!==void 0?d.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==fe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(d=!1){var S;const h=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=h.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=h.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=h.isSharedProjectionDirty);const p=!!this.resumingFrom||this!==h;if(!(d||p&&this.isSharedProjectionDirty||this.isProjectionDirty||(S=this.parent)!=null&&S.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:y,layoutId:v}=this.options;if(!(!this.layout||!(y||v))){if(this.resolvedRelativeTargetAt=fe.timestamp,!this.targetDelta&&!this.relativeTarget){const E=this.getClosestProjectingParent();E&&E.layout&&this.animationProgress!==1?(this.relativeParent=E,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Zt(),this.relativeTargetOrigin=Zt(),_r(this.relativeTargetOrigin,this.layout.layoutBox,E.layout.layoutBox),We(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Zt(),this.targetWithTransforms=Zt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),z2(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):We(this.target,this.layout.layoutBox),MS(this.target,this.targetDelta)):We(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const E=this.getClosestProjectingParent();E&&!!E.resumingFrom==!!this.resumingFrom&&!E.options.layoutScroll&&E.target&&this.animationProgress!==1?(this.relativeParent=E,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Zt(),this.relativeTargetOrigin=Zt(),_r(this.relativeTargetOrigin,this.target,E.target),We(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||bd(this.parent.latestValues)||RS(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var x;const d=this.getLead(),h=!!this.resumingFrom||this!==d;let p=!0;if((this.isProjectionDirty||(x=this.parent)!=null&&x.isProjectionDirty)&&(p=!1),h&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(p=!1),this.resolvedRelativeTargetAt===fe.timestamp&&(p=!1),p)return;const{layout:m,layoutId:y}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(m||y))return;We(this.layoutCorrected,this.layout.layoutBox);const v=this.treeScale.x,S=this.treeScale.y;_2(this.layoutCorrected,this.treeScale,this.path,h),d.layout&&!d.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(d.target=d.layout.layoutBox,d.targetWithTransforms=Zt());const{target:E}=d;if(!E){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Hv(this.prevProjectionDelta.x,this.projectionDelta.x),Hv(this.prevProjectionDelta.y,this.projectionDelta.y)),zr(this.projectionDelta,this.layoutCorrected,E,this.latestValues),(this.treeScale.x!==v||this.treeScale.y!==S||!Fv(this.projectionDelta.x,this.prevProjectionDelta.x)||!Fv(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",E))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(d=!0){var h;if((h=this.options.visualElement)==null||h.scheduleRender(),d){const p=this.getStack();p&&p.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=as(),this.projectionDelta=as(),this.projectionDeltaWithTransform=as()}setAnimationOrigin(d,h=!1){const p=this.snapshot,m=p?p.latestValues:{},y={...this.latestValues},v=as();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!h;const S=Zt(),E=p?p.source:void 0,x=this.layout?this.layout.source:void 0,T=E!==x,w=this.getStack(),R=!w||w.members.length<=1,V=!!(T&&!R&&this.options.crossfade===!0&&!this.path.some(CO));this.animationProgress=0;let U;this.mixTargetDelta=K=>{const k=K/1e3;Jv(v.x,d.x,k),Jv(v.y,d.y,k),this.setTargetDelta(v),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(_r(S,this.layout.layoutBox,this.relativeParent.layout.layoutBox),MO(this.relativeTarget,this.relativeTargetOrigin,S,k),U&&cO(this.relativeTarget,U)&&(this.isProjectionDirty=!1),U||(U=Zt()),We(U,this.relativeTarget)),T&&(this.animationValues=y,iO(y,m,this.latestValues,k,V,R)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=k},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(d){var h,p,m;this.notifyListeners("animationStart"),(h=this.currentAnimation)==null||h.stop(),(m=(p=this.resumingFrom)==null?void 0:p.currentAnimation)==null||m.stop(),this.pendingAnimation&&(Da(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Pt.update(()=>{Lo.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ms(0)),this.currentAnimation=I2(this.motionValue,[0,1e3],{...d,velocity:0,isSync:!0,onUpdate:y=>{this.mixTargetDelta(y),d.onUpdate&&d.onUpdate(y)},onStop:()=>{},onComplete:()=>{d.onComplete&&d.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const d=this.getStack();d&&d.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(mO),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const d=this.getLead();let{targetWithTransforms:h,target:p,layout:m,latestValues:y}=d;if(!(!h||!p||!m)){if(this!==d&&this.layout&&m&&kS(this.options.animationType,this.layout.layoutBox,m.layoutBox)){p=this.target||Zt();const v=Te(this.layout.layoutBox.x);p.x.min=d.target.x.min,p.x.max=p.x.min+v;const S=Te(this.layout.layoutBox.y);p.y.min=d.target.y.min,p.y.max=p.y.min+S}We(h,p),ss(h,y),zr(this.projectionDeltaWithTransform,this.layoutCorrected,h,y)}}registerSharedNode(d,h){this.sharedNodes.has(d)||this.sharedNodes.set(d,new fO),this.sharedNodes.get(d).add(h);const m=h.options.initialPromotionConfig;h.promote({transition:m?m.transition:void 0,preserveFollowOpacity:m&&m.shouldPreserveFollowOpacity?m.shouldPreserveFollowOpacity(h):void 0})}isLead(){const d=this.getStack();return d?d.lead===this:!0}getLead(){var h;const{layoutId:d}=this.options;return d?((h=this.getStack())==null?void 0:h.lead)||this:this}getPrevLead(){var h;const{layoutId:d}=this.options;return d?(h=this.getStack())==null?void 0:h.prevLead:void 0}getStack(){const{layoutId:d}=this.options;if(d)return this.root.sharedNodes.get(d)}promote({needsReset:d,transition:h,preserveFollowOpacity:p}={}){const m=this.getStack();m&&m.promote(this,p),d&&(this.projectionDelta=void 0,this.needsReset=!0),h&&this.setOptions({transition:h})}relegate(){const d=this.getStack();return d?d.relegate(this):!1}resetSkewAndRotation(){const{visualElement:d}=this.options;if(!d)return;let h=!1;const{latestValues:p}=d;if((p.z||p.rotate||p.rotateX||p.rotateY||p.rotateZ||p.skewX||p.skewY)&&(h=!0),!h)return;const m={};p.z&&Gf("z",d,m,this.animationValues);for(let y=0;y<Pf.length;y++)Gf(`rotate${Pf[y]}`,d,m,this.animationValues),Gf(`skew${Pf[y]}`,d,m,this.animationValues);d.render();for(const y in m)d.setStaticValue(y,m[y]),this.animationValues&&(this.animationValues[y]=m[y]);d.scheduleRender()}getProjectionStyles(d){if(!this.instance||this.isSVG)return;if(!this.isVisible)return hO;const h={visibility:""},p=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,h.opacity="",h.pointerEvents=_o(d==null?void 0:d.pointerEvents)||"",h.transform=p?p(this.latestValues,""):"none",h;const m=this.getLead();if(!this.projectionDelta||!this.layout||!m.target){const E={};return this.options.layoutId&&(E.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,E.pointerEvents=_o(d==null?void 0:d.pointerEvents)||""),this.hasProjected&&!Ia(this.latestValues)&&(E.transform=p?p({},""):"none",this.hasProjected=!1),E}const y=m.animationValues||m.latestValues;this.applyTransformsToTarget(),h.transform=dO(this.projectionDeltaWithTransform,this.treeScale,y),p&&(h.transform=p(y,h.transform));const{x:v,y:S}=this.projectionDelta;h.transformOrigin=`${v.origin*100}% ${S.origin*100}% 0`,m.animationValues?h.opacity=m===this?y.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:y.opacityExit:h.opacity=m===this?y.opacity!==void 0?y.opacity:"":y.opacityExit!==void 0?y.opacityExit:0;for(const E in Gr){if(y[E]===void 0)continue;const{correct:x,applyTo:T,isCSSVariable:w}=Gr[E],R=h.transform==="none"?y[E]:x(y[E],m);if(T){const V=T.length;for(let U=0;U<V;U++)h[T[U]]=R}else w?this.options.visualElement.renderState.vars[E]=R:h[E]=R}return this.options.layoutId&&(h.pointerEvents=m===this?_o(d==null?void 0:d.pointerEvents)||"":"none"),h}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(d=>{var h;return(h=d.currentAnimation)==null?void 0:h.stop()}),this.root.nodes.forEach(Zv),this.root.sharedNodes.clear()}}}function yO(n){n.updateLayout()}function gO(n){var s;const a=((s=n.resumeFrom)==null?void 0:s.snapshot)||n.snapshot;if(n.isLead()&&n.layout&&a&&n.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:o}=n.layout,{animationType:c}=n.options,d=a.source!==n.layout.source;c==="size"?Ie(v=>{const S=d?a.measuredBox[v]:a.layoutBox[v],E=Te(S);S.min=r[v].min,S.max=S.min+E}):kS(c,a.layoutBox,r)&&Ie(v=>{const S=d?a.measuredBox[v]:a.layoutBox[v],E=Te(r[v]);S.max=S.min+E,n.relativeTarget&&!n.currentAnimation&&(n.isProjectionDirty=!0,n.relativeTarget[v].max=n.relativeTarget[v].min+E)});const h=as();zr(h,r,a.layoutBox);const p=as();d?zr(p,n.applyTransform(o,!0),a.measuredBox):zr(p,r,a.layoutBox);const m=!LS(h);let y=!1;if(!n.resumeFrom){const v=n.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:S,layout:E}=v;if(S&&E){const x=Zt();_r(x,a.layoutBox,S.layoutBox);const T=Zt();_r(T,r,E.layoutBox),VS(x,T)||(y=!0),v.options.layoutRoot&&(n.relativeTarget=T,n.relativeTargetOrigin=x,n.relativeParent=v)}}}n.notifyListeners("didUpdate",{layout:r,snapshot:a,delta:p,layoutDelta:h,hasLayoutChanged:m,hasRelativeLayoutChanged:y})}else if(n.isLead()){const{onExitComplete:r}=n.options;r&&r()}n.options.transition=void 0}function vO(n){n.parent&&(n.isProjecting()||(n.isProjectionDirty=n.parent.isProjectionDirty),n.isSharedProjectionDirty||(n.isSharedProjectionDirty=!!(n.isProjectionDirty||n.parent.isProjectionDirty||n.parent.isSharedProjectionDirty)),n.isTransformDirty||(n.isTransformDirty=n.parent.isTransformDirty))}function bO(n){n.isProjectionDirty=n.isSharedProjectionDirty=n.isTransformDirty=!1}function SO(n){n.clearSnapshot()}function Zv(n){n.clearMeasurements()}function xO(n){n.isLayoutDirty=!1}function TO(n){const{visualElement:a}=n.options;a&&a.getProps().onBeforeLayoutMeasure&&a.notify("BeforeLayoutMeasure"),n.resetTransform()}function $v(n){n.finishAnimation(),n.targetDelta=n.relativeTarget=n.target=void 0,n.isProjectionDirty=!0}function AO(n){n.resolveTargetDelta()}function EO(n){n.calcProjection()}function wO(n){n.resetSkewAndRotation()}function RO(n){n.removeLeadSnapshot()}function Jv(n,a,s){n.translate=qt(a.translate,0,s),n.scale=qt(a.scale,1,s),n.origin=a.origin,n.originPoint=a.originPoint}function Wv(n,a,s,r){n.min=qt(a.min,s.min,r),n.max=qt(a.max,s.max,r)}function MO(n,a,s,r){Wv(n.x,a.x,s.x,r),Wv(n.y,a.y,s.y,r)}function CO(n){return n.animationValues&&n.animationValues.opacityExit!==void 0}const OO={duration:.45,ease:[.4,0,.1,1]},Iv=n=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(n),t0=Iv("applewebkit/")&&!Iv("chrome/")?Math.round:en;function e0(n){n.min=t0(n.min),n.max=t0(n.max)}function DO(n){e0(n.x),e0(n.y)}function kS(n,a,s){return n==="position"||n==="preserve-aspect"&&!N2(Kv(a),Kv(s),.2)}function UO(n){var a;return n!==n.root&&((a=n.scroll)==null?void 0:a.wasRoot)}const NO=jS({attachResizeListener:(n,a)=>Xr(n,"resize",a),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Yf={current:void 0},HS=jS({measureScroll:n=>({x:n.scrollLeft,y:n.scrollTop}),defaultParent:()=>{if(!Yf.current){const n=new NO({});n.mount(window),n.setOptions({layoutScroll:!0}),Yf.current=n}return Yf.current},resetTransform:(n,a)=>{n.style.transform=a!==void 0?a:"none"},checkIsScrollRoot:n=>window.getComputedStyle(n).position==="fixed"}),zO={pan:{Feature:Z2},drag:{Feature:F2,ProjectionNode:HS,MeasureLayout:NS}};function n0(n,a,s){const{props:r}=n;n.animationState&&r.whileHover&&n.animationState.setActive("whileHover",s==="Start");const o="onHover"+s,c=r[o];c&&Pt.postRender(()=>c(a,nl(a)))}class _O extends Na{mount(){const{current:a}=this.node;a&&(this.unmount=cC(a,(s,r)=>(n0(this.node,r,"Start"),o=>n0(this.node,o,"End"))))}unmount(){}}class LO extends Na{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch{a=!0}!a||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ir(Xr(this.node.current,"focus",()=>this.onFocus()),Xr(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function a0(n,a,s){const{props:r}=n;if(n.current instanceof HTMLButtonElement&&n.current.disabled)return;n.animationState&&r.whileTap&&n.animationState.setActive("whileTap",s==="Start");const o="onTap"+(s==="End"?"":s),c=r[o];c&&Pt.postRender(()=>c(a,nl(a)))}class VO extends Na{mount(){const{current:a}=this.node;a&&(this.unmount=mC(a,(s,r)=>(a0(this.node,r,"Start"),(o,{success:c})=>a0(this.node,o,c?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Td=new WeakMap,Xf=new WeakMap,BO=n=>{const a=Td.get(n.target);a&&a(n)},jO=n=>{n.forEach(BO)};function kO({root:n,...a}){const s=n||document;Xf.has(s)||Xf.set(s,{});const r=Xf.get(s),o=JSON.stringify(a);return r[o]||(r[o]=new IntersectionObserver(jO,{root:n,...a})),r[o]}function HO(n,a,s){const r=kO(a);return Td.set(n,s),r.observe(n),()=>{Td.delete(n),r.unobserve(n)}}const qO={some:0,all:1};class PO extends Na{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:a={}}=this.node.getProps(),{root:s,margin:r,amount:o="some",once:c}=a,d={root:s?s.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:qO[o]},h=p=>{const{isIntersecting:m}=p;if(this.isInView===m||(this.isInView=m,c&&!m&&this.hasEnteredView))return;m&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",m);const{onViewportEnter:y,onViewportLeave:v}=this.node.getProps(),S=m?y:v;S&&S(p)};return HO(this.node.current,d,h)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:a,prevProps:s}=this.node;["amount","margin","root"].some(GO(a,s))&&this.startObserver()}unmount(){}}function GO({viewport:n={}},{viewport:a={}}={}){return s=>n[s]!==a[s]}const YO={inView:{Feature:PO},tap:{Feature:VO},focus:{Feature:LO},hover:{Feature:_O}},XO={layout:{ProjectionNode:HS,MeasureLayout:NS}},Ad={current:null},qS={current:!1};function QO(){if(qS.current=!0,!!kd)if(window.matchMedia){const n=window.matchMedia("(prefers-reduced-motion)"),a=()=>Ad.current=n.matches;n.addListener(a),a()}else Ad.current=!1}const KO=new WeakMap;function FO(n,a,s){for(const r in a){const o=a[r],c=s[r];if(ve(o))n.addValue(r,o);else if(ve(c))n.addValue(r,ms(o,{owner:n}));else if(c!==o)if(n.hasValue(r)){const d=n.getValue(r);d.liveStyle===!0?d.jump(o):d.hasAnimated||d.set(o)}else{const d=n.getStaticValue(r);n.addValue(r,ms(d!==void 0?d:o,{owner:n}))}}for(const r in s)a[r]===void 0&&n.removeValue(r);return a}const i0=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ZO{scrapeMotionValuesFromProps(a,s,r){return{}}constructor({parent:a,props:s,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:c,visualState:d},h={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ah,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const S=Oe.now();this.renderScheduledAt<S&&(this.renderScheduledAt=S,Pt.render(this.render,!1,!0))};const{latestValues:p,renderState:m}=d;this.latestValues=p,this.baseTarget={...p},this.initialValues=s.initial?{...p}:{},this.renderState=m,this.parent=a,this.props=s,this.presenceContext=r,this.depth=a?a.depth+1:0,this.reducedMotionConfig=o,this.options=h,this.blockInitialAnimation=!!c,this.isControllingVariants=su(s),this.isVariantNode=oS(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);const{willChange:y,...v}=this.scrapeMotionValuesFromProps(s,{},this);for(const S in v){const E=v[S];p[S]!==void 0&&ve(E)&&E.set(p[S],!1)}}mount(a){this.current=a,KO.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,r)=>this.bindToMotionValue(r,s)),qS.current||QO(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ad.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Da(this.notifyUpdate),Da(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const a in this.events)this.events[a].clear();for(const a in this.features){const s=this.features[a];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(a,s){this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();const r=xs.has(a);r&&this.onBindTransform&&this.onBindTransform();const o=s.on("change",h=>{this.latestValues[a]=h,this.props.onUpdate&&Pt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),c=s.on("renderRequest",this.scheduleRender);let d;window.MotionCheckAppearSync&&(d=window.MotionCheckAppearSync(this,a,s)),this.valueSubscriptions.set(a,()=>{o(),c(),d&&d(),s.owner&&s.stop()})}sortNodePosition(a){return!this.current||!this.sortInstanceNodePosition||this.type!==a.type?0:this.sortInstanceNodePosition(this.current,a.current)}updateFeatures(){let a="animation";for(a in ps){const s=ps[a];if(!s)continue;const{isEnabled:r,Feature:o}=s;if(!this.features[a]&&o&&r(this.props)&&(this.features[a]=new o(this)),this.features[a]){const c=this.features[a];c.isMounted?c.update():(c.mount(),c.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Zt()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,s){this.latestValues[a]=s}update(a,s){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let r=0;r<i0.length;r++){const o=i0[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const c="on"+o,d=a[c];d&&(this.propEventSubscriptions[o]=this.on(o,d))}this.prevMotionValues=FO(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(a),()=>s.variantChildren.delete(a)}addValue(a,s){const r=this.values.get(a);s!==r&&(r&&this.removeValue(a),this.bindToMotionValue(a,s),this.values.set(a,s),this.latestValues[a]=s.get())}removeValue(a){this.values.delete(a);const s=this.valueSubscriptions.get(a);s&&(s(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,s){if(this.props.values&&this.props.values[a])return this.props.values[a];let r=this.values.get(a);return r===void 0&&s!==void 0&&(r=ms(s===null?void 0:s,{owner:this}),this.addValue(a,r)),r}readValue(a,s){let r=this.latestValues[a]!==void 0||!this.current?this.latestValues[a]:this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options);return r!=null&&(typeof r=="string"&&(pb(r)||gb(r))?r=parseFloat(r):!gC(r)&&Ua.test(s)&&(r=Ib(a,s)),this.setBaseTarget(a,ve(r)?r.get():r)),ve(r)?r.get():r}setBaseTarget(a,s){this.baseTarget[a]=s}getBaseTarget(a){var c;const{initial:s}=this.props;let r;if(typeof s=="string"||typeof s=="object"){const d=mh(this.props,s,(c=this.presenceContext)==null?void 0:c.custom);d&&(r=d[a])}if(s&&r!==void 0)return r;const o=this.getBaseTargetFromProps(this.props,a);return o!==void 0&&!ve(o)?o:this.initialValues[a]!==void 0&&r===void 0?void 0:this.baseTarget[a]}on(a,s){return this.events[a]||(this.events[a]=new Xd),this.events[a].add(s)}notify(a,...s){this.events[a]&&this.events[a].notify(...s)}}class PS extends ZO{constructor(){super(...arguments),this.KeyframeResolver=sC}sortInstanceNodePosition(a,s){return a.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(a,s){return a.style?a.style[s]:void 0}removeValueFromRenderState(a,{vars:s,style:r}){delete s[a],delete r[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:a}=this.props;ve(a)&&(this.childSubscription=a.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function GS(n,{style:a,vars:s},r,o){Object.assign(n.style,a,o&&o.getProjectionStyles(r));for(const c in s)n.style.setProperty(c,s[c])}function $O(n){return window.getComputedStyle(n)}class JO extends PS{constructor(){super(...arguments),this.type="html",this.renderInstance=GS}readValueFromInstance(a,s){var r;if(xs.has(s))return(r=this.projection)!=null&&r.isProjecting?cd(s):EM(a,s);{const o=$O(a),c=(Fd(s)?o.getPropertyValue(s):o[s])||0;return typeof c=="string"?c.trim():c}}measureInstanceViewportBox(a,{transformPagePoint:s}){return CS(a,s)}build(a,s,r){fh(a,s,r.transformTemplate)}scrapeMotionValuesFromProps(a,s,r){return ph(a,s,r)}}const YS=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function WO(n,a,s,r){GS(n,a,void 0,r);for(const o in a.attrs)n.setAttribute(YS.has(o)?o:ch(o),a.attrs[o])}class IO extends PS{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Zt}getBaseTargetFromProps(a,s){return a[s]}readValueFromInstance(a,s){if(xs.has(s)){const r=Wb(s);return r&&r.default||0}return s=YS.has(s)?s:ch(s),a.getAttribute(s)}scrapeMotionValuesFromProps(a,s,r){return vS(a,s,r)}build(a,s,r){mS(a,s,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(a,s,r,o){WO(a,s,r,o)}mount(a){this.isSVGTag=yS(a.tagName),super.mount(a)}}const tD=(n,a)=>hh(n)?new IO(a):new JO(a,{allowProjection:n!==z.Fragment}),eD=WC({...E2,...YO,...zO,...XO},tD),nD=AC(eD);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aD=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),iD=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,s,r)=>r?r.toUpperCase():s.toLowerCase()),s0=n=>{const a=iD(n);return a.charAt(0).toUpperCase()+a.slice(1)},XS=(...n)=>n.filter((a,s,r)=>!!a&&a.trim()!==""&&r.indexOf(a)===s).join(" ").trim(),sD=n=>{for(const a in n)if(a.startsWith("aria-")||a==="role"||a==="title")return!0};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var rD={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lD=z.forwardRef(({color:n="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:r,className:o="",children:c,iconNode:d,...h},p)=>z.createElement("svg",{ref:p,...rD,width:a,height:a,stroke:n,strokeWidth:r?Number(s)*24/Number(a):s,className:XS("lucide",o),...!c&&!sD(h)&&{"aria-hidden":"true"},...h},[...d.map(([m,y])=>z.createElement(m,y)),...Array.isArray(c)?c:[c]]));/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oD=(n,a)=>{const s=z.forwardRef(({className:r,...o},c)=>z.createElement(lD,{ref:c,iconNode:a,className:XS(`lucide-${aD(s0(n))}`,`lucide-${n}`,r),...o}));return s.displayName=s0(n),s};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uD=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],cD=oD("loader-circle",uD);function QS(n){var a,s,r="";if(typeof n=="string"||typeof n=="number")r+=n;else if(typeof n=="object")if(Array.isArray(n)){var o=n.length;for(a=0;a<o;a++)n[a]&&(s=QS(n[a]))&&(r&&(r+=" "),r+=s)}else for(s in n)n[s]&&(r&&(r+=" "),r+=s);return r}function fD(){for(var n,a,s=0,r="",o=arguments.length;s<o;s++)(n=arguments[s])&&(a=QS(n))&&(r&&(r+=" "),r+=a);return r}const gh="-",dD=n=>{const a=mD(n),{conflictingClassGroups:s,conflictingClassGroupModifiers:r}=n;return{getClassGroupId:d=>{const h=d.split(gh);return h[0]===""&&h.length!==1&&h.shift(),KS(h,a)||hD(d)},getConflictingClassGroupIds:(d,h)=>{const p=s[d]||[];return h&&r[d]?[...p,...r[d]]:p}}},KS=(n,a)=>{var d;if(n.length===0)return a.classGroupId;const s=n[0],r=a.nextPart.get(s),o=r?KS(n.slice(1),r):void 0;if(o)return o;if(a.validators.length===0)return;const c=n.join(gh);return(d=a.validators.find(({validator:h})=>h(c)))==null?void 0:d.classGroupId},r0=/^\[(.+)\]$/,hD=n=>{if(r0.test(n)){const a=r0.exec(n)[1],s=a==null?void 0:a.substring(0,a.indexOf(":"));if(s)return"arbitrary.."+s}},mD=n=>{const{theme:a,classGroups:s}=n,r={nextPart:new Map,validators:[]};for(const o in s)Ed(s[o],r,o,a);return r},Ed=(n,a,s,r)=>{n.forEach(o=>{if(typeof o=="string"){const c=o===""?a:l0(a,o);c.classGroupId=s;return}if(typeof o=="function"){if(pD(o)){Ed(o(r),a,s,r);return}a.validators.push({validator:o,classGroupId:s});return}Object.entries(o).forEach(([c,d])=>{Ed(d,l0(a,c),s,r)})})},l0=(n,a)=>{let s=n;return a.split(gh).forEach(r=>{s.nextPart.has(r)||s.nextPart.set(r,{nextPart:new Map,validators:[]}),s=s.nextPart.get(r)}),s},pD=n=>n.isThemeGetter,yD=n=>{if(n<1)return{get:()=>{},set:()=>{}};let a=0,s=new Map,r=new Map;const o=(c,d)=>{s.set(c,d),a++,a>n&&(a=0,r=s,s=new Map)};return{get(c){let d=s.get(c);if(d!==void 0)return d;if((d=r.get(c))!==void 0)return o(c,d),d},set(c,d){s.has(c)?s.set(c,d):o(c,d)}}},wd="!",Rd=":",gD=Rd.length,vD=n=>{const{prefix:a,experimentalParseClassName:s}=n;let r=o=>{const c=[];let d=0,h=0,p=0,m;for(let x=0;x<o.length;x++){let T=o[x];if(d===0&&h===0){if(T===Rd){c.push(o.slice(p,x)),p=x+gD;continue}if(T==="/"){m=x;continue}}T==="["?d++:T==="]"?d--:T==="("?h++:T===")"&&h--}const y=c.length===0?o:o.substring(p),v=bD(y),S=v!==y,E=m&&m>p?m-p:void 0;return{modifiers:c,hasImportantModifier:S,baseClassName:v,maybePostfixModifierPosition:E}};if(a){const o=a+Rd,c=r;r=d=>d.startsWith(o)?c(d.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:d,maybePostfixModifierPosition:void 0}}if(s){const o=r;r=c=>s({className:c,parseClassName:o})}return r},bD=n=>n.endsWith(wd)?n.substring(0,n.length-1):n.startsWith(wd)?n.substring(1):n,SD=n=>{const a=Object.fromEntries(n.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const o=[];let c=[];return r.forEach(d=>{d[0]==="["||a[d]?(o.push(...c.sort(),d),c=[]):c.push(d)}),o.push(...c.sort()),o}},xD=n=>({cache:yD(n.cacheSize),parseClassName:vD(n),sortModifiers:SD(n),...dD(n)}),TD=/\s+/,AD=(n,a)=>{const{parseClassName:s,getClassGroupId:r,getConflictingClassGroupIds:o,sortModifiers:c}=a,d=[],h=n.trim().split(TD);let p="";for(let m=h.length-1;m>=0;m-=1){const y=h[m],{isExternal:v,modifiers:S,hasImportantModifier:E,baseClassName:x,maybePostfixModifierPosition:T}=s(y);if(v){p=y+(p.length>0?" "+p:p);continue}let w=!!T,R=r(w?x.substring(0,T):x);if(!R){if(!w){p=y+(p.length>0?" "+p:p);continue}if(R=r(x),!R){p=y+(p.length>0?" "+p:p);continue}w=!1}const V=c(S).join(":"),U=E?V+wd:V,K=U+R;if(d.includes(K))continue;d.push(K);const k=o(R,w);for(let $=0;$<k.length;++$){const at=k[$];d.push(U+at)}p=y+(p.length>0?" "+p:p)}return p};function ED(){let n=0,a,s,r="";for(;n<arguments.length;)(a=arguments[n++])&&(s=FS(a))&&(r&&(r+=" "),r+=s);return r}const FS=n=>{if(typeof n=="string")return n;let a,s="";for(let r=0;r<n.length;r++)n[r]&&(a=FS(n[r]))&&(s&&(s+=" "),s+=a);return s};function wD(n,...a){let s,r,o,c=d;function d(p){const m=a.reduce((y,v)=>v(y),n());return s=xD(m),r=s.cache.get,o=s.cache.set,c=h,h(p)}function h(p){const m=r(p);if(m)return m;const y=AD(p,s);return o(p,y),y}return function(){return c(ED.apply(null,arguments))}}const ne=n=>{const a=s=>s[n]||[];return a.isThemeGetter=!0,a},ZS=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,$S=/^\((?:(\w[\w-]*):)?(.+)\)$/i,RD=/^\d+\/\d+$/,MD=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,CD=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,OD=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,DD=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,UD=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ii=n=>RD.test(n),yt=n=>!!n&&!Number.isNaN(Number(n)),Ta=n=>!!n&&Number.isInteger(Number(n)),Qf=n=>n.endsWith("%")&&yt(n.slice(0,-1)),qn=n=>MD.test(n),ND=()=>!0,zD=n=>CD.test(n)&&!OD.test(n),JS=()=>!1,_D=n=>DD.test(n),LD=n=>UD.test(n),VD=n=>!et(n)&&!nt(n),BD=n=>Ts(n,t1,JS),et=n=>ZS.test(n),Wa=n=>Ts(n,e1,zD),Kf=n=>Ts(n,PD,yt),o0=n=>Ts(n,WS,JS),jD=n=>Ts(n,IS,LD),wo=n=>Ts(n,n1,_D),nt=n=>$S.test(n),Cr=n=>As(n,e1),kD=n=>As(n,GD),u0=n=>As(n,WS),HD=n=>As(n,t1),qD=n=>As(n,IS),Ro=n=>As(n,n1,!0),Ts=(n,a,s)=>{const r=ZS.exec(n);return r?r[1]?a(r[1]):s(r[2]):!1},As=(n,a,s=!1)=>{const r=$S.exec(n);return r?r[1]?a(r[1]):s:!1},WS=n=>n==="position"||n==="percentage",IS=n=>n==="image"||n==="url",t1=n=>n==="length"||n==="size"||n==="bg-size",e1=n=>n==="length",PD=n=>n==="number",GD=n=>n==="family-name",n1=n=>n==="shadow",YD=()=>{const n=ne("color"),a=ne("font"),s=ne("text"),r=ne("font-weight"),o=ne("tracking"),c=ne("leading"),d=ne("breakpoint"),h=ne("container"),p=ne("spacing"),m=ne("radius"),y=ne("shadow"),v=ne("inset-shadow"),S=ne("text-shadow"),E=ne("drop-shadow"),x=ne("blur"),T=ne("perspective"),w=ne("aspect"),R=ne("ease"),V=ne("animate"),U=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...K(),nt,et],$=()=>["auto","hidden","clip","visible","scroll"],at=()=>["auto","contain","none"],Y=()=>[nt,et,p],J=()=>[Ii,"full","auto",...Y()],mt=()=>[Ta,"none","subgrid",nt,et],Vt=()=>["auto",{span:["full",Ta,nt,et]},Ta,nt,et],Bt=()=>[Ta,"auto",nt,et],Pe=()=>["auto","min","max","fr",nt,et],Ae=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],jt=()=>["start","end","center","stretch","center-safe","end-safe"],j=()=>["auto",...Y()],F=()=>[Ii,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Y()],X=()=>[n,nt,et],xt=()=>[...K(),u0,o0,{position:[nt,et]}],M=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Q=()=>["auto","cover","contain",HD,BD,{size:[nt,et]}],W=()=>[Qf,Cr,Wa],Z=()=>["","none","full",m,nt,et],I=()=>["",yt,Cr,Wa],vt=()=>["solid","dashed","dotted","double"],ct=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],At=()=>[yt,Qf,u0,o0],Nt=()=>["","none",x,nt,et],Ne=()=>["none",yt,nt,et],Zn=()=>["none",yt,nt,et],$n=()=>[yt,nt,et],Jn=()=>[Ii,"full",...Y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[qn],breakpoint:[qn],color:[ND],container:[qn],"drop-shadow":[qn],ease:["in","out","in-out"],font:[VD],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[qn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[qn],shadow:[qn],spacing:["px",yt],text:[qn],"text-shadow":[qn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ii,et,nt,w]}],container:["container"],columns:[{columns:[yt,et,nt,h]}],"break-after":[{"break-after":U()}],"break-before":[{"break-before":U()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:$()}],"overflow-x":[{"overflow-x":$()}],"overflow-y":[{"overflow-y":$()}],overscroll:[{overscroll:at()}],"overscroll-x":[{"overscroll-x":at()}],"overscroll-y":[{"overscroll-y":at()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:J()}],"inset-x":[{"inset-x":J()}],"inset-y":[{"inset-y":J()}],start:[{start:J()}],end:[{end:J()}],top:[{top:J()}],right:[{right:J()}],bottom:[{bottom:J()}],left:[{left:J()}],visibility:["visible","invisible","collapse"],z:[{z:[Ta,"auto",nt,et]}],basis:[{basis:[Ii,"full","auto",h,...Y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[yt,Ii,"auto","initial","none",et]}],grow:[{grow:["",yt,nt,et]}],shrink:[{shrink:["",yt,nt,et]}],order:[{order:[Ta,"first","last","none",nt,et]}],"grid-cols":[{"grid-cols":mt()}],"col-start-end":[{col:Vt()}],"col-start":[{"col-start":Bt()}],"col-end":[{"col-end":Bt()}],"grid-rows":[{"grid-rows":mt()}],"row-start-end":[{row:Vt()}],"row-start":[{"row-start":Bt()}],"row-end":[{"row-end":Bt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Pe()}],"auto-rows":[{"auto-rows":Pe()}],gap:[{gap:Y()}],"gap-x":[{"gap-x":Y()}],"gap-y":[{"gap-y":Y()}],"justify-content":[{justify:[...Ae(),"normal"]}],"justify-items":[{"justify-items":[...jt(),"normal"]}],"justify-self":[{"justify-self":["auto",...jt()]}],"align-content":[{content:["normal",...Ae()]}],"align-items":[{items:[...jt(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...jt(),{baseline:["","last"]}]}],"place-content":[{"place-content":Ae()}],"place-items":[{"place-items":[...jt(),"baseline"]}],"place-self":[{"place-self":["auto",...jt()]}],p:[{p:Y()}],px:[{px:Y()}],py:[{py:Y()}],ps:[{ps:Y()}],pe:[{pe:Y()}],pt:[{pt:Y()}],pr:[{pr:Y()}],pb:[{pb:Y()}],pl:[{pl:Y()}],m:[{m:j()}],mx:[{mx:j()}],my:[{my:j()}],ms:[{ms:j()}],me:[{me:j()}],mt:[{mt:j()}],mr:[{mr:j()}],mb:[{mb:j()}],ml:[{ml:j()}],"space-x":[{"space-x":Y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Y()}],"space-y-reverse":["space-y-reverse"],size:[{size:F()}],w:[{w:[h,"screen",...F()]}],"min-w":[{"min-w":[h,"screen","none",...F()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[d]},...F()]}],h:[{h:["screen","lh",...F()]}],"min-h":[{"min-h":["screen","lh","none",...F()]}],"max-h":[{"max-h":["screen","lh",...F()]}],"font-size":[{text:["base",s,Cr,Wa]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,nt,Kf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Qf,et]}],"font-family":[{font:[kD,et,a]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,nt,et]}],"line-clamp":[{"line-clamp":[yt,"none",nt,Kf]}],leading:[{leading:[c,...Y()]}],"list-image":[{"list-image":["none",nt,et]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",nt,et]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:X()}],"text-color":[{text:X()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...vt(),"wavy"]}],"text-decoration-thickness":[{decoration:[yt,"from-font","auto",nt,Wa]}],"text-decoration-color":[{decoration:X()}],"underline-offset":[{"underline-offset":[yt,"auto",nt,et]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",nt,et]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",nt,et]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:xt()}],"bg-repeat":[{bg:M()}],"bg-size":[{bg:Q()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ta,nt,et],radial:["",nt,et],conic:[Ta,nt,et]},qD,jD]}],"bg-color":[{bg:X()}],"gradient-from-pos":[{from:W()}],"gradient-via-pos":[{via:W()}],"gradient-to-pos":[{to:W()}],"gradient-from":[{from:X()}],"gradient-via":[{via:X()}],"gradient-to":[{to:X()}],rounded:[{rounded:Z()}],"rounded-s":[{"rounded-s":Z()}],"rounded-e":[{"rounded-e":Z()}],"rounded-t":[{"rounded-t":Z()}],"rounded-r":[{"rounded-r":Z()}],"rounded-b":[{"rounded-b":Z()}],"rounded-l":[{"rounded-l":Z()}],"rounded-ss":[{"rounded-ss":Z()}],"rounded-se":[{"rounded-se":Z()}],"rounded-ee":[{"rounded-ee":Z()}],"rounded-es":[{"rounded-es":Z()}],"rounded-tl":[{"rounded-tl":Z()}],"rounded-tr":[{"rounded-tr":Z()}],"rounded-br":[{"rounded-br":Z()}],"rounded-bl":[{"rounded-bl":Z()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...vt(),"hidden","none"]}],"divide-style":[{divide:[...vt(),"hidden","none"]}],"border-color":[{border:X()}],"border-color-x":[{"border-x":X()}],"border-color-y":[{"border-y":X()}],"border-color-s":[{"border-s":X()}],"border-color-e":[{"border-e":X()}],"border-color-t":[{"border-t":X()}],"border-color-r":[{"border-r":X()}],"border-color-b":[{"border-b":X()}],"border-color-l":[{"border-l":X()}],"divide-color":[{divide:X()}],"outline-style":[{outline:[...vt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[yt,nt,et]}],"outline-w":[{outline:["",yt,Cr,Wa]}],"outline-color":[{outline:X()}],shadow:[{shadow:["","none",y,Ro,wo]}],"shadow-color":[{shadow:X()}],"inset-shadow":[{"inset-shadow":["none",v,Ro,wo]}],"inset-shadow-color":[{"inset-shadow":X()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:X()}],"ring-offset-w":[{"ring-offset":[yt,Wa]}],"ring-offset-color":[{"ring-offset":X()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":X()}],"text-shadow":[{"text-shadow":["none",S,Ro,wo]}],"text-shadow-color":[{"text-shadow":X()}],opacity:[{opacity:[yt,nt,et]}],"mix-blend":[{"mix-blend":[...ct(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ct()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[yt]}],"mask-image-linear-from-pos":[{"mask-linear-from":At()}],"mask-image-linear-to-pos":[{"mask-linear-to":At()}],"mask-image-linear-from-color":[{"mask-linear-from":X()}],"mask-image-linear-to-color":[{"mask-linear-to":X()}],"mask-image-t-from-pos":[{"mask-t-from":At()}],"mask-image-t-to-pos":[{"mask-t-to":At()}],"mask-image-t-from-color":[{"mask-t-from":X()}],"mask-image-t-to-color":[{"mask-t-to":X()}],"mask-image-r-from-pos":[{"mask-r-from":At()}],"mask-image-r-to-pos":[{"mask-r-to":At()}],"mask-image-r-from-color":[{"mask-r-from":X()}],"mask-image-r-to-color":[{"mask-r-to":X()}],"mask-image-b-from-pos":[{"mask-b-from":At()}],"mask-image-b-to-pos":[{"mask-b-to":At()}],"mask-image-b-from-color":[{"mask-b-from":X()}],"mask-image-b-to-color":[{"mask-b-to":X()}],"mask-image-l-from-pos":[{"mask-l-from":At()}],"mask-image-l-to-pos":[{"mask-l-to":At()}],"mask-image-l-from-color":[{"mask-l-from":X()}],"mask-image-l-to-color":[{"mask-l-to":X()}],"mask-image-x-from-pos":[{"mask-x-from":At()}],"mask-image-x-to-pos":[{"mask-x-to":At()}],"mask-image-x-from-color":[{"mask-x-from":X()}],"mask-image-x-to-color":[{"mask-x-to":X()}],"mask-image-y-from-pos":[{"mask-y-from":At()}],"mask-image-y-to-pos":[{"mask-y-to":At()}],"mask-image-y-from-color":[{"mask-y-from":X()}],"mask-image-y-to-color":[{"mask-y-to":X()}],"mask-image-radial":[{"mask-radial":[nt,et]}],"mask-image-radial-from-pos":[{"mask-radial-from":At()}],"mask-image-radial-to-pos":[{"mask-radial-to":At()}],"mask-image-radial-from-color":[{"mask-radial-from":X()}],"mask-image-radial-to-color":[{"mask-radial-to":X()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":K()}],"mask-image-conic-pos":[{"mask-conic":[yt]}],"mask-image-conic-from-pos":[{"mask-conic-from":At()}],"mask-image-conic-to-pos":[{"mask-conic-to":At()}],"mask-image-conic-from-color":[{"mask-conic-from":X()}],"mask-image-conic-to-color":[{"mask-conic-to":X()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:xt()}],"mask-repeat":[{mask:M()}],"mask-size":[{mask:Q()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",nt,et]}],filter:[{filter:["","none",nt,et]}],blur:[{blur:Nt()}],brightness:[{brightness:[yt,nt,et]}],contrast:[{contrast:[yt,nt,et]}],"drop-shadow":[{"drop-shadow":["","none",E,Ro,wo]}],"drop-shadow-color":[{"drop-shadow":X()}],grayscale:[{grayscale:["",yt,nt,et]}],"hue-rotate":[{"hue-rotate":[yt,nt,et]}],invert:[{invert:["",yt,nt,et]}],saturate:[{saturate:[yt,nt,et]}],sepia:[{sepia:["",yt,nt,et]}],"backdrop-filter":[{"backdrop-filter":["","none",nt,et]}],"backdrop-blur":[{"backdrop-blur":Nt()}],"backdrop-brightness":[{"backdrop-brightness":[yt,nt,et]}],"backdrop-contrast":[{"backdrop-contrast":[yt,nt,et]}],"backdrop-grayscale":[{"backdrop-grayscale":["",yt,nt,et]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[yt,nt,et]}],"backdrop-invert":[{"backdrop-invert":["",yt,nt,et]}],"backdrop-opacity":[{"backdrop-opacity":[yt,nt,et]}],"backdrop-saturate":[{"backdrop-saturate":[yt,nt,et]}],"backdrop-sepia":[{"backdrop-sepia":["",yt,nt,et]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Y()}],"border-spacing-x":[{"border-spacing-x":Y()}],"border-spacing-y":[{"border-spacing-y":Y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",nt,et]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[yt,"initial",nt,et]}],ease:[{ease:["linear","initial",R,nt,et]}],delay:[{delay:[yt,nt,et]}],animate:[{animate:["none",V,nt,et]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[T,nt,et]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:Ne()}],"rotate-x":[{"rotate-x":Ne()}],"rotate-y":[{"rotate-y":Ne()}],"rotate-z":[{"rotate-z":Ne()}],scale:[{scale:Zn()}],"scale-x":[{"scale-x":Zn()}],"scale-y":[{"scale-y":Zn()}],"scale-z":[{"scale-z":Zn()}],"scale-3d":["scale-3d"],skew:[{skew:$n()}],"skew-x":[{"skew-x":$n()}],"skew-y":[{"skew-y":$n()}],transform:[{transform:[nt,et,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Jn()}],"translate-x":[{"translate-x":Jn()}],"translate-y":[{"translate-y":Jn()}],"translate-z":[{"translate-z":Jn()}],"translate-none":["translate-none"],accent:[{accent:X()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:X()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",nt,et]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Y()}],"scroll-mx":[{"scroll-mx":Y()}],"scroll-my":[{"scroll-my":Y()}],"scroll-ms":[{"scroll-ms":Y()}],"scroll-me":[{"scroll-me":Y()}],"scroll-mt":[{"scroll-mt":Y()}],"scroll-mr":[{"scroll-mr":Y()}],"scroll-mb":[{"scroll-mb":Y()}],"scroll-ml":[{"scroll-ml":Y()}],"scroll-p":[{"scroll-p":Y()}],"scroll-px":[{"scroll-px":Y()}],"scroll-py":[{"scroll-py":Y()}],"scroll-ps":[{"scroll-ps":Y()}],"scroll-pe":[{"scroll-pe":Y()}],"scroll-pt":[{"scroll-pt":Y()}],"scroll-pr":[{"scroll-pr":Y()}],"scroll-pb":[{"scroll-pb":Y()}],"scroll-pl":[{"scroll-pl":Y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",nt,et]}],fill:[{fill:["none",...X()]}],"stroke-w":[{stroke:[yt,Cr,Wa,Kf]}],stroke:[{stroke:["none",...X()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},XD=wD(YD);function QD(...n){return XD(fD(n))}const KD={primary:"btn-primary",secondary:"btn-secondary",outline:"btn-outline",ghost:"btn-ghost",destructive:"bg-error-600 text-white hover:bg-error-700 focus:ring-error-500"},FD={sm:"btn-sm text-xs px-3 py-1.5",md:"h-10 px-4 py-2 text-sm",lg:"btn-lg text-base",xl:"h-12 px-6 py-3 text-lg"},ts=({variant:n="primary",size:a="md",loading:s=!1,leftIcon:r,rightIcon:o,fullWidth:c=!1,disabled:d,className:h,children:p,...m})=>{const y=d||s;return it.jsxs(nD.button,{whileHover:y?{}:{scale:1.02},whileTap:y?{}:{scale:.98},className:QD("btn",KD[n],FD[a],c&&"w-full",y&&"opacity-50 cursor-not-allowed",h),disabled:y,...m,children:[s&&it.jsx(cD,{className:"w-4 h-4 mr-2 animate-spin"}),!s&&r&&it.jsx("span",{className:"mr-2",children:r}),it.jsx("span",{children:p}),!s&&o&&it.jsx("span",{className:"ml-2",children:o})]})},ZD=new rA({defaultOptions:{queries:{retry:3,retryDelay:n=>Math.min(1e3*2**n,3e4),staleTime:5*60*1e3,gcTime:10*60*1e3},mutations:{retry:1}}});function $D(){const{checkAuth:n,isAuthenticated:a,user:s}=yR(),{theme:r}=bR();return z.useEffect(()=>{n()},[n]),z.useEffect(()=>{const o=document.documentElement;r.mode==="dark"?o.classList.add("dark"):o.classList.remove("dark"),o.style.setProperty("--primary-color",r.primaryColor),o.style.setProperty("--secondary-color",r.secondaryColor),o.style.setProperty("--border-radius",`${r.borderRadius}px`),o.style.setProperty("--font-size",`${r.fontSize}px`),o.style.setProperty("--font-family",r.fontFamily)},[r]),it.jsxs(oA,{client:ZD,children:[it.jsx(AE,{children:it.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50",children:[it.jsx("header",{className:"bg-white shadow-soft border-b border-secondary-200",children:it.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:it.jsxs("div",{className:"flex justify-between items-center h-16",children:[it.jsx("div",{className:"flex items-center",children:it.jsx("h1",{className:"text-2xl font-bold gradient-text",children:"🚀 Project2 - AI Platform"})}),it.jsx("div",{className:"flex items-center space-x-4",children:a?it.jsxs("div",{className:"flex items-center space-x-4",children:[it.jsxs("span",{className:"text-sm text-secondary-600",children:["Welcome, ",(s==null?void 0:s.firstName)||"User","!"]}),it.jsx(ts,{variant:"outline",size:"sm",children:"Dashboard"}),it.jsx(ts,{variant:"ghost",size:"sm",children:"Logout"})]}):it.jsxs("div",{className:"flex items-center space-x-2",children:[it.jsx(ts,{variant:"ghost",size:"sm",children:"Login"}),it.jsx(ts,{variant:"primary",size:"sm",children:"Sign Up"})]})})]})})}),it.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:it.jsxs("div",{className:"text-center",children:[it.jsx("h2",{className:"text-4xl font-bold text-secondary-900 mb-4",children:"Welcome to the Future of AI"}),it.jsx("p",{className:"text-xl text-secondary-600 mb-8 max-w-3xl mx-auto",children:"A complete microservices platform with React, FastAPI, MLflow, Kubernetes, and advanced monitoring. Built for scale, performance, and innovation."}),it.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12",children:[it.jsxs("div",{className:"card p-6",children:[it.jsx("div",{className:"text-3xl mb-4",children:"🎨"}),it.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-2",children:"Modern Frontend"}),it.jsx("p",{className:"text-secondary-600",children:"React 18 + TypeScript + Vite + Tailwind CSS for blazing fast development"})]}),it.jsxs("div",{className:"card p-6",children:[it.jsx("div",{className:"text-3xl mb-4",children:"⚡"}),it.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-2",children:"FastAPI Backend"}),it.jsx("p",{className:"text-secondary-600",children:"High-performance async API with automatic documentation and validation"})]}),it.jsxs("div",{className:"card p-6",children:[it.jsx("div",{className:"text-3xl mb-4",children:"🤖"}),it.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-2",children:"AI & ML Pipeline"}),it.jsx("p",{className:"text-secondary-600",children:"MLflow tracking, Pinecone vector DB, and federated learning capabilities"})]}),it.jsxs("div",{className:"card p-6",children:[it.jsx("div",{className:"text-3xl mb-4",children:"☸️"}),it.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-2",children:"Kubernetes Ready"}),it.jsx("p",{className:"text-secondary-600",children:"Auto-scaling, load balancing, and container orchestration out of the box"})]}),it.jsxs("div",{className:"card p-6",children:[it.jsx("div",{className:"text-3xl mb-4",children:"📊"}),it.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-2",children:"Full Observability"}),it.jsx("p",{className:"text-secondary-600",children:"Prometheus, Grafana, and Jaeger for complete system monitoring"})]}),it.jsxs("div",{className:"card p-6",children:[it.jsx("div",{className:"text-3xl mb-4",children:"🔒"}),it.jsx("h3",{className:"text-lg font-semibold text-secondary-900 mb-2",children:"Enterprise Security"}),it.jsx("p",{className:"text-secondary-600",children:"JWT authentication, RBAC, and comprehensive security policies"})]})]}),it.jsxs("div",{className:"mt-12",children:[it.jsx(ts,{size:"lg",className:"mr-4",children:"Get Started"}),it.jsx(ts,{variant:"outline",size:"lg",children:"View Documentation"})]})]})})]})}),!1]})}BT.createRoot(document.getElementById("root")).render(it.jsx(z.StrictMode,{children:it.jsx($D,{})}));
