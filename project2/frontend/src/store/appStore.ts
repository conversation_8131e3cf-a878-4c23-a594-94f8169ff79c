// ============================================================================
// APP STORE - Global Application State Management
// ============================================================================

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { ThemeConfig, ToastProps, AppConfig } from '../types';

interface AppState {
  // Theme
  theme: ThemeConfig;
  
  // UI State
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
  
  // Notifications
  toasts: ToastProps[];
  
  // Navigation
  currentPath: string;
  breadcrumbs: { label: string; path?: string }[];
  
  // Loading states
  globalLoading: boolean;
  
  // Configuration
  config: AppConfig;
  
  // Feature flags
  features: Record<string, boolean>;
}

interface AppStore extends AppState {
  // Theme actions
  setTheme: (theme: Partial<ThemeConfig>) => void;
  toggleTheme: () => void;
  
  // UI actions
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  setMobileMenuOpen: (open: boolean) => void;
  toggleMobileMenu: () => void;
  
  // Toast actions
  addToast: (toast: Omit<ToastProps, 'id'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
  
  // Navigation actions
  setCurrentPath: (path: string) => void;
  setBreadcrumbs: (breadcrumbs: { label: string; path?: string }[]) => void;
  
  // Loading actions
  setGlobalLoading: (loading: boolean) => void;
  
  // Configuration actions
  updateConfig: (config: Partial<AppConfig>) => void;
  
  // Feature flag actions
  setFeature: (key: string, enabled: boolean) => void;
  isFeatureEnabled: (key: string) => boolean;
}

const defaultTheme: ThemeConfig = {
  mode: 'light',
  primaryColor: '#3b82f6',
  secondaryColor: '#64748b',
  borderRadius: 8,
  fontSize: 14,
  fontFamily: 'Inter',
};

const defaultConfig: AppConfig = {
  apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  wsUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:8000',
  environment: (import.meta.env.VITE_ENVIRONMENT as any) || 'development',
  features: {},
  analytics: {
    enabled: import.meta.env.VITE_ANALYTICS_ENABLED === 'true',
    trackingId: import.meta.env.VITE_ANALYTICS_TRACKING_ID,
    debug: import.meta.env.VITE_ANALYTICS_DEBUG === 'true',
  },
};

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Initial state
      theme: defaultTheme,
      sidebarOpen: true,
      mobileMenuOpen: false,
      toasts: [],
      currentPath: '/',
      breadcrumbs: [],
      globalLoading: false,
      config: defaultConfig,
      features: {},

      // Theme actions
      setTheme: (themeUpdate: Partial<ThemeConfig>) => {
        set((state) => ({
          theme: { ...state.theme, ...themeUpdate },
        }));
      },

      toggleTheme: () => {
        set((state) => ({
          theme: {
            ...state.theme,
            mode: state.theme.mode === 'light' ? 'dark' : 'light',
          },
        }));
      },

      // UI actions
      setSidebarOpen: (open: boolean) => {
        set({ sidebarOpen: open });
      },

      toggleSidebar: () => {
        set((state) => ({ sidebarOpen: !state.sidebarOpen }));
      },

      setMobileMenuOpen: (open: boolean) => {
        set({ mobileMenuOpen: open });
      },

      toggleMobileMenu: () => {
        set((state) => ({ mobileMenuOpen: !state.mobileMenuOpen }));
      },

      // Toast actions
      addToast: (toast: Omit<ToastProps, 'id'>) => {
        const id = Math.random().toString(36).substr(2, 9);
        const newToast: ToastProps = { ...toast, id };
        
        set((state) => ({
          toasts: [...state.toasts, newToast],
        }));

        // Auto remove toast after duration
        if (toast.duration !== 0) {
          setTimeout(() => {
            get().removeToast(id);
          }, toast.duration || 5000);
        }
      },

      removeToast: (id: string) => {
        set((state) => ({
          toasts: state.toasts.filter((toast) => toast.id !== id),
        }));
      },

      clearToasts: () => {
        set({ toasts: [] });
      },

      // Navigation actions
      setCurrentPath: (path: string) => {
        set({ currentPath: path });
      },

      setBreadcrumbs: (breadcrumbs: { label: string; path?: string }[]) => {
        set({ breadcrumbs });
      },

      // Loading actions
      setGlobalLoading: (loading: boolean) => {
        set({ globalLoading: loading });
      },

      // Configuration actions
      updateConfig: (configUpdate: Partial<AppConfig>) => {
        set((state) => ({
          config: { ...state.config, ...configUpdate },
        }));
      },

      // Feature flag actions
      setFeature: (key: string, enabled: boolean) => {
        set((state) => ({
          features: { ...state.features, [key]: enabled },
        }));
      },

      isFeatureEnabled: (key: string) => {
        const { features, config } = get();
        return features[key] ?? config.features[key] ?? false;
      },
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        theme: state.theme,
        sidebarOpen: state.sidebarOpen,
        features: state.features,
      }),
    }
  )
);

// Selectors
export const useTheme = () => useAppStore((state) => state.theme);
export const useUI = () => useAppStore((state) => ({
  sidebarOpen: state.sidebarOpen,
  mobileMenuOpen: state.mobileMenuOpen,
  globalLoading: state.globalLoading,
}));
export const useToasts = () => useAppStore((state) => state.toasts);
export const useNavigation = () => useAppStore((state) => ({
  currentPath: state.currentPath,
  breadcrumbs: state.breadcrumbs,
}));
export const useConfig = () => useAppStore((state) => state.config);

// Action selectors
export const useThemeActions = () => useAppStore((state) => ({
  setTheme: state.setTheme,
  toggleTheme: state.toggleTheme,
}));

export const useUIActions = () => useAppStore((state) => ({
  setSidebarOpen: state.setSidebarOpen,
  toggleSidebar: state.toggleSidebar,
  setMobileMenuOpen: state.setMobileMenuOpen,
  toggleMobileMenu: state.toggleMobileMenu,
  setGlobalLoading: state.setGlobalLoading,
}));

export const useToastActions = () => useAppStore((state) => ({
  addToast: state.addToast,
  removeToast: state.removeToast,
  clearToasts: state.clearToasts,
}));

export const useNavigationActions = () => useAppStore((state) => ({
  setCurrentPath: state.setCurrentPath,
  setBreadcrumbs: state.setBreadcrumbs,
}));

export const useFeatures = () => useAppStore((state) => ({
  isFeatureEnabled: state.isFeatureEnabled,
  setFeature: state.setFeature,
}));
