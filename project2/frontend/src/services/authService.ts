// ============================================================================
// AUTH SERVICE - Authentication API Service
// ============================================================================

import { apiClient } from './apiClient';
import { 
  User, 
  LoginCredentials, 
  RegisterData, 
  ResetPasswordData, 
  ChangePasswordData,
  ApiResponse 
} from '../types';

class AuthService {
  private readonly baseUrl = '/auth';

  /**
   * Login user with email and password
   */
  async login(credentials: LoginCredentials): Promise<ApiResponse<{
    user: User;
    token: string;
    refreshToken: string;
  }>> {
    return apiClient.post(`${this.baseUrl}/login`, credentials);
  }

  /**
   * Register new user
   */
  async register(data: RegisterData): Promise<ApiResponse<{
    user: User;
    token: string;
    refreshToken: string;
  }>> {
    return apiClient.post(`${this.baseUrl}/register`, data);
  }

  /**
   * Logout current user
   */
  async logout(): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/logout`);
      
      // Clear local storage
      localStorage.removeItem('auth-token');
      localStorage.removeItem('refresh-token');
      
      return response;
    } catch (error) {
      // Even if logout fails on server, clear local storage
      localStorage.removeItem('auth-token');
      localStorage.removeItem('refresh-token');
      throw error;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse<{
    token: string;
    refreshToken: string;
    user: User;
  }>> {
    return apiClient.post(`${this.baseUrl}/refresh`, { refreshToken });
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return apiClient.get(`${this.baseUrl}/me`);
  }

  /**
   * Update user profile
   */
  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    return apiClient.patch(`${this.baseUrl}/profile`, data);
  }

  /**
   * Change user password
   */
  async changePassword(data: ChangePasswordData): Promise<ApiResponse<void>> {
    return apiClient.post(`${this.baseUrl}/change-password`, data);
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(data: ResetPasswordData): Promise<ApiResponse<void>> {
    return apiClient.post(`${this.baseUrl}/reset-password`, data);
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<ApiResponse<void>> {
    return apiClient.post(`${this.baseUrl}/reset-password/confirm`, {
      token,
      newPassword,
    });
  }

  /**
   * Verify email address
   */
  async verifyEmail(token: string): Promise<ApiResponse<void>> {
    return apiClient.post(`${this.baseUrl}/verify-email`, { token });
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(): Promise<ApiResponse<void>> {
    return apiClient.post(`${this.baseUrl}/verify-email/resend`);
  }

  /**
   * Enable two-factor authentication
   */
  async enableTwoFactor(): Promise<ApiResponse<{
    qrCode: string;
    secret: string;
  }>> {
    return apiClient.post(`${this.baseUrl}/2fa/enable`);
  }

  /**
   * Confirm two-factor authentication setup
   */
  async confirmTwoFactor(code: string): Promise<ApiResponse<{
    backupCodes: string[];
  }>> {
    return apiClient.post(`${this.baseUrl}/2fa/confirm`, { code });
  }

  /**
   * Disable two-factor authentication
   */
  async disableTwoFactor(password: string): Promise<ApiResponse<void>> {
    return apiClient.post(`${this.baseUrl}/2fa/disable`, { password });
  }

  /**
   * Verify two-factor authentication code
   */
  async verifyTwoFactor(code: string): Promise<ApiResponse<{
    token: string;
    refreshToken: string;
  }>> {
    return apiClient.post(`${this.baseUrl}/2fa/verify`, { code });
  }

  /**
   * Get user sessions
   */
  async getSessions(): Promise<ApiResponse<Array<{
    id: string;
    deviceName: string;
    ipAddress: string;
    userAgent: string;
    lastActivity: string;
    isCurrent: boolean;
  }>>> {
    return apiClient.get(`${this.baseUrl}/sessions`);
  }

  /**
   * Revoke user session
   */
  async revokeSession(sessionId: string): Promise<ApiResponse<void>> {
    return apiClient.delete(`${this.baseUrl}/sessions/${sessionId}`);
  }

  /**
   * Revoke all other sessions
   */
  async revokeAllOtherSessions(): Promise<ApiResponse<void>> {
    return apiClient.post(`${this.baseUrl}/sessions/revoke-all`);
  }

  /**
   * Upload user avatar
   */
  async uploadAvatar(file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<{
    avatarUrl: string;
  }>> {
    return apiClient.upload(`${this.baseUrl}/avatar`, file, onProgress);
  }

  /**
   * Delete user avatar
   */
  async deleteAvatar(): Promise<ApiResponse<void>> {
    return apiClient.delete(`${this.baseUrl}/avatar`);
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<ApiResponse<User['preferences']>> {
    return apiClient.get(`${this.baseUrl}/preferences`);
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences: Partial<User['preferences']>): Promise<ApiResponse<User['preferences']>> {
    return apiClient.patch(`${this.baseUrl}/preferences`, preferences);
  }

  /**
   * Delete user account
   */
  async deleteAccount(password: string): Promise<ApiResponse<void>> {
    return apiClient.post(`${this.baseUrl}/delete-account`, { password });
  }

  /**
   * Export user data
   */
  async exportUserData(): Promise<ApiResponse<{
    downloadUrl: string;
    expiresAt: string;
  }>> {
    return apiClient.post(`${this.baseUrl}/export-data`);
  }

  /**
   * Check if email is available
   */
  async checkEmailAvailability(email: string): Promise<ApiResponse<{
    available: boolean;
  }>> {
    return apiClient.get(`${this.baseUrl}/check-email?email=${encodeURIComponent(email)}`);
  }

  /**
   * Check if username is available
   */
  async checkUsernameAvailability(username: string): Promise<ApiResponse<{
    available: boolean;
  }>> {
    return apiClient.get(`${this.baseUrl}/check-username?username=${encodeURIComponent(username)}`);
  }

  /**
   * Get authentication logs
   */
  async getAuthLogs(page: number = 1, limit: number = 20): Promise<ApiResponse<Array<{
    id: string;
    action: string;
    ipAddress: string;
    userAgent: string;
    success: boolean;
    timestamp: string;
    details?: Record<string, any>;
  }>>> {
    return apiClient.get(`${this.baseUrl}/logs?page=${page}&limit=${limit}`);
  }
}

// Create and export singleton instance
export const authService = new AuthService();
export default authService;
