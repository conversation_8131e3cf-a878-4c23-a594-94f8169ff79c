// ============================================================================
// TYPES DEFINITIONS - Frontend TypeScript Types
// ============================================================================

// User Types
export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: UserRole;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: NotificationSettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  marketing: boolean;
}

export type UserRole = 'admin' | 'user' | 'moderator' | 'analyst';

// Authentication Types
export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  username: string;
  password: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
}

export interface ResetPasswordData {
  email: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  errors?: Record<string, string[]>;
  meta?: {
    pagination?: PaginationMeta;
    total?: number;
    page?: number;
    limit?: number;
  };
}

export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// AI/ML Types
export interface MLModel {
  id: string;
  name: string;
  description: string;
  version: string;
  type: ModelType;
  status: ModelStatus;
  accuracy?: number;
  createdAt: string;
  updatedAt: string;
  metadata: ModelMetadata;
}

export type ModelType = 'classification' | 'regression' | 'clustering' | 'nlp' | 'computer_vision';
export type ModelStatus = 'training' | 'ready' | 'deployed' | 'failed' | 'archived';

export interface ModelMetadata {
  framework: string;
  parameters: Record<string, any>;
  metrics: Record<string, number>;
  tags: string[];
}

export interface TrainingJob {
  id: string;
  modelId: string;
  status: JobStatus;
  progress: number;
  startedAt: string;
  completedAt?: string;
  logs: string[];
  metrics: Record<string, number>;
}

export type JobStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

// Data Types
export interface Dataset {
  id: string;
  name: string;
  description: string;
  size: number;
  format: string;
  columns: DataColumn[];
  createdAt: string;
  updatedAt: string;
  tags: string[];
}

export interface DataColumn {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'categorical';
  nullable: boolean;
  unique: boolean;
  description?: string;
}

// Analytics Types
export interface AnalyticsData {
  metrics: Metric[];
  charts: ChartData[];
  kpis: KPI[];
  timeRange: TimeRange;
}

export interface Metric {
  id: string;
  name: string;
  value: number;
  unit: string;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  description: string;
}

export interface ChartData {
  id: string;
  type: ChartType;
  title: string;
  data: any[];
  config: ChartConfig;
}

export type ChartType = 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'heatmap';

export interface ChartConfig {
  xAxis?: string;
  yAxis?: string;
  colors?: string[];
  legend?: boolean;
  grid?: boolean;
}

export interface KPI {
  id: string;
  name: string;
  value: number;
  target: number;
  unit: string;
  status: 'good' | 'warning' | 'critical';
}

export interface TimeRange {
  start: string;
  end: string;
  granularity: 'hour' | 'day' | 'week' | 'month' | 'year';
}

// UI Component Types
export interface TableColumn<T = any> {
  key: keyof T;
  title: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: T) => React.ReactNode;
  width?: string | number;
}

export interface TableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: PaginationProps;
  onRowClick?: (record: T) => void;
  onSort?: (key: keyof T, direction: 'asc' | 'desc') => void;
  onFilter?: (filters: Record<string, any>) => void;
}

export interface PaginationProps {
  current: number;
  total: number;
  pageSize: number;
  onChange: (page: number, pageSize: number) => void;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  children: React.ReactNode;
}

export interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file';
  placeholder?: string;
  required?: boolean;
  validation?: ValidationRule[];
  options?: SelectOption[];
  disabled?: boolean;
  description?: string;
}

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

export interface ValidationRule {
  type: 'required' | 'email' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
}

// Navigation Types
export interface NavItem {
  id: string;
  label: string;
  icon?: string;
  path?: string;
  children?: NavItem[];
  badge?: string | number;
  disabled?: boolean;
  external?: boolean;
}

export interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: string;
}

// Theme Types
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  secondaryColor: string;
  borderRadius: number;
  fontSize: number;
  fontFamily: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  path?: string;
}

// Utility Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export type SortDirection = 'asc' | 'desc';

export type FilterOperator = 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith';

export interface Filter {
  field: string;
  operator: FilterOperator;
  value: any;
}

export interface Sort {
  field: string;
  direction: SortDirection;
}

// Event Types
export interface AppEvent {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
}

// Configuration Types
export interface AppConfig {
  apiUrl: string;
  wsUrl: string;
  environment: 'development' | 'staging' | 'production';
  features: FeatureFlags;
  analytics: AnalyticsConfig;
}

export interface FeatureFlags {
  [key: string]: boolean;
}

export interface AnalyticsConfig {
  enabled: boolean;
  trackingId?: string;
  debug: boolean;
}
