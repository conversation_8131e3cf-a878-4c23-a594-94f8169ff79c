// ============================================================================
// MAIN APP COMPONENT - Root Application Component
// ============================================================================

import React, { useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Store imports
import { useAuthStore } from './store/authStore';
import { useAppStore } from './store/appStore';

// Component imports
import { Button } from './components/ui/Button';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

function App() {
  const { checkAuth, isAuthenticated, user } = useAuthStore();
  const { theme } = useAppStore();

  // Check authentication on app start
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;

    if (theme.mode === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Apply custom CSS variables
    root.style.setProperty('--primary-color', theme.primaryColor);
    root.style.setProperty('--secondary-color', theme.secondaryColor);
    root.style.setProperty('--border-radius', `${theme.borderRadius}px`);
    root.style.setProperty('--font-size', `${theme.fontSize}px`);
    root.style.setProperty('--font-family', theme.fontFamily);
  }, [theme]);

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
          {/* Header */}
          <header className="bg-white shadow-soft border-b border-secondary-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center h-16">
                <div className="flex items-center">
                  <h1 className="text-2xl font-bold gradient-text">
                    🚀 Project2 - AI Platform
                  </h1>
                </div>
                <div className="flex items-center space-x-4">
                  {isAuthenticated ? (
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-secondary-600">
                        Welcome, {user?.firstName || 'User'}!
                      </span>
                      <Button variant="outline" size="sm">
                        Dashboard
                      </Button>
                      <Button variant="ghost" size="sm">
                        Logout
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        Login
                      </Button>
                      <Button variant="primary" size="sm">
                        Sign Up
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <h2 className="text-4xl font-bold text-secondary-900 mb-4">
                Welcome to the Future of AI
              </h2>
              <p className="text-xl text-secondary-600 mb-8 max-w-3xl mx-auto">
                A complete microservices platform with React, FastAPI, MLflow, Kubernetes,
                and advanced monitoring. Built for scale, performance, and innovation.
              </p>

              {/* Feature Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
                <div className="card p-6">
                  <div className="text-3xl mb-4">🎨</div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                    Modern Frontend
                  </h3>
                  <p className="text-secondary-600">
                    React 18 + TypeScript + Vite + Tailwind CSS for blazing fast development
                  </p>
                </div>

                <div className="card p-6">
                  <div className="text-3xl mb-4">⚡</div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                    FastAPI Backend
                  </h3>
                  <p className="text-secondary-600">
                    High-performance async API with automatic documentation and validation
                  </p>
                </div>

                <div className="card p-6">
                  <div className="text-3xl mb-4">🤖</div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                    AI & ML Pipeline
                  </h3>
                  <p className="text-secondary-600">
                    MLflow tracking, Pinecone vector DB, and federated learning capabilities
                  </p>
                </div>

                <div className="card p-6">
                  <div className="text-3xl mb-4">☸️</div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                    Kubernetes Ready
                  </h3>
                  <p className="text-secondary-600">
                    Auto-scaling, load balancing, and container orchestration out of the box
                  </p>
                </div>

                <div className="card p-6">
                  <div className="text-3xl mb-4">📊</div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                    Full Observability
                  </h3>
                  <p className="text-secondary-600">
                    Prometheus, Grafana, and Jaeger for complete system monitoring
                  </p>
                </div>

                <div className="card p-6">
                  <div className="text-3xl mb-4">🔒</div>
                  <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                    Enterprise Security
                  </h3>
                  <p className="text-secondary-600">
                    JWT authentication, RBAC, and comprehensive security policies
                  </p>
                </div>
              </div>

              <div className="mt-12">
                <Button size="lg" className="mr-4">
                  Get Started
                </Button>
                <Button variant="outline" size="lg">
                  View Documentation
                </Button>
              </div>
            </div>
          </main>
        </div>
      </Router>

      {/* React Query Devtools (only in development) */}
      {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
}

export default App;
