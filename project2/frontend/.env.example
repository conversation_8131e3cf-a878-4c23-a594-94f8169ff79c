# ============================================================================
# ENVIRONMENT VARIABLES - Frontend Configuration
# ============================================================================

# API Configuration
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000

# Environment
VITE_ENVIRONMENT=development

# Analytics
VITE_ANALYTICS_ENABLED=false
VITE_ANALYTICS_TRACKING_ID=
VITE_ANALYTICS_DEBUG=true

# Feature Flags
VITE_FEATURE_AI_MODELS=true
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_NOTIFICATIONS=true
VITE_FEATURE_DARK_MODE=true

# External Services
VITE_PINECONE_API_KEY=
VITE_PINECONE_ENVIRONMENT=
VITE_MLFLOW_TRACKING_URI=http://localhost:5000

# Security
VITE_ENABLE_HTTPS=false
VITE_CSRF_TOKEN=

# Performance
VITE_ENABLE_PWA=true
VITE_ENABLE_SERVICE_WORKER=true

# Development
VITE_ENABLE_DEVTOOLS=true
VITE_LOG_LEVEL=debug
