# ============================================================================
# MAKEFILE - Project2 Development Commands
# ============================================================================

.PHONY: help install start stop restart logs clean test build deploy

# Default target
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m

# Project variables
PROJECT_NAME := project2
DOCKER_COMPOSE_FILE := docker/docker-compose.yml

help: ## Show this help message
	@echo "$(BLUE)Project2 - AI Microservices Platform$(NC)"
	@echo ""
	@echo "$(GREEN)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install and setup the complete environment
	@echo "$(BLUE)Installing Project2...$(NC)"
	@chmod +x scripts/setup/install.sh
	@./scripts/setup/install.sh

start: ## Start all services
	@echo "$(BLUE)Starting all services...$(NC)"
	@cd docker && docker-compose up -d
	@echo "$(GREEN)Services started successfully!$(NC)"
	@echo ""
	@echo "$(YELLOW)Access URLs:$(NC)"
	@echo "  Frontend:     http://localhost:5173"
	@echo "  API Gateway:  http://localhost:8000"
	@echo "  API Docs:     http://localhost:8000/docs"
	@echo "  Grafana:      http://localhost:3000"
	@echo "  Prometheus:   http://localhost:9090"

stop: ## Stop all services
	@echo "$(BLUE)Stopping all services...$(NC)"
	@cd docker && docker-compose down
	@echo "$(GREEN)Services stopped successfully!$(NC)"

restart: ## Restart all services
	@echo "$(BLUE)Restarting all services...$(NC)"
	@cd docker && docker-compose restart
	@echo "$(GREEN)Services restarted successfully!$(NC)"

logs: ## Show logs for all services
	@echo "$(BLUE)Showing logs...$(NC)"
	@cd docker && docker-compose logs -f

logs-service: ## Show logs for specific service (usage: make logs-service SERVICE=api-gateway)
	@echo "$(BLUE)Showing logs for $(SERVICE)...$(NC)"
	@cd docker && docker-compose logs -f $(SERVICE)

status: ## Show status of all services
	@echo "$(BLUE)Service Status:$(NC)"
	@cd docker && docker-compose ps

build: ## Build all Docker images
	@echo "$(BLUE)Building Docker images...$(NC)"
	@cd docker && docker-compose build --no-cache
	@echo "$(GREEN)Images built successfully!$(NC)"

build-service: ## Build specific service (usage: make build-service SERVICE=api-gateway)
	@echo "$(BLUE)Building $(SERVICE)...$(NC)"
	@cd docker && docker-compose build --no-cache $(SERVICE)
	@echo "$(GREEN)$(SERVICE) built successfully!$(NC)"

clean: ## Clean up Docker containers, images, and volumes
	@echo "$(BLUE)Cleaning up Docker resources...$(NC)"
	@cd docker && docker-compose down -v --remove-orphans
	@docker system prune -f
	@echo "$(GREEN)Cleanup completed!$(NC)"

clean-all: ## Clean up everything including images
	@echo "$(BLUE)Cleaning up all Docker resources...$(NC)"
	@cd docker && docker-compose down -v --remove-orphans --rmi all
	@docker system prune -af
	@echo "$(GREEN)Complete cleanup finished!$(NC)"

# Frontend commands
frontend-install: ## Install frontend dependencies
	@echo "$(BLUE)Installing frontend dependencies...$(NC)"
	@cd frontend && npm install
	@echo "$(GREEN)Frontend dependencies installed!$(NC)"

frontend-dev: ## Start frontend in development mode
	@echo "$(BLUE)Starting frontend development server...$(NC)"
	@cd frontend && npm run dev

frontend-build: ## Build frontend for production
	@echo "$(BLUE)Building frontend for production...$(NC)"
	@cd frontend && npm run build
	@echo "$(GREEN)Frontend built successfully!$(NC)"

frontend-test: ## Run frontend tests
	@echo "$(BLUE)Running frontend tests...$(NC)"
	@cd frontend && npm test

# Backend commands
backend-install: ## Install backend dependencies
	@echo "$(BLUE)Installing backend dependencies...$(NC)"
	@cd backend && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt
	@echo "$(GREEN)Backend dependencies installed!$(NC)"

backend-dev: ## Start backend in development mode
	@echo "$(BLUE)Starting backend development server...$(NC)"
	@cd backend && source venv/bin/activate && uvicorn gateway.main:app --reload --host 0.0.0.0 --port 8000

backend-test: ## Run backend tests
	@echo "$(BLUE)Running backend tests...$(NC)"
	@cd backend && source venv/bin/activate && pytest

# Database commands
db-init: ## Initialize database
	@echo "$(BLUE)Initializing database...$(NC)"
	@cd docker && docker-compose up -d postgres
	@sleep 10
	@cd docker && docker-compose exec postgres psql -U project2_user -d project2 -f /docker-entrypoint-initdb.d/init.sql
	@echo "$(GREEN)Database initialized!$(NC)"

db-migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(NC)"
	@cd backend && source venv/bin/activate && alembic upgrade head
	@echo "$(GREEN)Migrations completed!$(NC)"

db-reset: ## Reset database (WARNING: This will delete all data)
	@echo "$(RED)WARNING: This will delete all database data!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@cd docker && docker-compose down postgres
	@docker volume rm docker_postgres_data || true
	@cd docker && docker-compose up -d postgres
	@sleep 15
	@make db-init
	@echo "$(GREEN)Database reset completed!$(NC)"

# Testing commands
test: ## Run all tests
	@echo "$(BLUE)Running all tests...$(NC)"
	@make frontend-test
	@make backend-test
	@echo "$(GREEN)All tests completed!$(NC)"

test-integration: ## Run integration tests
	@echo "$(BLUE)Running integration tests...$(NC)"
	@cd tests && python -m pytest integration/
	@echo "$(GREEN)Integration tests completed!$(NC)"

test-e2e: ## Run end-to-end tests
	@echo "$(BLUE)Running end-to-end tests...$(NC)"
	@cd tests && python -m pytest e2e/
	@echo "$(GREEN)End-to-end tests completed!$(NC)"

# Monitoring commands
monitor: ## Open monitoring dashboards
	@echo "$(BLUE)Opening monitoring dashboards...$(NC)"
	@open http://localhost:3000 || xdg-open http://localhost:3000 || echo "Please open http://localhost:3000"
	@open http://localhost:9090 || xdg-open http://localhost:9090 || echo "Please open http://localhost:9090"
	@open http://localhost:16686 || xdg-open http://localhost:16686 || echo "Please open http://localhost:16686"

health: ## Check health of all services
	@echo "$(BLUE)Checking service health...$(NC)"
	@curl -s http://localhost:8000/health | jq . || echo "API Gateway: $(RED)DOWN$(NC)"
	@curl -s http://localhost:5173 > /dev/null && echo "Frontend: $(GREEN)UP$(NC)" || echo "Frontend: $(RED)DOWN$(NC)"
	@curl -s http://localhost:3000 > /dev/null && echo "Grafana: $(GREEN)UP$(NC)" || echo "Grafana: $(RED)DOWN$(NC)"
	@curl -s http://localhost:9090 > /dev/null && echo "Prometheus: $(GREEN)UP$(NC)" || echo "Prometheus: $(RED)DOWN$(NC)"

# Development commands
dev: ## Start development environment
	@echo "$(BLUE)Starting development environment...$(NC)"
	@make start
	@sleep 30
	@make health
	@echo "$(GREEN)Development environment ready!$(NC)"

dev-frontend: ## Start only frontend for development
	@echo "$(BLUE)Starting frontend development...$(NC)"
	@cd docker && docker-compose up -d postgres redis
	@make frontend-dev

dev-backend: ## Start only backend for development
	@echo "$(BLUE)Starting backend development...$(NC)"
	@cd docker && docker-compose up -d postgres redis rabbitmq
	@make backend-dev

# Deployment commands
deploy-staging: ## Deploy to staging environment
	@echo "$(BLUE)Deploying to staging...$(NC)"
	@echo "$(YELLOW)Staging deployment not implemented yet$(NC)"

deploy-production: ## Deploy to production environment
	@echo "$(BLUE)Deploying to production...$(NC)"
	@echo "$(YELLOW)Production deployment not implemented yet$(NC)"

# Utility commands
shell-postgres: ## Open PostgreSQL shell
	@cd docker && docker-compose exec postgres psql -U project2_user -d project2

shell-redis: ## Open Redis shell
	@cd docker && docker-compose exec redis redis-cli

backup: ## Create backup of database
	@echo "$(BLUE)Creating database backup...$(NC)"
	@mkdir -p backups
	@cd docker && docker-compose exec postgres pg_dump -U project2_user project2 > ../backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)Backup created in backups/ directory$(NC)"

restore: ## Restore database from backup (usage: make restore BACKUP=backup_file.sql)
	@echo "$(BLUE)Restoring database from $(BACKUP)...$(NC)"
	@cd docker && docker-compose exec -T postgres psql -U project2_user -d project2 < ../backups/$(BACKUP)
	@echo "$(GREEN)Database restored successfully!$(NC)"

update: ## Update all dependencies
	@echo "$(BLUE)Updating dependencies...$(NC)"
	@make frontend-install
	@make backend-install
	@echo "$(GREEN)Dependencies updated!$(NC)"

docs: ## Generate and serve documentation
	@echo "$(BLUE)Generating documentation...$(NC)"
	@cd docs && mkdocs serve
	@echo "$(GREEN)Documentation available at http://localhost:8001$(NC)"
