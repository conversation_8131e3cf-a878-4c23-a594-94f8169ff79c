#!/bin/bash

# ============================================================================
# AUTOMATED BACKUP SCRIPT - Database and Application Backup
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
BACKUP_DIR="backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS=30

# Database configuration
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="project2"
DB_USER="project2_user"
DB_PASSWORD="project2_password"

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Create backup directory
create_backup_dir() {
    log "Creating backup directory..."
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$BACKUP_DIR/database"
    mkdir -p "$BACKUP_DIR/redis"
    mkdir -p "$BACKUP_DIR/mlflow"
    mkdir -p "$BACKUP_DIR/uploads"
    mkdir -p "$BACKUP_DIR/logs"
}

# Backup PostgreSQL database
backup_postgres() {
    log "Backing up PostgreSQL database..."
    
    local backup_file="$BACKUP_DIR/database/postgres_backup_$TIMESTAMP.sql"
    
    if command -v docker-compose &> /dev/null; then
        # Using Docker Compose
        cd docker
        docker-compose exec -T postgres pg_dump -U "$DB_USER" "$DB_NAME" > "../$backup_file"
        cd ..
    else
        # Direct connection
        PGPASSWORD="$DB_PASSWORD" pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" > "$backup_file"
    fi
    
    # Compress backup
    gzip "$backup_file"
    log "PostgreSQL backup completed: ${backup_file}.gz"
}

# Backup Redis data
backup_redis() {
    log "Backing up Redis data..."
    
    local backup_file="$BACKUP_DIR/redis/redis_backup_$TIMESTAMP.rdb"
    
    if command -v docker-compose &> /dev/null; then
        cd docker
        docker-compose exec redis redis-cli BGSAVE
        sleep 5
        docker-compose exec redis cat /data/dump.rdb > "../$backup_file"
        cd ..
    else
        redis-cli BGSAVE
        sleep 5
        cp /var/lib/redis/dump.rdb "$backup_file"
    fi
    
    gzip "$backup_file"
    log "Redis backup completed: ${backup_file}.gz"
}

# Backup MLflow artifacts
backup_mlflow() {
    log "Backing up MLflow artifacts..."
    
    local backup_file="$BACKUP_DIR/mlflow/mlflow_artifacts_$TIMESTAMP.tar.gz"
    
    if [ -d "ai-ml/mlflow/artifacts" ]; then
        tar -czf "$backup_file" -C ai-ml/mlflow artifacts/
        log "MLflow artifacts backup completed: $backup_file"
    else
        warn "MLflow artifacts directory not found, skipping..."
    fi
}

# Backup uploaded files
backup_uploads() {
    log "Backing up uploaded files..."
    
    local backup_file="$BACKUP_DIR/uploads/uploads_$TIMESTAMP.tar.gz"
    
    if [ -d "uploads" ]; then
        tar -czf "$backup_file" uploads/
        log "Uploads backup completed: $backup_file"
    else
        warn "Uploads directory not found, skipping..."
    fi
}

# Backup application logs
backup_logs() {
    log "Backing up application logs..."
    
    local backup_file="$BACKUP_DIR/logs/logs_$TIMESTAMP.tar.gz"
    
    # Collect Docker logs
    if command -v docker-compose &> /dev/null; then
        cd docker
        mkdir -p ../temp_logs
        
        # Export logs from all services
        for service in $(docker-compose config --services); do
            docker-compose logs "$service" > "../temp_logs/${service}.log" 2>/dev/null || true
        done
        
        cd ..
        tar -czf "$backup_file" temp_logs/
        rm -rf temp_logs/
        log "Logs backup completed: $backup_file"
    else
        warn "Docker Compose not available, skipping logs backup..."
    fi
}

# Create backup manifest
create_manifest() {
    log "Creating backup manifest..."
    
    local manifest_file="$BACKUP_DIR/backup_manifest_$TIMESTAMP.json"
    
    cat > "$manifest_file" << EOF
{
    "backup_timestamp": "$TIMESTAMP",
    "backup_date": "$(date -Iseconds)",
    "version": "1.0.0",
    "environment": "development",
    "components": {
        "database": {
            "postgres": "postgres_backup_${TIMESTAMP}.sql.gz",
            "redis": "redis_backup_${TIMESTAMP}.rdb.gz"
        },
        "mlflow": "mlflow_artifacts_${TIMESTAMP}.tar.gz",
        "uploads": "uploads_${TIMESTAMP}.tar.gz",
        "logs": "logs_${TIMESTAMP}.tar.gz"
    },
    "retention_policy": {
        "retention_days": $RETENTION_DAYS,
        "cleanup_enabled": true
    },
    "backup_size": "$(du -sh $BACKUP_DIR | cut -f1)",
    "backup_location": "$(pwd)/$BACKUP_DIR"
}
EOF
    
    log "Backup manifest created: $manifest_file"
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    # Find and remove old backup files
    find "$BACKUP_DIR" -name "*backup_*" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*artifacts_*" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*uploads_*" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*logs_*" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*manifest_*" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    
    log "Old backups cleaned up"
}

# Verify backup integrity
verify_backup() {
    log "Verifying backup integrity..."
    
    local postgres_backup="$BACKUP_DIR/database/postgres_backup_${TIMESTAMP}.sql.gz"
    local redis_backup="$BACKUP_DIR/redis/redis_backup_${TIMESTAMP}.rdb.gz"
    
    # Check if backup files exist and are not empty
    if [ -f "$postgres_backup" ] && [ -s "$postgres_backup" ]; then
        log "✓ PostgreSQL backup verified"
    else
        error "PostgreSQL backup verification failed"
    fi
    
    if [ -f "$redis_backup" ] && [ -s "$redis_backup" ]; then
        log "✓ Redis backup verified"
    else
        warn "Redis backup verification failed"
    fi
    
    log "Backup verification completed"
}

# Send backup notification
send_notification() {
    log "Sending backup notification..."
    
    local backup_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    local backup_count=$(find "$BACKUP_DIR" -name "*backup_*" -type f | wc -l)
    
    # You can integrate with your notification service here
    # For now, just log the information
    log "Backup Summary:"
    log "  - Timestamp: $TIMESTAMP"
    log "  - Total Size: $backup_size"
    log "  - Files Created: $backup_count"
    log "  - Location: $(pwd)/$BACKUP_DIR"
}

# Main backup function
main() {
    log "Starting automated backup process..."
    
    # Check if services are running
    if ! docker-compose -f docker/docker-compose.yml ps | grep -q "Up"; then
        warn "Some services may not be running. Backup may be incomplete."
    fi
    
    create_backup_dir
    backup_postgres
    backup_redis
    backup_mlflow
    backup_uploads
    backup_logs
    create_manifest
    verify_backup
    cleanup_old_backups
    send_notification
    
    log "Backup process completed successfully!"
    log "Backup location: $(pwd)/$BACKUP_DIR"
}

# Handle script arguments
case "${1:-}" in
    --postgres-only)
        log "Running PostgreSQL backup only..."
        create_backup_dir
        backup_postgres
        verify_backup
        ;;
    --redis-only)
        log "Running Redis backup only..."
        create_backup_dir
        backup_redis
        ;;
    --cleanup)
        log "Running cleanup only..."
        cleanup_old_backups
        ;;
    --help)
        echo "Usage: $0 [OPTIONS]"
        echo "Options:"
        echo "  --postgres-only    Backup PostgreSQL only"
        echo "  --redis-only       Backup Redis only"
        echo "  --cleanup          Clean old backups only"
        echo "  --help             Show this help"
        ;;
    *)
        main
        ;;
esac
