#!/bin/bash

# ============================================================================
# INSTALLATION SCRIPT - Complete Environment Setup
# ============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check OS
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log "✓ Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        log "✓ macOS detected"
    else
        error "Unsupported operating system: $OSTYPE"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    log "✓ Docker found: $(docker --version)"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    log "✓ Docker Compose found: $(docker-compose --version)"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed. Please install Node.js first."
    fi
    log "✓ Node.js found: $(node --version)"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        error "npm is not installed. Please install npm first."
    fi
    log "✓ npm found: $(npm --version)"
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        error "Python 3 is not installed. Please install Python 3 first."
    fi
    log "✓ Python found: $(python3 --version)"
    
    # Check pip
    if ! command -v pip3 &> /dev/null; then
        error "pip3 is not installed. Please install pip3 first."
    fi
    log "✓ pip found: $(pip3 --version)"
}

# Setup environment variables
setup_env() {
    log "Setting up environment variables..."
    
    # Backend environment
    if [[ ! -f "backend/.env" ]]; then
        log "Creating backend .env file..."
        cp backend/.env.example backend/.env
        
        # Generate secret keys
        SECRET_KEY=$(openssl rand -hex 32)
        JWT_SECRET_KEY=$(openssl rand -hex 32)
        
        # Update .env file
        sed -i.bak "s/your-super-secret-key-here-at-least-32-characters-long/$SECRET_KEY/g" backend/.env
        sed -i.bak "s/your-jwt-secret-key-here-at-least-32-characters-long/$JWT_SECRET_KEY/g" backend/.env
        
        log "✓ Backend environment configured"
    else
        log "✓ Backend .env file already exists"
    fi
    
    # Frontend environment
    if [[ ! -f "frontend/.env" ]]; then
        log "Creating frontend .env file..."
        cp frontend/.env.example frontend/.env
        log "✓ Frontend environment configured"
    else
        log "✓ Frontend .env file already exists"
    fi
}

# Install frontend dependencies
install_frontend() {
    log "Installing frontend dependencies..."
    cd frontend
    
    if [[ ! -d "node_modules" ]]; then
        npm install
        log "✓ Frontend dependencies installed"
    else
        log "✓ Frontend dependencies already installed"
    fi
    
    cd ..
}

# Install backend dependencies
install_backend() {
    log "Installing backend dependencies..."
    
    # Create virtual environment if it doesn't exist
    if [[ ! -d "backend/venv" ]]; then
        log "Creating Python virtual environment..."
        cd backend
        python3 -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
        cd ..
        log "✓ Backend virtual environment created and dependencies installed"
    else
        log "✓ Backend virtual environment already exists"
    fi
}

# Setup Docker networks and volumes
setup_docker() {
    log "Setting up Docker environment..."
    
    # Create Docker network if it doesn't exist
    if ! docker network ls | grep -q "project2-network"; then
        docker network create project2-network
        log "✓ Docker network created"
    else
        log "✓ Docker network already exists"
    fi
    
    # Create necessary directories
    mkdir -p monitoring/grafana/provisioning/{dashboards,datasources}
    mkdir -p monitoring/grafana/dashboards
    mkdir -p ai-ml/mlflow/artifacts
    mkdir -p uploads
    
    log "✓ Docker environment prepared"
}

# Build Docker images
build_images() {
    log "Building Docker images..."
    
    cd docker
    docker-compose build --no-cache
    log "✓ Docker images built successfully"
    cd ..
}

# Initialize databases
init_databases() {
    log "Initializing databases..."
    
    # Start only database services first
    cd docker
    docker-compose up -d postgres redis
    
    # Wait for databases to be ready
    log "Waiting for databases to be ready..."
    sleep 30
    
    # Check if databases are ready
    docker-compose exec postgres pg_isready -U project2_user -d project2
    docker-compose exec redis redis-cli ping
    
    log "✓ Databases initialized successfully"
    cd ..
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Create Grafana datasource configuration
    cat > monitoring/grafana/provisioning/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

    # Create Grafana dashboard provisioning
    cat > monitoring/grafana/provisioning/dashboards/dashboard.yml << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF

    log "✓ Monitoring configuration created"
}

# Start all services
start_services() {
    log "Starting all services..."
    
    cd docker
    docker-compose up -d
    
    # Wait for services to be ready
    log "Waiting for services to start..."
    sleep 60
    
    # Check service health
    log "Checking service health..."
    
    # Check API Gateway
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log "✓ API Gateway is healthy"
    else
        warn "API Gateway health check failed"
    fi
    
    # Check Frontend
    if curl -f http://localhost:5173 > /dev/null 2>&1; then
        log "✓ Frontend is running"
    else
        warn "Frontend health check failed"
    fi
    
    # Check Grafana
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log "✓ Grafana is running"
    else
        warn "Grafana health check failed"
    fi
    
    # Check Prometheus
    if curl -f http://localhost:9090 > /dev/null 2>&1; then
        log "✓ Prometheus is running"
    else
        warn "Prometheus health check failed"
    fi
    
    cd ..
}

# Display final information
show_info() {
    log "Installation completed successfully!"
    echo
    echo -e "${BLUE}🚀 Project2 is now running!${NC}"
    echo
    echo -e "${GREEN}Access URLs:${NC}"
    echo "  Frontend:     http://localhost:5173"
    echo "  API Gateway:  http://localhost:8000"
    echo "  API Docs:     http://localhost:8000/docs"
    echo "  Grafana:      http://localhost:3000 (admin/admin123)"
    echo "  Prometheus:   http://localhost:9090"
    echo "  Jaeger:       http://localhost:16686"
    echo "  MLflow:       http://localhost:5000"
    echo "  Flower:       http://localhost:5555"
    echo "  RabbitMQ:     http://localhost:15672 (project2_user/project2_password)"
    echo
    echo -e "${GREEN}Default Admin User:${NC}"
    echo "  Email:        <EMAIL>"
    echo "  Password:     admin123"
    echo
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "  1. Visit http://localhost:5173 to access the frontend"
    echo "  2. Login with the admin credentials"
    echo "  3. Check the monitoring dashboards"
    echo "  4. Explore the API documentation"
    echo
    echo -e "${GREEN}Useful Commands:${NC}"
    echo "  Stop services:    cd docker && docker-compose down"
    echo "  View logs:        cd docker && docker-compose logs -f [service]"
    echo "  Restart service:  cd docker && docker-compose restart [service]"
    echo
}

# Main installation function
main() {
    log "Starting Project2 installation..."
    
    check_root
    check_requirements
    setup_env
    install_frontend
    install_backend
    setup_docker
    setup_monitoring
    build_images
    init_databases
    start_services
    show_info
    
    log "Installation process completed!"
}

# Run main function
main "$@"
