# ============================================================================
# AUTH SERVICE - Authentication Microservice
# ============================================================================

import os
import sys
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import structlog

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.config import settings
from shared.database.connection import database
from shared.utils.health import health_check

from .routes import router
from .models import User
from .services.auth_service import AuthService
from .services.jwt_service import JWTService

# Configure logging
logger = structlog.get_logger()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Auth Service...")
    await database.connect()
    logger.info("Auth Service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Auth Service...")
    await database.disconnect()
    logger.info("Auth Service shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Auth Service",
    description="Authentication and Authorization Microservice",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Include routes
app.include_router(router, prefix="/api/v1")

# Health check
@app.get("/health")
async def health_endpoint():
    """Health check endpoint"""
    is_healthy = await health_check()
    return {
        "status": "healthy" if is_healthy else "unhealthy",
        "service": "auth-service"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "auth-service",
        "version": app.version,
        "status": "running"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=settings.DEBUG
    )
