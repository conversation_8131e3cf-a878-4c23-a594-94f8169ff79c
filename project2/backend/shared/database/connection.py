# ============================================================================
# DATABASE CONNECTION - Shared Database Connection Management
# ============================================================================

import asyncio
from typing import Optional
import asyncpg
from databases import Database
import structlog
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool

from ..config import settings

logger = structlog.get_logger()

# SQLAlchemy setup
engine = create_engine(
    settings.DATABASE_URL,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_timeout=settings.DATABASE_POOL_TIMEOUT,
    pool_recycle=settings.DATABASE_POOL_RECYCLE,
    poolclass=NullPool if settings.DEBUG else None,
    echo=settings.DEBUG
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
metadata = MetaData()

# Async database connection
database = Database(settings.DATABASE_URL)

class DatabaseManager:
    """Database connection manager"""
    
    def __init__(self):
        self.database = database
        self.is_connected = False
    
    async def connect(self):
        """Connect to database"""
        try:
            if not self.is_connected:
                await self.database.connect()
                self.is_connected = True
                logger.info("Database connected successfully")
        except Exception as e:
            logger.error("Failed to connect to database", error=str(e))
            raise
    
    async def disconnect(self):
        """Disconnect from database"""
        try:
            if self.is_connected:
                await self.database.disconnect()
                self.is_connected = False
                logger.info("Database disconnected successfully")
        except Exception as e:
            logger.error("Failed to disconnect from database", error=str(e))
    
    async def execute(self, query, values=None):
        """Execute a query"""
        try:
            return await self.database.execute(query, values)
        except Exception as e:
            logger.error("Database query failed", query=str(query), error=str(e))
            raise
    
    async def fetch_all(self, query, values=None):
        """Fetch all results"""
        try:
            return await self.database.fetch_all(query, values)
        except Exception as e:
            logger.error("Database fetch_all failed", query=str(query), error=str(e))
            raise
    
    async def fetch_one(self, query, values=None):
        """Fetch one result"""
        try:
            return await self.database.fetch_one(query, values)
        except Exception as e:
            logger.error("Database fetch_one failed", query=str(query), error=str(e))
            raise

# Global database manager instance
db_manager = DatabaseManager()

# Dependency for FastAPI
async def get_database():
    """Get database connection for dependency injection"""
    return db_manager.database

def get_db():
    """Get synchronous database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Health check function
async def check_database_health() -> bool:
    """Check if database is healthy"""
    try:
        await db_manager.database.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        return False
