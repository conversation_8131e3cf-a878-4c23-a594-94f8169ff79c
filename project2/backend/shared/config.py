# ============================================================================
# SHARED CONFIGURATION - Application Settings and Environment Variables
# ============================================================================

import os
from typing import List, Optional, Any
from pydantic import BaseSettings, validator, Field
from functools import lru_cache

class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    APP_NAME: str = "Project2 API Gateway"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Microservices API Gateway with FastAPI, ML, and Kubernetes"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    
    # Server
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    WORKERS: int = Field(default=4, env="WORKERS")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Security
    SECRET_KEY: str = Field(env="SECRET_KEY")
    JWT_SECRET_KEY: str = Field(env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="JWT_ACCESS_TOKEN_EXPIRE_MINUTES")
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, env="JWT_REFRESH_TOKEN_EXPIRE_DAYS")
    
    # CORS
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"],
        env="ALLOWED_ORIGINS"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1", "0.0.0.0"],
        env="ALLOWED_HOSTS"
    )
    
    # Session
    SESSION_MAX_AGE: int = Field(default=86400, env="SESSION_MAX_AGE")  # 24 hours
    
    # Database
    DATABASE_URL: str = Field(env="DATABASE_URL")
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    DATABASE_POOL_RECYCLE: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")
    
    # Redis
    REDIS_URL: str = Field(env="REDIS_URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_MAX_CONNECTIONS: int = Field(default=20, env="REDIS_MAX_CONNECTIONS")
    
    # Celery
    CELERY_BROKER_URL: str = Field(env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(env="CELERY_RESULT_BACKEND")
    
    # MLflow
    MLFLOW_TRACKING_URI: str = Field(env="MLFLOW_TRACKING_URI")
    MLFLOW_EXPERIMENT_NAME: str = Field(default="project2", env="MLFLOW_EXPERIMENT_NAME")
    MLFLOW_ARTIFACT_ROOT: str = Field(env="MLFLOW_ARTIFACT_ROOT")
    
    # Pinecone
    PINECONE_API_KEY: str = Field(env="PINECONE_API_KEY")
    PINECONE_ENVIRONMENT: str = Field(env="PINECONE_ENVIRONMENT")
    PINECONE_INDEX_NAME: str = Field(default="project2-vectors", env="PINECONE_INDEX_NAME")
    
    # Email
    SMTP_HOST: str = Field(env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: str = Field(env="SMTP_USERNAME")
    SMTP_PASSWORD: str = Field(env="SMTP_PASSWORD")
    SMTP_USE_TLS: bool = Field(default=True, env="SMTP_USE_TLS")
    EMAIL_FROM: str = Field(env="EMAIL_FROM")
    
    # File Storage
    UPLOAD_DIR: str = Field(default="uploads", env="UPLOAD_DIR")
    MAX_FILE_SIZE: int = Field(default=10485760, env="MAX_FILE_SIZE")  # 10MB
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=["image/jpeg", "image/png", "image/gif", "application/pdf", "text/csv"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # Monitoring
    PROMETHEUS_ENABLED: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    JAEGER_ENABLED: bool = Field(default=True, env="JAEGER_ENABLED")
    JAEGER_AGENT_HOST: str = Field(default="localhost", env="JAEGER_AGENT_HOST")
    JAEGER_AGENT_PORT: int = Field(default=6831, env="JAEGER_AGENT_PORT")
    
    # Sentry
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    SENTRY_ENVIRONMENT: str = Field(default="development", env="SENTRY_ENVIRONMENT")
    
    # External APIs
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    HUGGINGFACE_API_KEY: Optional[str] = Field(default=None, env="HUGGINGFACE_API_KEY")
    
    # Kubernetes
    KUBERNETES_NAMESPACE: str = Field(default="default", env="KUBERNETES_NAMESPACE")
    KUBERNETES_SERVICE_ACCOUNT: str = Field(default="default", env="KUBERNETES_SERVICE_ACCOUNT")
    
    # Feature Flags
    FEATURE_AI_MODELS: bool = Field(default=True, env="FEATURE_AI_MODELS")
    FEATURE_ANALYTICS: bool = Field(default=True, env="FEATURE_ANALYTICS")
    FEATURE_NOTIFICATIONS: bool = Field(default=True, env="FEATURE_NOTIFICATIONS")
    FEATURE_FEDERATED_LEARNING: bool = Field(default=True, env="FEATURE_FEDERATED_LEARNING")
    
    # Cache
    CACHE_TTL: int = Field(default=300, env="CACHE_TTL")  # 5 minutes
    CACHE_MAX_SIZE: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    # Backup
    BACKUP_ENABLED: bool = Field(default=True, env="BACKUP_ENABLED")
    BACKUP_SCHEDULE: str = Field(default="0 2 * * *", env="BACKUP_SCHEDULE")  # Daily at 2 AM
    BACKUP_RETENTION_DAYS: int = Field(default=30, env="BACKUP_RETENTION_DAYS")
    
    # Health Check
    HEALTH_CHECK_INTERVAL: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")  # seconds
    HEALTH_CHECK_TIMEOUT: int = Field(default=10, env="HEALTH_CHECK_TIMEOUT")  # seconds
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("ALLOWED_FILE_TYPES", pre=True)
    def parse_file_types(cls, v):
        if isinstance(v, str):
            return [file_type.strip() for file_type in v.split(",")]
        return v
    
    @validator("DATABASE_URL")
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL is required")
        return v
    
    @validator("REDIS_URL")
    def validate_redis_url(cls, v):
        if not v:
            raise ValueError("REDIS_URL is required")
        return v
    
    @validator("SECRET_KEY")
    def validate_secret_key(cls, v):
        if not v or len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    @validator("JWT_SECRET_KEY")
    def validate_jwt_secret_key(cls, v):
        if not v or len(v) < 32:
            raise ValueError("JWT_SECRET_KEY must be at least 32 characters long")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# Database configuration
class DatabaseConfig:
    """Database configuration helper"""
    
    @staticmethod
    def get_database_url(settings: Settings) -> str:
        """Get database URL with proper formatting"""
        return settings.DATABASE_URL
    
    @staticmethod
    def get_async_database_url(settings: Settings) -> str:
        """Get async database URL"""
        url = settings.DATABASE_URL
        if url.startswith("postgresql://"):
            return url.replace("postgresql://", "postgresql+asyncpg://", 1)
        return url

# Redis configuration
class RedisConfig:
    """Redis configuration helper"""
    
    @staticmethod
    def get_redis_url(settings: Settings) -> str:
        """Get Redis URL with proper formatting"""
        return settings.REDIS_URL

# Cache settings singleton
@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()

# Global settings instance
settings = get_settings()

# Environment-specific configurations
ENVIRONMENT_CONFIGS = {
    "development": {
        "DEBUG": True,
        "LOG_LEVEL": "DEBUG",
        "WORKERS": 1,
    },
    "staging": {
        "DEBUG": False,
        "LOG_LEVEL": "INFO",
        "WORKERS": 2,
    },
    "production": {
        "DEBUG": False,
        "LOG_LEVEL": "WARNING",
        "WORKERS": 4,
    }
}

def get_environment_config() -> dict:
    """Get environment-specific configuration"""
    return ENVIRONMENT_CONFIGS.get(settings.ENVIRONMENT, ENVIRONMENT_CONFIGS["development"])
