# ============================================================================
# HEALTH CHECK UTILITIES - System Health Monitoring
# ============================================================================

import asyncio
import time
from typing import Dict, Any, List
import aioredis
import structlog
from ..database.connection import check_database_health
from ..config import settings

logger = structlog.get_logger()

class HealthChecker:
    """Health check manager for all system components"""
    
    def __init__(self):
        self.checks = {}
        self.last_check_time = None
        self.last_results = {}
    
    async def check_database(self) -> Dict[str, Any]:
        """Check database health"""
        try:
            start_time = time.time()
            is_healthy = await check_database_health()
            response_time = time.time() - start_time
            
            return {
                "status": "healthy" if is_healthy else "unhealthy",
                "response_time": response_time,
                "details": "Database connection successful" if is_healthy else "Database connection failed"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "response_time": None,
                "details": f"Database check failed: {str(e)}"
            }
    
    async def check_redis(self) -> Dict[str, Any]:
        """Check Redis health"""
        try:
            start_time = time.time()
            redis = aioredis.from_url(settings.REDIS_URL)
            await redis.ping()
            await redis.close()
            response_time = time.time() - start_time
            
            return {
                "status": "healthy",
                "response_time": response_time,
                "details": "Redis connection successful"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "response_time": None,
                "details": f"Redis check failed: {str(e)}"
            }
    
    async def check_disk_space(self) -> Dict[str, Any]:
        """Check disk space"""
        try:
            import shutil
            total, used, free = shutil.disk_usage("/")
            free_percentage = (free / total) * 100
            
            status = "healthy" if free_percentage > 10 else "unhealthy"
            
            return {
                "status": status,
                "free_space_gb": free // (1024**3),
                "free_percentage": round(free_percentage, 2),
                "details": f"Free disk space: {round(free_percentage, 2)}%"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "details": f"Disk space check failed: {str(e)}"
            }
    
    async def check_memory(self) -> Dict[str, Any]:
        """Check memory usage"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            
            status = "healthy" if memory.percent < 90 else "unhealthy"
            
            return {
                "status": status,
                "used_percentage": memory.percent,
                "available_gb": memory.available // (1024**3),
                "details": f"Memory usage: {memory.percent}%"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "details": f"Memory check failed: {str(e)}"
            }
    
    async def check_cpu(self) -> Dict[str, Any]:
        """Check CPU usage"""
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            
            status = "healthy" if cpu_percent < 80 else "unhealthy"
            
            return {
                "status": status,
                "usage_percentage": cpu_percent,
                "details": f"CPU usage: {cpu_percent}%"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "details": f"CPU check failed: {str(e)}"
            }
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks"""
        start_time = time.time()
        
        checks = {
            "database": self.check_database(),
            "redis": self.check_redis(),
            "disk": self.check_disk_space(),
            "memory": self.check_memory(),
            "cpu": self.check_cpu()
        }
        
        # Run all checks concurrently
        results = {}
        for name, check_coro in checks.items():
            try:
                results[name] = await check_coro
            except Exception as e:
                results[name] = {
                    "status": "unhealthy",
                    "details": f"Check failed: {str(e)}"
                }
        
        # Determine overall health
        overall_status = "healthy"
        unhealthy_services = []
        
        for service, result in results.items():
            if result["status"] != "healthy":
                overall_status = "unhealthy"
                unhealthy_services.append(service)
        
        total_time = time.time() - start_time
        
        health_report = {
            "status": overall_status,
            "timestamp": time.time(),
            "response_time": total_time,
            "services": results,
            "summary": {
                "total_checks": len(results),
                "healthy_checks": len([r for r in results.values() if r["status"] == "healthy"]),
                "unhealthy_services": unhealthy_services
            }
        }
        
        self.last_check_time = time.time()
        self.last_results = health_report
        
        return health_report

# Global health checker instance
health_checker = HealthChecker()

async def health_check() -> bool:
    """Simple health check function"""
    try:
        result = await health_checker.run_all_checks()
        return result["status"] == "healthy"
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return False

async def detailed_health_check() -> Dict[str, Any]:
    """Detailed health check with full report"""
    return await health_checker.run_all_checks()
