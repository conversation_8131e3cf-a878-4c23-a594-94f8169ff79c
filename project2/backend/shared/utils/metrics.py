# ============================================================================
# METRICS UTILITIES - Prometheus Metrics Setup
# ============================================================================

from prometheus_client import Counter, Histogram, Gauge, Info
import structlog

logger = structlog.get_logger()

# HTTP Metrics
http_requests_total = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code', 'service']
)

http_request_duration_seconds = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint', 'service']
)

http_requests_in_progress = Gauge(
    'http_requests_in_progress',
    'HTTP requests currently being processed',
    ['service']
)

# Database Metrics
database_connections_active = Gauge(
    'database_connections_active',
    'Active database connections',
    ['service']
)

database_query_duration_seconds = Histogram(
    'database_query_duration_seconds',
    'Database query duration in seconds',
    ['query_type', 'service']
)

database_queries_total = Counter(
    'database_queries_total',
    'Total database queries',
    ['query_type', 'status', 'service']
)

# Cache Metrics
cache_operations_total = Counter(
    'cache_operations_total',
    'Total cache operations',
    ['operation', 'status', 'service']
)

cache_hit_ratio = Gauge(
    'cache_hit_ratio',
    'Cache hit ratio',
    ['service']
)

# ML Metrics
ml_model_predictions_total = Counter(
    'ml_model_predictions_total',
    'Total ML model predictions',
    ['model_name', 'model_version', 'status']
)

ml_model_prediction_duration_seconds = Histogram(
    'ml_model_prediction_duration_seconds',
    'ML model prediction duration in seconds',
    ['model_name', 'model_version']
)

ml_training_jobs_total = Counter(
    'ml_training_jobs_total',
    'Total ML training jobs',
    ['status']
)

ml_model_accuracy = Gauge(
    'ml_model_accuracy',
    'ML model accuracy',
    ['model_name', 'model_version']
)

# Business Metrics
user_registrations_total = Counter(
    'user_registrations_total',
    'Total user registrations'
)

user_logins_total = Counter(
    'user_logins_total',
    'Total user logins',
    ['status']
)

active_users = Gauge(
    'active_users',
    'Currently active users'
)

# System Metrics
system_cpu_usage = Gauge(
    'system_cpu_usage_percent',
    'System CPU usage percentage',
    ['service']
)

system_memory_usage = Gauge(
    'system_memory_usage_bytes',
    'System memory usage in bytes',
    ['service']
)

system_disk_usage = Gauge(
    'system_disk_usage_bytes',
    'System disk usage in bytes',
    ['service', 'mount_point']
)

# Application Info
application_info = Info(
    'application_info',
    'Application information'
)

# Error Metrics
application_errors_total = Counter(
    'application_errors_total',
    'Total application errors',
    ['error_type', 'service']
)

# Task Queue Metrics
task_queue_size = Gauge(
    'task_queue_size',
    'Task queue size',
    ['queue_name']
)

task_processing_duration_seconds = Histogram(
    'task_processing_duration_seconds',
    'Task processing duration in seconds',
    ['task_type', 'status']
)

tasks_total = Counter(
    'tasks_total',
    'Total tasks processed',
    ['task_type', 'status']
)

def setup_metrics():
    """Initialize metrics with default values"""
    try:
        # Set application info
        application_info.info({
            'version': '1.0.0',
            'environment': 'development',
            'service': 'project2'
        })
        
        logger.info("Metrics initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize metrics", error=str(e))

def record_http_request(method: str, endpoint: str, status_code: int, duration: float, service: str):
    """Record HTTP request metrics"""
    http_requests_total.labels(
        method=method,
        endpoint=endpoint,
        status_code=status_code,
        service=service
    ).inc()
    
    http_request_duration_seconds.labels(
        method=method,
        endpoint=endpoint,
        service=service
    ).observe(duration)

def record_database_query(query_type: str, duration: float, status: str, service: str):
    """Record database query metrics"""
    database_queries_total.labels(
        query_type=query_type,
        status=status,
        service=service
    ).inc()
    
    database_query_duration_seconds.labels(
        query_type=query_type,
        service=service
    ).observe(duration)

def record_cache_operation(operation: str, status: str, service: str):
    """Record cache operation metrics"""
    cache_operations_total.labels(
        operation=operation,
        status=status,
        service=service
    ).inc()

def record_ml_prediction(model_name: str, model_version: str, duration: float, status: str):
    """Record ML prediction metrics"""
    ml_model_predictions_total.labels(
        model_name=model_name,
        model_version=model_version,
        status=status
    ).inc()
    
    if status == "success":
        ml_model_prediction_duration_seconds.labels(
            model_name=model_name,
            model_version=model_version
        ).observe(duration)

def record_user_registration():
    """Record user registration"""
    user_registrations_total.inc()

def record_user_login(status: str):
    """Record user login"""
    user_logins_total.labels(status=status).inc()

def update_active_users(count: int):
    """Update active users count"""
    active_users.set(count)

def record_error(error_type: str, service: str):
    """Record application error"""
    application_errors_total.labels(
        error_type=error_type,
        service=service
    ).inc()

def update_system_metrics(cpu_percent: float, memory_bytes: int, service: str):
    """Update system metrics"""
    system_cpu_usage.labels(service=service).set(cpu_percent)
    system_memory_usage.labels(service=service).set(memory_bytes)

def record_task(task_type: str, duration: float, status: str):
    """Record task processing metrics"""
    tasks_total.labels(
        task_type=task_type,
        status=status
    ).inc()
    
    task_processing_duration_seconds.labels(
        task_type=task_type,
        status=status
    ).observe(duration)
