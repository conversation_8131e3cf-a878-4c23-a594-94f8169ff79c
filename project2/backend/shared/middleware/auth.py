# ============================================================================
# AUTH MIDDLEWARE - Authentication and Authorization Middleware
# ============================================================================

import time
from typing import Optional
from fastapi import Request, Response, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
import structlog
import jwt

from ..config import settings
from ..utils.metrics import record_error

logger = structlog.get_logger()
security = HTTPBearer()

class AuthMiddleware(BaseHTTPMiddleware):
    """Authentication middleware for protecting routes"""
    
    def __init__(self, app):
        super().__init__(app)
        self.public_paths = {
            "/",
            "/health",
            "/metrics",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/auth/refresh",
            "/api/v1/auth/password-reset",
            "/api/v1/auth/password-reset/confirm"
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process request through auth middleware"""
        start_time = time.time()
        
        try:
            # Skip auth for public paths
            if self._is_public_path(request.url.path):
                response = await call_next(request)
                return response
            
            # Extract and validate token
            token = self._extract_token(request)
            if not token:
                return self._unauthorized_response("Missing authentication token")
            
            # Verify token
            user_data = self._verify_token(token)
            if not user_data:
                return self._unauthorized_response("Invalid authentication token")
            
            # Add user data to request state
            request.state.user = user_data
            request.state.user_id = user_data.get("sub")
            
            # Continue with request
            response = await call_next(request)
            
            # Log successful auth
            logger.debug(
                "Request authenticated",
                user_id=user_data.get("sub"),
                path=request.url.path,
                method=request.method,
                duration=time.time() - start_time
            )
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                "Auth middleware error",
                error=str(e),
                path=request.url.path,
                method=request.method
            )
            record_error("auth_middleware_error", "gateway")
            return self._server_error_response("Authentication error")
    
    def _is_public_path(self, path: str) -> bool:
        """Check if path is public (doesn't require auth)"""
        # Exact match
        if path in self.public_paths:
            return True
        
        # Pattern matching for dynamic paths
        public_patterns = [
            "/api/v1/auth/",
            "/static/",
            "/favicon.ico"
        ]
        
        for pattern in public_patterns:
            if path.startswith(pattern):
                return True
        
        return False
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """Extract JWT token from request"""
        # Try Authorization header first
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header.split(" ")[1]
        
        # Try cookie as fallback
        token = request.cookies.get("access_token")
        if token:
            return token
        
        return None
    
    def _verify_token(self, token: str) -> Optional[dict]:
        """Verify JWT token and return user data"""
        try:
            payload = jwt.decode(
                token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM]
            )
            
            # Check token expiration
            exp = payload.get("exp")
            if exp and exp < time.time():
                logger.warning("Token expired", exp=exp, current_time=time.time())
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning("Invalid token", error=str(e))
            return None
        except Exception as e:
            logger.error("Token verification error", error=str(e))
            return None
    
    def _unauthorized_response(self, message: str) -> Response:
        """Return unauthorized response"""
        logger.warning("Unauthorized request", message=message)
        record_error("unauthorized_request", "gateway")
        
        return Response(
            content=f'{{"error": "{message}", "status_code": 401}}',
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"Content-Type": "application/json"}
        )
    
    def _server_error_response(self, message: str) -> Response:
        """Return server error response"""
        return Response(
            content=f'{{"error": "{message}", "status_code": 500}}',
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            headers={"Content-Type": "application/json"}
        )

# Utility functions for manual auth checking
def verify_token(token: str) -> Optional[dict]:
    """Standalone token verification function"""
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        # Check expiration
        exp = payload.get("exp")
        if exp and exp < time.time():
            return None
        
        return payload
        
    except (jwt.ExpiredSignatureError, jwt.InvalidTokenError):
        return None

def create_access_token(data: dict, expires_delta: Optional[int] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = time.time() + expires_delta
    else:
        expire = time.time() + (settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60)
    
    to_encode.update({"exp": expire, "type": "access"})
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM
    )
    
    return encoded_jwt

def create_refresh_token(data: dict) -> str:
    """Create JWT refresh token"""
    to_encode = data.copy()
    expire = time.time() + (settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60)
    
    to_encode.update({"exp": expire, "type": "refresh"})
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.JWT_SECRET_KEY,
        algorithm=settings.JWT_ALGORITHM
    )
    
    return encoded_jwt
