# ============================================================================
# LOGGING MIDDLEWARE - Request/Response Logging
# ============================================================================

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

from ..utils.metrics import record_http_request, record_error

logger = structlog.get_logger()

class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses"""
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through logging middleware"""
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Start timing
        start_time = time.time()
        
        # Log request
        await self._log_request(request, request_id)
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log response
            await self._log_response(request, response, duration, request_id)
            
            # Record metrics
            service_name = self._get_service_name(request)
            record_http_request(
                method=request.method,
                endpoint=self._get_endpoint_pattern(request),
                status_code=response.status_code,
                duration=duration,
                service=service_name
            )
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # Calculate duration
            duration = time.time() - start_time
            
            # Log error
            await self._log_error(request, e, duration, request_id)
            
            # Record error metrics
            service_name = self._get_service_name(request)
            record_error("request_processing_error", service_name)
            
            # Re-raise the exception
            raise
    
    async def _log_request(self, request: Request, request_id: str):
        """Log incoming request"""
        # Get client info
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "")
        
        # Get user info if available
        user_id = getattr(request.state, 'user_id', None)
        
        # Log request details
        logger.info(
            "HTTP request started",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            path=request.url.path,
            query_params=dict(request.query_params),
            client_ip=client_ip,
            user_agent=user_agent,
            user_id=user_id,
            headers=dict(request.headers) if logger.isEnabledFor("DEBUG") else None
        )
    
    async def _log_response(self, request: Request, response: Response, duration: float, request_id: str):
        """Log response"""
        user_id = getattr(request.state, 'user_id', None)
        
        # Determine log level based on status code
        if response.status_code >= 500:
            log_level = "error"
        elif response.status_code >= 400:
            log_level = "warning"
        else:
            log_level = "info"
        
        # Log response
        getattr(logger, log_level)(
            "HTTP request completed",
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            duration_ms=round(duration * 1000, 2),
            user_id=user_id,
            response_size=response.headers.get("Content-Length"),
            content_type=response.headers.get("Content-Type")
        )
    
    async def _log_error(self, request: Request, error: Exception, duration: float, request_id: str):
        """Log request error"""
        user_id = getattr(request.state, 'user_id', None)
        
        logger.error(
            "HTTP request failed",
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            error=str(error),
            error_type=type(error).__name__,
            duration_ms=round(duration * 1000, 2),
            user_id=user_id,
            exc_info=True
        )
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        return request.client.host if request.client else "unknown"
    
    def _get_service_name(self, request: Request) -> str:
        """Get service name from request"""
        # Try to determine service from path
        path = request.url.path
        
        if path.startswith("/api/v1/auth"):
            return "auth-service"
        elif path.startswith("/api/v1/users"):
            return "user-service"
        elif path.startswith("/api/v1/ai"):
            return "ai-service"
        elif path.startswith("/api/v1/data"):
            return "data-service"
        elif path.startswith("/api/v1/notifications"):
            return "notification-service"
        else:
            return "api-gateway"
    
    def _get_endpoint_pattern(self, request: Request) -> str:
        """Get endpoint pattern for metrics (remove IDs)"""
        path = request.url.path
        
        # Replace UUIDs and numeric IDs with placeholders
        import re
        
        # Replace UUIDs
        path = re.sub(
            r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}',
            '/{id}',
            path,
            flags=re.IGNORECASE
        )
        
        # Replace numeric IDs
        path = re.sub(r'/\d+', '/{id}', path)
        
        return path

# Utility function to get request ID from current request
def get_request_id(request: Request) -> str:
    """Get request ID from request state"""
    return getattr(request.state, 'request_id', 'unknown')
