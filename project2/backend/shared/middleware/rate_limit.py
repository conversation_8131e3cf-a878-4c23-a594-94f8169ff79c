# ============================================================================
# RATE LIMIT MIDDLEWARE - Request Rate Limiting
# ============================================================================

import time
import asyncio
from typing import Dict, Optional
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
import aioredis
import structlog

from ..config import settings
from ..utils.metrics import record_error

logger = structlog.get_logger()

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware using Redis for distributed rate limiting"""
    
    def __init__(self, app):
        super().__init__(app)
        self.redis_client: Optional[aioredis.Redis] = None
        self.local_cache: Dict[str, Dict] = {}
        self.cache_cleanup_interval = 60  # seconds
        self.last_cleanup = time.time()
    
    async def dispatch(self, request: Request, call_next):
        """Process request through rate limiting"""
        try:
            # Initialize Redis client if not done
            if not self.redis_client:
                await self._init_redis()
            
            # Get client identifier
            client_id = self._get_client_id(request)
            
            # Check rate limit
            is_allowed, remaining, reset_time = await self._check_rate_limit(client_id, request)
            
            if not is_allowed:
                return self._rate_limit_exceeded_response(remaining, reset_time)
            
            # Continue with request
            response = await call_next(request)
            
            # Add rate limit headers
            response.headers["X-RateLimit-Limit"] = str(settings.RATE_LIMIT_REQUESTS)
            response.headers["X-RateLimit-Remaining"] = str(remaining)
            response.headers["X-RateLimit-Reset"] = str(int(reset_time))
            
            return response
            
        except Exception as e:
            logger.error("Rate limit middleware error", error=str(e))
            record_error("rate_limit_middleware_error", "gateway")
            # Continue without rate limiting on error
            return await call_next(request)
    
    async def _init_redis(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = aioredis.from_url(
                settings.REDIS_URL,
                max_connections=settings.REDIS_MAX_CONNECTIONS,
                retry_on_timeout=True
            )
            await self.redis_client.ping()
            logger.info("Rate limit Redis client initialized")
        except Exception as e:
            logger.error("Failed to initialize Redis for rate limiting", error=str(e))
            self.redis_client = None
    
    def _get_client_id(self, request: Request) -> str:
        """Get unique client identifier for rate limiting"""
        # Try to get user ID from auth
        user_id = getattr(request.state, 'user_id', None)
        if user_id:
            return f"user:{user_id}"
        
        # Fall back to IP address
        client_ip = request.client.host if request.client else "unknown"
        
        # Check for forwarded IP headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            client_ip = real_ip
        
        return f"ip:{client_ip}"
    
    async def _check_rate_limit(self, client_id: str, request: Request) -> tuple[bool, int, float]:
        """Check if request is within rate limit"""
        current_time = time.time()
        window_start = current_time - settings.RATE_LIMIT_WINDOW
        
        # Try Redis first
        if self.redis_client:
            try:
                return await self._check_rate_limit_redis(client_id, current_time, window_start)
            except Exception as e:
                logger.warning("Redis rate limit check failed, falling back to local", error=str(e))
        
        # Fall back to local cache
        return self._check_rate_limit_local(client_id, current_time, window_start)
    
    async def _check_rate_limit_redis(self, client_id: str, current_time: float, window_start: float) -> tuple[bool, int, float]:
        """Check rate limit using Redis"""
        key = f"rate_limit:{client_id}"
        
        # Use Redis pipeline for atomic operations
        pipe = self.redis_client.pipeline()
        
        # Remove old entries
        pipe.zremrangebyscore(key, 0, window_start)
        
        # Count current requests
        pipe.zcard(key)
        
        # Add current request
        pipe.zadd(key, {str(current_time): current_time})
        
        # Set expiration
        pipe.expire(key, settings.RATE_LIMIT_WINDOW)
        
        results = await pipe.execute()
        current_requests = results[1]
        
        remaining = max(0, settings.RATE_LIMIT_REQUESTS - current_requests - 1)
        reset_time = current_time + settings.RATE_LIMIT_WINDOW
        
        is_allowed = current_requests < settings.RATE_LIMIT_REQUESTS
        
        return is_allowed, remaining, reset_time
    
    def _check_rate_limit_local(self, client_id: str, current_time: float, window_start: float) -> tuple[bool, int, float]:
        """Check rate limit using local cache"""
        # Clean up old entries periodically
        if current_time - self.last_cleanup > self.cache_cleanup_interval:
            self._cleanup_local_cache(window_start)
            self.last_cleanup = current_time
        
        # Get or create client entry
        if client_id not in self.local_cache:
            self.local_cache[client_id] = {"requests": [], "reset_time": current_time + settings.RATE_LIMIT_WINDOW}
        
        client_data = self.local_cache[client_id]
        
        # Remove old requests
        client_data["requests"] = [req_time for req_time in client_data["requests"] if req_time > window_start]
        
        # Check if limit exceeded
        current_requests = len(client_data["requests"])
        is_allowed = current_requests < settings.RATE_LIMIT_REQUESTS
        
        if is_allowed:
            client_data["requests"].append(current_time)
        
        remaining = max(0, settings.RATE_LIMIT_REQUESTS - current_requests - (1 if is_allowed else 0))
        reset_time = current_time + settings.RATE_LIMIT_WINDOW
        
        return is_allowed, remaining, reset_time
    
    def _cleanup_local_cache(self, window_start: float):
        """Clean up old entries from local cache"""
        clients_to_remove = []
        
        for client_id, client_data in self.local_cache.items():
            # Remove old requests
            client_data["requests"] = [req_time for req_time in client_data["requests"] if req_time > window_start]
            
            # Remove client if no recent requests
            if not client_data["requests"]:
                clients_to_remove.append(client_id)
        
        for client_id in clients_to_remove:
            del self.local_cache[client_id]
    
    def _rate_limit_exceeded_response(self, remaining: int, reset_time: float) -> Response:
        """Return rate limit exceeded response"""
        logger.warning("Rate limit exceeded", remaining=remaining, reset_time=reset_time)
        record_error("rate_limit_exceeded", "gateway")
        
        return Response(
            content='{"error": "Rate limit exceeded", "status_code": 429}',
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            headers={
                "Content-Type": "application/json",
                "X-RateLimit-Limit": str(settings.RATE_LIMIT_REQUESTS),
                "X-RateLimit-Remaining": str(remaining),
                "X-RateLimit-Reset": str(int(reset_time)),
                "Retry-After": str(settings.RATE_LIMIT_WINDOW)
            }
        )
