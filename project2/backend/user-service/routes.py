# ============================================================================
# USER ROUTES - User Management API Endpoints
# ============================================================================

from fastapi import APIRouter

router = APIRouter()

@router.get("/")
async def get_users():
    """Get all users"""
    return {"message": "User service is running"}

@router.get("/{user_id}")
async def get_user(user_id: str):
    """Get user by ID"""
    return {"user_id": user_id, "message": "User service is running"}
