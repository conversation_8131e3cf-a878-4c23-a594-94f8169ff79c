# ============================================================================
# SIMPLE API GATEWAY - Working Version
# ============================================================================

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import time

# Create FastAPI application
app = FastAPI(
    title="Project2 API Gateway",
    description="Microservices API Gateway - Simple Version",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "api-gateway",
        "version": "1.0.0"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Project2 API Gateway",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "api": "/api/v1"
        }
    }

# API v1 routes
@app.get("/api/v1")
async def api_v1():
    """API v1 info"""
    return {
        "version": "1.0.0",
        "services": [
            "auth-service",
            "user-service", 
            "ai-service",
            "data-service",
            "notification-service"
        ],
        "status": "operational"
    }

# Mock auth endpoint
@app.post("/api/v1/auth/login")
async def mock_login():
    """Mock login endpoint"""
    return {
        "access_token": "mock-token",
        "token_type": "bearer",
        "user": {
            "id": "admin-id",
            "email": "<EMAIL>",
            "username": "admin",
            "role": "admin"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
