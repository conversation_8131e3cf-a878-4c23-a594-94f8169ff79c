# ============================================================================
# BACKEND ENVIRONMENT VARIABLES - Configuration Template
# ============================================================================

# Application
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000
WORKERS=4
LOG_LEVEL=INFO

# Security
SECRET_KEY=your-super-secret-key-here-at-least-32-characters-long
JWT_SECRET_KEY=your-jwt-secret-key-here-at-least-32-characters-long
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Session
SESSION_MAX_AGE=86400

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/project2
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20

# Celery
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# MLflow
MLFLOW_TRACKING_URI=http://localhost:5000
MLFLOW_EXPERIMENT_NAME=project2
MLFLOW_ARTIFACT_ROOT=s3://mlflow-artifacts

# Pinecone
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=us-west1-gcp
PINECONE_INDEX_NAME=project2-vectors

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true
EMAIL_FROM=<EMAIL>

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/csv

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring
PROMETHEUS_ENABLED=true
JAEGER_ENABLED=true
JAEGER_AGENT_HOST=localhost
JAEGER_AGENT_PORT=6831

# Sentry
SENTRY_DSN=
SENTRY_ENVIRONMENT=development

# External APIs
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
HUGGINGFACE_API_KEY=

# Kubernetes
KUBERNETES_NAMESPACE=default
KUBERNETES_SERVICE_ACCOUNT=default

# Feature Flags
FEATURE_AI_MODELS=true
FEATURE_ANALYTICS=true
FEATURE_NOTIFICATIONS=true
FEATURE_FEDERATED_LEARNING=true

# Cache
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Health Check
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
