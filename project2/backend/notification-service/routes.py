# ============================================================================
# NOTIFICATION ROUTES - Notification API Endpoints
# ============================================================================

from fastapi import APIRouter

router = APIRouter()

@router.get("/")
async def get_notifications():
    """Get all notifications"""
    return {"message": "Notification service is running"}

@router.post("/send")
async def send_notification():
    """Send notification"""
    return {"message": "Notification sent"}
