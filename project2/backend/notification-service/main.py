# ============================================================================
# NOTIFICATION SERVICE - Notification Management Microservice
# ============================================================================

import os
import sys
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
import structlog

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.config import settings
from shared.database.connection import database
from shared.utils.health import health_check

# Configure logging
logger = structlog.get_logger()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Notification Service...")
    await database.connect()
    logger.info("Notification Service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Notification Service...")
    await database.disconnect()
    logger.info("Notification Service shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Notification Service",
    description="Notification Management Microservice",
    version="1.0.0",
    lifespan=lifespan
)

# Health check
@app.get("/health")
async def health_endpoint():
    """Health check endpoint"""
    is_healthy = await health_check()
    return {
        "status": "healthy" if is_healthy else "unhealthy",
        "service": "notification-service"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "notification-service",
        "version": app.version,
        "status": "running"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8005,
        reload=settings.DEBUG
    )
