# ============================================================================
# BACKEND DEPENDENCIES - Python Requirements
# ============================================================================

# FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis
redis==5.0.1
aioredis==2.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# Validation & Serialization
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Background Tasks
celery==5.3.4
flower==2.0.1

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# ML & AI
mlflow==2.8.1
pinecone-client==2.2.4
scikit-learn==1.3.2
numpy==1.25.2
pandas==2.1.4

# Vector Database
pinecone-client==2.2.4
chromadb==0.4.18

# Message Queue
pika==1.3.2
kombu==5.3.4

# Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Utilities
python-slugify==8.0.1
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7

# File Processing
python-magic==0.4.27
pillow==10.1.0

# Networking
websockets==12.0

# Health Checks
healthcheck==1.3.3

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8

# Performance
orjson==3.9.10
ujson==5.8.0

# Caching
python-memcached==1.62
diskcache==5.6.3

# Rate Limiting
slowapi==0.1.9

# CORS
fastapi-cors==0.0.6

# Middleware
starlette-prometheus==0.9.0

# Database Migrations
yoyo-migrations==8.2.0

# Environment
environs==10.0.0

# Cryptography
cryptography==41.0.8

# JWT
pyjwt==2.8.0

# OAuth
authlib==1.2.1

# File Upload
python-multipart==0.0.6
aiofiles==23.2.1

# Image Processing
opencv-python==********

# Data Processing
openpyxl==3.1.2
xlsxwriter==3.1.9

# API Documentation
fastapi-users==12.1.2
fastapi-pagination==0.12.13

# Async Database
databases[postgresql]==0.8.0
asyncio-mqtt==0.16.1

# Distributed Computing
dask==2023.11.0
distributed==2023.11.0

# Time Series
influxdb-client==1.38.0

# Graph Database
neo4j==5.14.1

# Search Engine
elasticsearch==8.11.0

# Message Streaming
kafka-python==2.0.2
aiokafka==0.9.0

# Workflow
prefect==2.14.11
airflow==2.7.3

# Feature Store
feast==0.35.0

# Model Serving
bentoml==1.1.10
seldon-core==1.17.1

# Experiment Tracking
wandb==0.16.0
neptune==1.8.5

# Data Validation
great-expectations==0.18.5
pandera==0.17.2

# API Gateway
kong==0.1.0

# Service Mesh
istio==1.19.4

# Container Runtime
docker==6.1.3
kubernetes==28.1.0
