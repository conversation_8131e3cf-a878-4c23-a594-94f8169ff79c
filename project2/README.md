# 🚀 Project2 - AI Microservices Platform

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![Node.js 18+](https://img.shields.io/badge/node-18+-green.svg)](https://nodejs.org/)
[![Docker](https://img.shields.io/badge/docker-required-blue.svg)](https://www.docker.com/)

A complete, production-ready microservices platform with AI/ML capabilities, built with modern technologies and best practices.

## 🌟 Features

### 🎨 **Modern Frontend**
- **React 18** with TypeScript and Vite
- **Tailwind CSS** for styling
- **Zustand** for state management
- **React Query** for data fetching
- **Framer Motion** for animations

### ⚡ **High-Performance Backend**
- **FastAPI** with async/await support
- **PostgreSQL** with async drivers
- **Redis** for caching and sessions
- **Microservices architecture** with Domain-Driven Design
- **JWT authentication** with refresh tokens

### 🤖 **AI & Machine Learning**
- **MLflow** for experiment tracking
- **Pinecone** vector database
- **Federated learning** capabilities
- **Model serving** and deployment
- **Real-time predictions**

### ☸️ **Cloud-Native & DevOps**
- **Kubernetes** manifests with autoscaling
- **Docker** containerization
- **Prometheus + Grafana** monitoring
- **Jaeger** distributed tracing
- **ELK Stack** for logging

### 🔒 **Enterprise Security**
- **OAuth 2.0** authentication
- **RBAC** (Role-Based Access Control)
- **Rate limiting** and DDoS protection
- **SSL/TLS** encryption
- **Security headers** and CORS

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │  Microservices  │
│   React + TS    │◄──►│    FastAPI      │◄──►│   Auth, User,   │
│   Tailwind CSS  │    │   Load Balancer │    │   AI, Data,     │
└─────────────────┘    └─────────────────┘    │  Notification   │
                                              └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Databases     │    │   Monitoring    │    │   ML Platform   │
│  PostgreSQL     │    │  Prometheus     │    │    MLflow       │
│    Redis        │    │   Grafana       │    │   Pinecone      │
│   RabbitMQ      │    │   Jaeger        │    │  Fed. Learning  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- **Docker** and **Docker Compose**
- **Node.js** 18+ and **npm**
- **Python** 3.11+ and **pip**
- **Git**

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/project2.git
cd project2
```

### 2. One-Command Installation

```bash
make install
```

This will:
- ✅ Check system requirements
- ✅ Setup environment variables
- ✅ Install all dependencies
- ✅ Build Docker images
- ✅ Initialize databases
- ✅ Start all services
- ✅ Run health checks

### 3. Access the Platform

| Service | URL | Credentials |
|---------|-----|-------------|
| **Frontend** | http://localhost:5173 | - |
| **API Gateway** | http://localhost:8000 | - |
| **API Documentation** | http://localhost:8000/docs | - |
| **Grafana** | http://localhost:3000 | admin/admin123 |
| **Prometheus** | http://localhost:9090 | - |
| **Jaeger** | http://localhost:16686 | - |
| **MLflow** | http://localhost:5000 | - |
| **RabbitMQ** | http://localhost:15672 | project2_user/project2_password |

### 4. Default Admin User

```
Email: <EMAIL>
Password: admin123
```

## 📋 Available Commands

```bash
# Development
make dev              # Start development environment
make start            # Start all services
make stop             # Stop all services
make restart          # Restart all services

# Monitoring
make logs             # View all logs
make health           # Check service health
make monitor          # Open monitoring dashboards

# Database
make db-init          # Initialize database
make db-reset         # Reset database (⚠️ deletes data)
make backup           # Create database backup

# Testing
make test             # Run all tests
make test-integration # Run integration tests
make test-e2e         # Run end-to-end tests

# Utilities
make clean            # Clean Docker resources
make help             # Show all commands
```

## 🏢 Microservices

| Service | Port | Description |
|---------|------|-------------|
| **API Gateway** | 8000 | Main entry point, routing, auth |
| **Auth Service** | 8001 | Authentication & authorization |
| **User Service** | 8002 | User management & profiles |
| **AI Service** | 8003 | ML models & predictions |
| **Data Service** | 8004 | Data processing & management |
| **Notification Service** | 8005 | Email, SMS, push notifications |

## 🗄️ Database Schema

The platform uses PostgreSQL with separate schemas for each microservice:

- `auth` - Users, sessions, tokens
- `users` - User profiles and preferences
- `ai_ml` - ML models, training jobs
- `data_management` - Datasets, processing jobs
- `notifications` - Notifications and preferences
- `analytics` - Events and metrics

## 🔧 Configuration

### Environment Variables

Copy and customize the environment files:

```bash
# Backend
cp backend/.env.example backend/.env

# Frontend
cp frontend/.env.example frontend/.env
```

### Key Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://...` |
| `REDIS_URL` | Redis connection string | `redis://localhost:6379` |
| `JWT_SECRET_KEY` | JWT signing key | Auto-generated |
| `PINECONE_API_KEY` | Pinecone API key | Required for AI features |
| `MLFLOW_TRACKING_URI` | MLflow server URL | `http://localhost:5000` |

## 🧪 Testing

### Unit Tests
```bash
make test
```

### Integration Tests
```bash
make test-integration
```

### End-to-End Tests
```bash
make test-e2e
```

### Load Testing
```bash
cd tests/performance
python load_test.py
```

## 📊 Monitoring & Observability

### Metrics (Prometheus)
- HTTP request metrics
- Database performance
- Cache hit rates
- ML model performance
- Business metrics

### Dashboards (Grafana)
- System overview
- Service health
- Database metrics
- ML model performance
- Business KPIs

### Tracing (Jaeger)
- Distributed request tracing
- Performance bottlenecks
- Error tracking
- Service dependencies

### Logging (ELK Stack)
- Centralized logging
- Log aggregation
- Search and analysis
- Alert management

## 🚀 Deployment

### Development
```bash
make dev
```

### Staging
```bash
make deploy-staging
```

### Production
```bash
make deploy-production
```

### Kubernetes
```bash
kubectl apply -f kubernetes/
```

## 🔒 Security

- **Authentication**: JWT with refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Rate Limiting**: Per-user and per-IP limits
- **CORS**: Configurable cross-origin policies
- **HTTPS**: SSL/TLS encryption
- **Input Validation**: Comprehensive data validation
- **SQL Injection**: Parameterized queries
- **XSS Protection**: Content Security Policy

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-username/project2/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/project2/discussions)

## 🙏 Acknowledgments

- **FastAPI** for the amazing Python web framework
- **React** team for the excellent frontend library
- **Docker** for containerization
- **Kubernetes** for orchestration
- **Prometheus** and **Grafana** for monitoring
- **MLflow** for ML lifecycle management

---

**Built with ❤️ by the Project2 Team**
